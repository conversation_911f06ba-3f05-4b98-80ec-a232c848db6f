FROM jnbyharbor.jnby.com/base-images/baseimagejdk8:v1.6

MAINTAINER box-group

RUN mkdir -p /jic-backend-box/jic-backend-box-web

WORKDIR /jic-backend-box/jic-backend-box-web

EXPOSE 9101

# 参考安装探针的第4步，获得所在地域的探针下载地址，请注意公网地址和VPC地址的区别。
RUN wget "http://arms-apm-cn-hangzhou.oss-cn-hangzhou.aliyuncs.com/AliyunJavaAgent.zip" -O AliyunJavaAgent.zip
# 解压探针。
RUN unzip AliyunJavaAgent.zip -d /jic-backend-box/jic-backend-box-web
# 参考安装探针的第6步，将LicenseKey和AppName写入环境变量中。
ENV arms_licenseKey=a56pmv5zxu@34205b1db88c0cc
ENV arms_appName=jic-backend-box-web

ADD ./target/jic-backend-box-web.jar ./jic-backend-box-web.jar

#COPY --from=hengyunabc/arthas:latest /opt/arthas /opt/arthas

ENV TZ='Asia/Shanghai'

ENTRYPOINT ["java","-javaagent:/jic-backend-box/jic-backend-box-web/AliyunJavaAgent/aliyun-java-agent.jar","-Darms.licenseKey=a56pmv5zxu@34205b1db88c0cc","-Darms.appName=jic-backend-box-web","-Dreactor.netty.pool.leasingStrategy=lifo","-Xmx2g", "-Xms2g","-XX:NewRatio=3","-Xss512k", "-Xmn2g","-XX:SurvivorRatio=2", "-XX:+UseParallelGC","-jar", "jic-backend-box-web.jar"]
#ENTRYPOINT ["java","-Dreactor.netty.pool.leasingStrategy=lifo","-Xmx2g", "-Xms2g","-XX:NewRatio=3","-Xss512k", "-Xmn2g","-XX:SurvivorRatio=2", "-XX:+UseParallelGC","-jar", "jic-backend-box-web.jar"]

CMD ["--spring.profiles.active=prod"]
