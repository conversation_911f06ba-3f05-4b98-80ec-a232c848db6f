<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.bojun.mapper.NcCustomerMapper">

    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.bojun.model.NcCustomer">
            <id property="id" column="ID" jdbcType="DECIMAL"/>
            <result property="ncAccountId" column="NC_ACCOUNT_ID" jdbcType="DECIMAL"/>
            <result property="custcode" column="CUSTCODE" jdbcType="VARCHAR"/>
            <result property="custname" column="CUSTNAME" jdbcType="VARCHAR"/>
            <result property="adClientId" column="AD_CLIENT_ID" jdbcType="DECIMAL"/>
            <result property="adOrgId" column="AD_ORG_ID" jdbcType="DECIMAL"/>
            <result property="ownerid" column="OWNERID" jdbcType="DECIMAL"/>
            <result property="modifierid" column="MODIFIERID" jdbcType="DECIMAL"/>
            <result property="creationdate" column="CREATIONDATE" jdbcType="TIMESTAMP"/>
            <result property="modifieddate" column="MODIFIEDDATE" jdbcType="TIMESTAMP"/>
            <result property="isactive" column="ISACTIVE" jdbcType="CHAR"/>
            <result property="isnew" column="ISNEW" jdbcType="CHAR"/>
            <result property="ksmc" column="KSMC" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,NC_ACCOUNT_ID,CUSTCODE,
        CUSTNAME,AD_CLIENT_ID,AD_ORG_ID,
        OWNERID,MODIFIERID,CREATIONDATE,
        MODIFIEDDATE,ISACTIVE,ISNEW,
        KSMC
    </sql>

    <select id="selectLatestByCustomerName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from NC_CUSTOMER
        where CUSTNAME = #{ncCustomerName} and NC_ACCOUNT_ID = #{ncAccountId} and ISACTIVE = 'Y' and rownum = 1 order by ID desc
    </select>


</mapper>
