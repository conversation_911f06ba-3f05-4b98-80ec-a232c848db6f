<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.bojun.mapper.CCustomerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.dto.bojun.CustomerDto">
        <result column="id" property="id" />
        <result column="ISACTIVE" property="isactive" />
        <result column="NAME" property="name" />
        <result column="ENTERDATE" property="enterdate" />
        <result column="ISSTOP" property="isstop" />

        <result column="CONTACTER" property="contacter" />
        <result column="PHONE" property="phone" />
        <result column="ADDRESS" property="address" />
        <result column="POST" property="post" />
        <result column="EMAIL" property="email" />
        <result column="BIGAREAMNG_ID" property="areamngId" />
        <result column="C_CITY_ID" property="cCityId" />

        <result column="CODE" property="code" />
        <result column="C_AREA_ID" property="cAreaId" />
        <result column="C_ARCBRAND_ID" property="cArcbrandId" />
        <result column="CLOP_STORE" property="clopStore" />
    </resultMap>

    <sql id="BaseSql">
        id,ISACTIVE,NAME,ENTERDATE,ISSTOP,CONTACTER,PHONE,ADDRESS,POST,EMAIL,AREAMNG_ID,C_CITY_ID,CODE,C_AREA_ID,C_ARCBRAND_ID,BIGAREAMNG_ID,CLOP_STORE
    </sql>
    <select id="insertIntoStoreChildTableAc" statementType="CALLABLE" parameterType="java.util.Map" resultType="java.lang.Void">
        { call C_STORE_AC(#{storeId,mode=IN,jdbcType=INTEGER}) }
    </select>
    <select id="insertIntoStoreChildTableAm" statementType="CALLABLE" parameterType="java.util.Map" resultType="java.lang.Void">
        { call C_STORE_AM(#{storeId,mode=IN,jdbcType=INTEGER}) }
    </select>
    <!--经销商向子表插入数据存储过程 statementType 声明指向的是什么类型，其中CALLABLE是执行存储过程和函数的-->
    <select id="insertIntoCustomerChildTableAc" statementType="CALLABLE" parameterType="java.util.Map" resultType="java.lang.Void">
        { call C_CUSTOMER_AC(#{customerId,mode=IN,jdbcType=INTEGER}) }
    </select>

    <select id="insertIntoCustomerChildTableAm" statementType="CALLABLE" parameterType="java.util.Map" resultType="java.lang.Void">
        { call C_CUSTOMER_AM(#{customerId,mode=IN,jdbcType=INTEGER}) }
    </select>

    <select id="getSequencesByTableName" resultType="java.lang.Long">
        select get_sequences(#{tableName})  FROM dual
    </select>

    <select id="getSequences" resultType="java.lang.Long" parameterType="java.lang.String">
        select ${seqName}.nextval from dual
    </select>



    <select id="selectByCodes"  resultMap="BaseResultMap">
        select <include refid="BaseSql"></include> from c_customer where code in
                                      <foreach collection="codes" open="(" close=")" item="item" separator=",">
                                          #{item}
                                      </foreach>
                                        and ISACTIVE = 'Y'
    </select>


    <select id="selectByCodesAll"  resultMap="BaseResultMap">
        select <include refid="BaseSql"></include> from c_customer where code in
        <foreach collection="codes" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="getCustomreById"  resultMap="BaseResultMap">
        select <include refid="BaseSql"></include> from c_customer where id=#{id}
    </select>

</mapper>