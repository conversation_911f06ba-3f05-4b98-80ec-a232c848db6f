<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.bojun.mapper.CStoreMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap2" type="com.jnby.dto.bojun.CstoreSumDto">
        <result column="sumNum" property="sumNum" />
        <result column="C_CUSTOMER_ID" property="customerId" />
    </resultMap>


    <select id="selectCountStoreByCustomerIds" resultMap="BaseResultMap2">
        select C_CUSTOMER_ID , count(*) as "sumNum" from c_store where ISACTIVE = 'Y' and C_CUSTOMER_ID in
                                                                                          <foreach collection="customerIds" open="(" close=")" item="item" separator=",">
                                                                                              #{item}
                                                                                          </foreach>
                                 and IS_TM != 'Y' and id != '25277' and name not like '%道具%'
        And ISACTIVE = 'Y'  and  C_STOREATTRIB37_ID ='Y' and  ISRETAIL = 'Y'
                                                                                            group by C_CUSTOMER_ID
    </select>
    <select id="selectByParams" resultType="com.jnby.dto.StoreCustomerResp">
        select cs.code as "storeCode", cs.name as "storeName" , cc.name as "customerName" , cs.C_AREA_ID as  "storeArea" ,
               cs.C_ARCBRAND_ID as "brandId" , cs.ISACTIVE as "status",cs.ISRETAIL as "isretail",cs.default01 as "default01", cs.opendate as "opendate",cs.default07 as "default07",
               cs.C_STOREATTRIB37_ID as "cStoreattrib37Id"

        from c_store cs left join c_customer cc on cs.c_customer_id = cc.id
        where cs.c_customer_id != 176  and cs.IS_TM != 'Y' and cs.id != '25277' and cs.name not like '%道具%'

        <if test='status !=  null and status  == "Y"  '>
            and   cs.ISACTIVE = #{status}  and  cs.C_STOREATTRIB37_ID = #{status} and  cs.ISRETAIL = #{status}
        </if>
        <if test='status !=  null  and status == "N"  '>
            and  ( cs.ISACTIVE = #{status} or cs.C_STOREATTRIB37_ID = #{status} or cs.ISRETAIL = #{status} )
        </if>

      <if test="area !=  null and area != '' ">
        and   cs.C_AREA_ID = #{area}
      </if>
      <if test="brandIds != null and brandIds.size() > 0 ">
          and  cs.C_ARCBRAND_ID in
          <foreach collection="brandIds" separator="," item="item" close=")" open="(">
              #{item}
          </foreach>
      </if>
      <if test="search !=  null and search != '' ">
        and   (cs.code = #{search}  or cs.name like concat(concat('%',#{search}),'%') or cc.name like concat(concat('%',#{search}),'%'))
      </if>

    </select>

    <select id="listStoreByStoreId" resultType="com.jnby.dto.bojun.CstoreBrandDto">
        SELECT a.id AS storeId, a.NAME AS storeName, a.c_unionstore_id AS unionStoreId, b.BRANDNAME AS brandName, a.DEFAULT05 AS outlets FROM (
            SELECT * FROM C_STORE WHERE ISACTIVE ='Y' AND id IN
                <foreach collection="storeIds" separator="," item="storeId" close=")" open="(">
                    #{storeId}
                </foreach>
        ) a LEFT JOIN C_ARCBRAND b ON a.C_ARCBRAND_id=b.ID
    </select>

    <select id="listStoreAndBrandNameByUnionStoreId" resultType="com.jnby.dto.bojun.CstoreBrandDto">
        SELECT a.id AS storeId, a.NAME AS storeName, a.c_unionstore_id AS unionStoreId, b.BRANDNAME AS brandName FROM (
            SELECT * FROM C_STORE WHERE
                                      c_unionstore_id = #{unionStoreId}
                                    AND id !=  #{unionStoreId}
                                    AND ISACTIVE ='Y'
        ) a LEFT JOIN C_ARCBRAND b ON a.C_ARCBRAND_id=b.ID
        WHERE b.BRANDNAME IN ('JNBY','LESS','CROQUIS','蓬马','童装','JNBYHOME')
        ORDER BY
            CASE b.BRANDNAME
                WHEN 'JNBY' THEN 1
                WHEN 'LESS' THEN 2
                WHEN 'CROQUIS' THEN 3
                WHEN '蓬马' THEN 4
                WHEN '童装' THEN 5
                WHEN 'JNBYHOME' THEN 6
                ELSE 7
                END
    </select>
</mapper>