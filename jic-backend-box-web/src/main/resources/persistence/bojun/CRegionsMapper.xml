<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.bojun.mapper.CRegionsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.bojun.model.CRegions">
        <result column="id" property="id" />
        <result column="name" property="name" />
        <result column="p_id" property="pId" />
        <result column="code" property="code" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <select id="getProvince" resultMap="BaseResultMap">
        select id,name,'' as p_id,code from c_province where name = #{name,jdbcType=VARCHAR}  and ISACTIVE = 'Y'
    </select>

    <select id="getCity" resultMap="BaseResultMap">
        select id,name,c_province_id as p_id from c_city where c_province_id = #{pId,jdbcType=DECIMAL} and name = #{name,jdbcType=VARCHAR}  and ISACTIVE = 'Y'
    </select>

    <select id="getDistrict" resultMap="BaseResultMap">
        select id,name,c_city_id as p_id from c_district where c_city_id = #{pId,jdbcType=DECIMAL} and name = #{name,jdbcType=VARCHAR} and ISACTIVE = 'Y'
    </select>
    <select id="getProvinces" resultMap="BaseResultMap">
        select id,name,'' as p_id,code from c_province1 where ISACTIVE = 'Y'
    </select>
    <select id="getCities" resultMap="BaseResultMap">
        select id,name,c_province_id as p_id from c_city where c_province_id = #{pId,jdbcType=DECIMAL}  and ISACTIVE = 'Y'
    </select>
    <select id="getDistricts" resultMap="BaseResultMap">
        select id,name,c_city_id as p_id from c_district where c_city_id = #{pId,jdbcType=DECIMAL} and ISACTIVE = 'Y'
    </select>
    <select id="getProvinceById" resultMap="BaseResultMap">
        select        id,name,'' as p_id,code from  c_province where id = #{id}
    </select>
    <select id="getCityById" resultMap="BaseResultMap">
        select id,name,c_province_id as p_id from c_city where id = #{id}  and ISACTIVE = 'Y'
    </select>
    <select id="getDistrictById" resultMap="BaseResultMap">
        select id,name,c_city_id as p_id from c_district where id = #{id} and ISACTIVE = 'Y'
    </select>
    <select id="getAllCity" resultMap="BaseResultMap">
        select id,name,c_province_id as p_id from c_city1 where  ISACTIVE = 'Y'
    </select>
    <select id="getAllDistricts" resultMap="BaseResultMap">
        select id,name,c_city_id as p_id from c_district1 where  ISACTIVE = 'Y'
    </select>
</mapper>