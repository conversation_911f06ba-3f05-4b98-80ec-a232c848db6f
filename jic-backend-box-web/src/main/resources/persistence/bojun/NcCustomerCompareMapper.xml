<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.bojun.mapper.NcCustomerCompareMapper">

    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.bojun.model.NcCustomerCompare">
        <id property="id" column="ID" jdbcType="DECIMAL"/>
        <result property="ncAccountId" column="NC_ACCOUNT_ID" jdbcType="DECIMAL"/>
        <result property="ncCustomerId" column="NC_CUSTOMER_ID" jdbcType="DECIMAL"/>
        <result property="cCustomerId" column="C_CUSTOMER_ID" jdbcType="DECIMAL"/>
        <result property="adClientId" column="AD_CLIENT_ID" jdbcType="DECIMAL"/>
        <result property="adOrgId" column="AD_ORG_ID" jdbcType="DECIMAL"/>
        <result property="ownerid" column="OWNERID" jdbcType="DECIMAL"/>
        <result property="modifierid" column="MODIFIERID" jdbcType="DECIMAL"/>
        <result property="creationdate" column="CREATIONDATE" jdbcType="TIMESTAMP"/>
        <result property="modifieddate" column="MODIFIEDDATE" jdbcType="TIMESTAMP"/>
        <result property="isactive" column="ISACTIVE" jdbcType="CHAR"/>
        <result property="cInvocegroupmodeId" column="C_INVOCEGROUPMODE_ID" jdbcType="DECIMAL"/>
        <result property="ks" column="KS" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,NC_ACCOUNT_ID,NC_CUSTOMER_ID,
        C_CUSTOMER_ID,AD_CLIENT_ID,AD_ORG_ID,
        OWNERID,MODIFIERID,CREATIONDATE,
        MODIFIEDDATE,ISACTIVE,C_INVOCEGROUPMODE_ID,
        KS
    </sql>

    <select id="selectByCCustomerId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from NC_CUSTOMER_COMPARE
        where C_CUSTOMER_ID = #{cCustomerId} and ISACTIVE = 'Y'
    </select>

    <insert id="insert" keyColumn="ID" keyProperty="id"
            parameterType="com.jnby.infrastructure.bojun.model.NcCustomerCompare"
            useGeneratedKeys="true">
        <selectKey resultType="int" keyProperty="id" order="BEFORE">
            SELECT get_sequences('NC_CUSTOMER_COMPARE') FROM dual
        </selectKey>

        insert into NC_CUSTOMER_COMPARE
        ( ID, NC_ACCOUNT_ID, NC_CUSTOMER_ID
        , C_CUSTOMER_ID, AD_CLIENT_ID, AD_ORG_ID
        , OWNERID, MODIFIERID, CREATIONDATE
        , MODIFIEDDATE, ISACTIVE, C_INVOCEGROUPMODE_ID
        , KS)
        values ( ${id}, #{ncAccountId,jdbcType=DECIMAL}, #{ncCustomerId,jdbcType=DECIMAL}
               , #{cCustomerId,jdbcType=DECIMAL}, #{adClientId,jdbcType=DECIMAL}, #{adOrgId,jdbcType=DECIMAL}
               , #{ownerid,jdbcType=DECIMAL}, #{modifierid,jdbcType=DECIMAL}, #{creationdate,jdbcType=TIMESTAMP}
               , #{modifieddate,jdbcType=TIMESTAMP}, #{isactive,jdbcType=CHAR}, #{cInvocegroupmodeId,jdbcType=DECIMAL}
               , #{ks,jdbcType=VARCHAR})
    </insert>

    <update id="updateCustomerEntity">
        update NC_CUSTOMER_COMPARE
        set NC_CUSTOMER_ID = #{ncCustomerId,jdbcType=DECIMAL},
            MODIFIEDDATE   = sysdate,
            KS             = #{customerName,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=DECIMAL}
    </update>
</mapper>
