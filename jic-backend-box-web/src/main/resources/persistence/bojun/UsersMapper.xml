<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.bojun.mapper.UsersMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.bojun.model.UsersVo">
        <id column="ID" property="id" />
        <result column="AD_CLIENT_ID" property="adClientId" />
        <result column="AD_ORG_ID" property="adOrgId" />
        <result column="ISACTIVE" property="isactive" />
        <result column="SUPERVISOR_ID" property="supervisorId" />
        <result column="PA_GOALPRIVATE_ID" property="paGoalprivateId" />
        <result column="C_BPARTNER_ID" property="cBpartnerId" />
        <result column="PROCESSING" property="processing" />
        <result column="C_BPARTNER_LOCATION_ID" property="cBpartnerLocationId" />
        <result column="C_GREETING_ID" property="cGreetingId" />
        <result column="LASTCONTACT" property="lastcontact" />
        <result column="BIRTHDAY" property="birthday" />
        <result column="AD_ORGTRX_ID" property="adOrgtrxId" />
        <result column="ISLDAPAUTHORIZED" property="isldapauthorized" />
        <result column="MODIFIERID" property="modifierid" />
        <result column="CREATIONDATE" property="creationdate" />
        <result column="MODIFIEDDATE" property="modifieddate" />
        <result column="OWNERID" property="ownerid" />
        <result column="PASSWORDHASH" property="passwordhash" />
        <result column="ISENABLED" property="isenabled" />
        <result column="ISEMPLOYEE" property="isemployee" />
        <result column="ISADMIN" property="isadmin" />
        <result column="USERTYPE" property="usertype" />
        <result column="TRUENAME" property="truename" />
        <result column="NAME" property="name" />
        <result column="DESCRIPTION" property="description" />
        <result column="PASSWORD" property="password" />
        <result column="EMAIL" property="email" />
        <result column="EMAILUSER" property="emailuser" />
        <result column="EMAILUSERPW" property="emailuserpw" />
        <result column="TITLE" property="title" />
        <result column="COMMENTS" property="comments" />
        <result column="PHONE" property="phone" />
        <result column="PHONE2" property="phone2" />
        <result column="FAX" property="fax" />
        <result column="LASTRESULT" property="lastresult" />
        <result column="EMAILVERIFY" property="emailverify" />
        <result column="C_DEPARTMENT_ID" property="cDepartmentId" />
        <result column="LANGUAGE" property="language" />
        <result column="IS_OTP" property="isOtp" />
        <result column="OTP_LENGTH" property="otpLength" />
        <result column="OTP_SECONDS" property="otpSeconds" />
        <result column="OTP_SECRET" property="otpSecret" />
        <result column="OTP_COUNTER" property="otpCounter" />
        <result column="IS_OTP_ONLY" property="isOtpOnly" />
        <result column="OTP_CDATE" property="otpCdate" />
        <result column="LOGIN_IP_RULE" property="loginIpRule" />
        <result column="ISSMS" property="issms" />
        <result column="IS_OUT" property="isOut" />
        <result column="ASSIGNEE_ID" property="assigneeId" />
        <result column="ISSALER" property="issaler" />
        <result column="C_STORE_ID" property="cStoreId" />
        <result column="DISCOUNTLIMIT" property="discountlimit" />
        <result column="ISOPR" property="isopr" />
        <result column="SAASVENDOR" property="saasvendor" />
        <result column="SAASUSER" property="saasuser" />
        <result column="HR_EMPLOYEE_ID" property="hrEmployeeId" />
        <result column="C_CUSTOMER_ID" property="cCustomerId" />
        <result column="C_CUSTOMERUP_ID" property="cCustomerupId" />
        <result column="AREAMNG_ID" property="areamngId" />
        <result column="SGRADE" property="sgrade" />
        <result column="ISOVERSEAS" property="isoverseas" />
        <result column="SUBSYSTEMS" property="subsystems" />
        <result column="WEBPOS_PER" property="webposPer" />
        <result column="C_SUPPLIER_ID" property="cSupplierId" />
        <result column="ISRET" property="isret" />
        <result column="MAC" property="mac" />
        <result column="IS_CLOSE" property="isClose" />
        <result column="C_AREA_ID" property="cAreaId" />
        <result column="C_CITY_ID" property="cCityId" />
        <result column="PASSWORDEXPIRATIONDATE" property="passwordexpirationdate" />
        <result column="HR_PSN_ID" property="hrPsnId" />
        <result column="PASSWORDRESET" property="passwordreset" />
        <result column="QNVL_VIP" property="qnvlVip" />
        <result column="IS_CAN_EBSOOUT" property="isCanEbsoout" />
        <result column="WEBPOS20_PER" property="webpos20Per" />
        <result column="CHECKCODE" property="checkcode" />
        <result column="CHECKDATE" property="checkdate" />
        <result column="IS_WXMESSAGE_PUSH" property="isWxmessagePush" />
        <result column="C_PRICEAREA_ID" property="cPriceareaId" />
        <result column="C_PRICEREGION_ID" property="cPriceregionId" />
        <result column="PRICE_REVISION" property="priceRevision" />
        <result column="DISCOUNTLIMIT_NEW" property="discountlimitNew" />
        <result column="BPOS_SPECIAL_LANGUAGE" property="bposSpecialLanguage" />
        <result column="DISCOUNTMAX" property="discountmax" />
        <result column="BPOSDATE" property="bposdate" />
        <result column="IS_TORFID" property="isTorfid" />
        <result column="OLD_NAME" property="oldName" />
        <result column="C_MAINSTORE_ID" property="cMainstoreId" />
        <result column="MODIFY_TOTAL_AMOUNT_LIMIT" property="modifyTotalAmountLimit" />
        <result column="IS_TODD" property="isTodd" />
        <result column="IS_UPUSERPASSWORD" property="isUpuserpassword" />
        <result column="DEPARTMENT" property="department" />
        <result column="OACODE" property="oacode" />
    </resultMap>


    <!--档案创建账号 向子表插入数据存储过程 statementType 声明指向的是什么类型，其中CALLABLE是执行存储过程和函数的-->
    <select id="insertIntoUsersTableAc" statementType="CALLABLE" parameterType="java.util.Map" resultType="java.lang.Void">
            { call USERS_AC(#{usersId,mode=IN,jdbcType=INTEGER}) }
    </select>

    <select id="insertIntoUsersTableAm" statementType="CALLABLE" parameterType="java.util.Map" resultType="java.lang.Void">
        { call USERS_AM(#{usersId,mode=IN,jdbcType=INTEGER}) }
    </select>


</mapper>
