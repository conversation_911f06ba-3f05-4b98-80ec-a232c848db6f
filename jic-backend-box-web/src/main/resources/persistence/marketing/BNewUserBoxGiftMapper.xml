<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.marketing.mapper.BNewUserBoxGiftMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.marketing.model.BNewUserBoxGift">
        <id column="ID" property="id" />
        <result column="UNIONID" property="unionid" />
        <result column="STATUS" property="status" />
        <result column="SEND_NUM" property="sendNum" />
        <result column="SKU" property="sku" />
        <result column="BOX_GIFT_NAME" property="boxGiftName" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DEL" property="isDel" />
    </resultMap>

    <resultMap id="DtoMap" type="com.jnby.dto.marketing.BNewUserBoxGiftDTO">
        <id column="ID" property="id" />
        <result column="UNIONID" property="unionid" />
        <result column="STATUS" property="status" />
        <result column="SEND_NUM" property="sendNum" />
        <result column="SKU" property="sku" />
        <result column="BOX_GIFT_NAME" property="boxGiftName" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="IS_DEL" property="isDel" />
        <result column="subId" property="subId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, UNIONID, STATUS, SEND_NUM, SKU, BOX_GIFT_NAME, CREATE_TIME, UPDATE_TIME, IS_DEL
    </sql>

    <select id="selectIncrementCountByTime" resultType="java.lang.Integer">
        select count(*) from (
             SELECT a.*, b.SUBSCRIBE_ID as subId FROM b_new_user_box_gift a
                left join b_new_user_member_card b on a.OUT_NO=b.OUT_NO
             WHERE
                 ((
                      a.create_time BETWEEN #{fromDate} AND #{toDate}
                      )
                     OR (
                      a.update_time BETWEEN #{fromDate} AND #{toDate}
                      ))
               and b.APPLICABLE_PARTY = 1
               and b.STATUS = 1
         )t
    </select>

    <select id="selectIncrementListByTime" resultMap="DtoMap">
        SELECT a.*, b.SUBSCRIBE_ID as subId FROM b_new_user_box_gift a
             left join b_new_user_member_card b on a.OUT_NO=b.OUT_NO
        WHERE
            ((
                 a.create_time BETWEEN #{fromDate} AND #{toDate}
                 )
                OR (
                 a.update_time BETWEEN #{fromDate} AND #{toDate}
                 ))
          and b.APPLICABLE_PARTY = 1
          and b.STATUS = 1
        ORDER BY a.update_time asc
    </select>

</mapper>
