<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.oa.mapper.XsMapper">


    <resultMap id="CbBaseResultMap" type="com.jnby.infrastructure.oa.model.CustomerWithBrand">
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="request_id" jdbcType="VARCHAR" property="requestId"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="customer_id" jdbcType="VARCHAR" property="customerId"/>
        <result column="arc_brand_id" jdbcType="VARCHAR" property="arcBrandId"/>
    </resultMap>


    <select id="getXsCustomerWithBrand" resultMap="CbBaseResultMap">
        (
            SELECT
           'fm469-' || fm469.id as id,
            fm469.REQUESTID AS request_id,
            cc.C_ARCBRAND_ID  as arc_brand_id,
            cc.id AS customer_id,
            cc.name AS customer_name
            FROM
            formtable_main_469 fm469
            LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm469.bjjxs
            where fm469.REQUESTID in
            <foreach collection="requestIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        )union(
            SELECT
            'fm457dt1-' || fm457dt1.id as id,
            fm457.REQUESTID AS request_id,
            cc.C_ARCBRAND_ID  as arc_brand_id,
            cc.id AS customer_id,
            cc.name AS customer_name
            FROM
            formtable_main_457_dt1 fm457dt1
            LEFT JOIN formtable_main_457 fm457 on fm457dt1.mainid=fm457.id
            LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm457dt1.bjjxs
            where fm457.REQUESTID in
            <foreach collection="requestIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        )union(
            SELECT
            'fm457dt2-' || fm457dt2.id as id,
            fm457.REQUESTID AS request_id,
            cc.C_ARCBRAND_ID  as arc_brand_id,
            cc.id AS customer_id,
            cc.name AS customer_name
            FROM
            formtable_main_457_dt2 fm457dt2
            LEFT JOIN formtable_main_457 fm457 on fm457dt2.mainid=fm457.id
            LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm457dt2.bjjxs1
            where fm457.REQUESTID in
            <foreach collection="requestIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        )union(
            SELECT
                'fm422dt1-' ||  fm422dt1.id as id,
                fm422.REQUESTID AS request_id,
                cc.C_ARCBRAND_ID  as arc_brand_id,
                cc.id AS customer_id,
                cc.name AS customer_name
                FROM
                formtable_main_422_dt1 fm422dt1
                LEFT JOIN formtable_main_422 fm422 on fm422dt1.mainid=fm422.id
                LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm422dt1.SSJXS
            where fm422.REQUESTID in
            <foreach collection="requestIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        )union(
            SELECT
            'fm423dt1-' ||fm423dt1.id as id,
            fm423.REQUESTID AS request_id,
            cc.C_ARCBRAND_ID as arc_brand_id,
            cc.id AS customer_id,
            cc.name AS customer_name
            FROM
            formtable_main_423_dt1 fm423dt1
            LEFT JOIN formtable_main_423 fm423 on fm423dt1.mainid=fm423.id
            LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm423dt1.bjjxs
            where fm423.REQUESTID in
            <foreach collection="requestIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        )union(
            SELECT
            'fm443-' ||  fm443.id as id,
            fm443.REQUESTID AS request_id,
            cc.C_ARCBRAND_ID as arc_brand_id,
            cc.id AS customer_id,
            cc.name AS customer_name
            FROM
            formtable_main_443 fm443
            LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm443.bjjxs
            where fm443.REQUESTID in
            <foreach collection="requestIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        )union(
            SELECT
            'fm545dt1-' ||  fm545dt1.id as id,
            fm545.REQUESTID AS request_id,
            cc.C_ARCBRAND_ID as arc_brand_id,
            cc.id AS customer_id,
            cc.name AS customer_name
            FROM
            formtable_main_545_dt1  fm545dt1
            LEFT JOIN  formtable_main_545 fm545  on fm545dt1.mainid =fm545.ID
            LEFT JOIN c_store_new csn on csn.id =fm545dt1.dcmc
            LEFT JOIN C_CUSTOMER cc ON csn.C_CUSTOMER_ID = cc.id
            WHERE fm545.mdlx=1 and
            fm545.REQUESTID IN
            <foreach collection="requestIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        )union(
            SELECT
            'fm790-' || fm790.id as id,
            fm790.REQUESTID AS request_id,
            cc.C_ARCBRAND_ID as arc_brand_id,
            cc.id AS customer_id,
            cc.name AS customer_name
            FROM
            formtable_main_790 fm790
            LEFT JOIN C_CUSTOMER cc ON fm790.bjjxs = cc.id
            WHERE fm790.hdfqgs=1 and
            fm790.REQUESTID IN
            <foreach collection="requestIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        )
    </select>




    <select id="getXsCustomerWithBrand457" resultMap="CbBaseResultMap">
      (
        SELECT
        'fm457dt1-' ||   fm457dt1.id as id,
        fm457.REQUESTID AS request_id,
        cc.C_ARCBRAND_ID  as arc_brand_id,
        cc.id AS customer_id,
        cc.name AS customer_name
        FROM
        formtable_main_457_dt1 fm457dt1
        LEFT JOIN formtable_main_457 fm457 on fm457dt1.mainid=fm457.id
        LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm457dt1.bjjxs
        where fm457.REQUESTID in
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        )union(
        SELECT
        'fm457dt2-' || fm457dt2.id as id,
        fm457.REQUESTID AS request_id,
        cc.C_ARCBRAND_ID  as arc_brand_id,
        cc.id AS customer_id,
        cc.name AS customer_name
        FROM
        formtable_main_457_dt2 fm457dt2
        LEFT JOIN formtable_main_457 fm457 on fm457dt2.mainid=fm457.id
        LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm457dt2.bjjxs1
        where fm457.REQUESTID in
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        )
    </select>


    <select id="getXsCustomerWithBrand422And423" resultMap="CbBaseResultMap">
        (
            SELECT
             'fm422dt1-' || fm422dt1.id as id,
            fm422.REQUESTID AS request_id,
            cc.C_ARCBRAND_ID  as arc_brand_id,
            cc.id AS customer_id,
            cc.name AS customer_name
            FROM
            formtable_main_422_dt1 fm422dt1
            LEFT JOIN formtable_main_422 fm422 on fm422dt1.mainid=fm422.id
            LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm422dt1.SSJXS
            where fm422.REQUESTID in
            <foreach collection="requestIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        )union(
            SELECT
             'fm423dt1-' || fm423dt1.id as id,
            fm423.REQUESTID AS request_id,
            cc.C_ARCBRAND_ID  as arc_brand_id,
            cc.id AS customer_id,
            cc.name AS customer_name
            FROM
            formtable_main_423_dt1 fm423dt1
            LEFT JOIN formtable_main_423 fm423 on fm423dt1.mainid=fm423.id
            LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm423dt1.bjjxs
            where fm423.REQUESTID in
            <foreach collection="requestIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        )
    </select>

    <select id="getXsCustomerWithBrand545" resultMap="CbBaseResultMap">
        SELECT
        'fm545dt1-' || fm545dt1.id as id,
        fm545.REQUESTID AS request_id,
        cc.C_ARCBRAND_ID as arc_brand_id,
        cc.id AS customer_id,
        cc.name AS customer_name
        FROM
        formtable_main_545_dt1 fm545dt1
        LEFT JOIN formtable_main_545 fm545 on fm545dt1.mainid =fm545.ID
        LEFT JOIN c_store_new csn on csn.id =fm545dt1.dcmc
        LEFT JOIN C_CUSTOMER cc ON csn.C_CUSTOMER_ID = cc.id
        WHERE fm545.mdlx=1 and
        fm545.REQUESTID IN
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
    </select>

    <select id="getXsCustomerWithBrand469And443And790" resultMap="CbBaseResultMap">
        (
            SELECT
            'fm469-'||fm469.id as id,
            fm469.REQUESTID AS request_id,
            cc.C_ARCBRAND_ID  as arc_brand_id,
            cc.id AS customer_id,
            cc.name AS customer_name
            FROM
            formtable_main_469 fm469
            LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm469.bjjxs
            where fm469.REQUESTID in
            <foreach collection="requestIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        )union(
            SELECT
            'fm443-'|| fm443.id as id,
            fm443.REQUESTID AS request_id,
            cc.C_ARCBRAND_ID  as arc_brand_id,
            cc.id AS customer_id,
            cc.name AS customer_name
            FROM
            formtable_main_443 fm443
            LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm443.bjjxs
            where fm443.REQUESTID in
            <foreach collection="requestIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        )union(
            SELECT
            'fm790-'||fm790.id as id,
            fm790.REQUESTID AS request_id,
            cc.C_ARCBRAND_ID  as arc_brand_id,
            cc.id AS customer_id,
            cc.name AS customer_name
            FROM
            formtable_main_790 fm790
            LEFT JOIN C_CUSTOMER cc ON fm790.bjjxs = cc.id
            WHERE fm790.hdfqgs=1 and
            fm790.REQUESTID IN
            <foreach collection="requestIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        )
    </select>


</mapper>
