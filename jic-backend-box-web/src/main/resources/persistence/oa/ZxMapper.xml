<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.oa.mapper.ZxMapper">


    <resultMap id="CbBaseResultMap" type="com.jnby.infrastructure.oa.model.CustomerWithBrand">
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="request_id" jdbcType="VARCHAR" property="requestId"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="customer_id" jdbcType="VARCHAR" property="customerId"/>
        <result column="arc_brand_id" jdbcType="VARCHAR" property="arcBrandId"/>
    </resultMap>


    <select id="getZxCustomerWithBrand" resultMap="CbBaseResultMap">
        (
            SELECT
            'fm26-' ||  fm26.id  as id,
            fm26.REQUESTID as request_id,
            cc.C_ARCBRAND_ID as arc_brand_id,
            cc.id AS customer_id,
            cc.name AS customer_name
            FROM
            formtable_main_26 fm26
            LEFT JOIN C_STORE_NEW csn ON csn.id = fm26.DCMCHB
            LEFT JOIN C_CUSTOMER cc ON csn.C_CUSTOMER_ID = cc.id
            WHERE
            fm26.DCMCHB IS NOT NULL and
            fm26.REQUESTID in
            <foreach collection="requestIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        )
        union
        (
            SELECT
           'fm26-' || fm26.id as id,
            fm26.REQUESTID,
            cc.C_ARCBRAND_ID  as arc_brand_id,
            cc.id AS customer_id,
            cc.name AS customer_name
            FROM
            formtable_main_26 fm26
            LEFT JOIN C_STORE_NEW csn ON csn.id = fm26.DCMC
            LEFT JOIN C_CUSTOMER cc ON csn.C_CUSTOMER_ID = cc.id
            WHERE
            fm26.DCMCHB  is NULL and
            fm26.REQUESTID in
            <foreach collection="requestIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        )

    </select>


</mapper>
