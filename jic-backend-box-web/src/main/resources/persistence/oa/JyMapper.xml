<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.oa.mapper.JyMapper">

    <select id="select48ByRequestId" resultType="com.jnby.dto.oa.FormTableMain48Dto">
        select id, bjjxsmc, REQUESTID from formtable_main_48
    </select>


    <select id="select88ByRequestId" resultType="com.jnby.dto.oa.FormTableMain88Dto">
        select id, jxs, REQUESTID from formtable_main_88
    </select>


    <resultMap id="CbBaseResultMap" type="com.jnby.infrastructure.oa.model.CustomerWithBrand">
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="request_id" jdbcType="VARCHAR" property="requestId"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="customer_id" jdbcType="VARCHAR" property="customerId"/>
        <result column="arc_brand_id" jdbcType="VARCHAR" property="arcBrandId"/>
    </resultMap>


    <select id="getJyCustomerWithBrand" resultMap="CbBaseResultMap">
        (
                SELECT
            'fm48-' ||  fm48.id as id,
            fm48.REQUESTID as request_id,
            cc.id AS customer_id,
            cc.name AS customer_name,
            cc.C_ARCBRAND_ID as arc_brand_id
            FROM
            formtable_main_48 fm48
            LEFT JOIN C_CUSTOMER cc ON fm48.bjjxsmc = cc.id
            where fm48.REQUESTID in
            <foreach collection="requestIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        )
        union
        (
            SELECT  'fm88-' ||   fm88.id as id,
                   fm88.REQUESTID as request_id,
            cc2.id AS customer_id,
            cc2.name AS customer_name,
            cc2.C_ARCBRAND_ID as arc_brand_id
            from
            formtable_main_88 fm88
            LEFT JOIN C_CUSTOMER cc2 ON fm88.jxs = cc2.id
            where fm88.REQUESTID in
            <foreach collection="requestIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        )


    </select>

</mapper>
