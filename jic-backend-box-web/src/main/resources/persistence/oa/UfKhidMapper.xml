<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.oa.mapper.UfKhidMapper">

    <insert id="insertSelective">
        insert into ECOLOGY.UF_KHID (KHID,name) values (#{khid},#{name})
    </insert>

    <select id="selectById" resultType="com.jnby.dto.UfKhid">

        select id, khid,name from ECOLOGY.UF_KHID where id = #{id}

    </select>

</mapper>
