<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.oa.mapper.DingtalkSendMsgMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.oa.model.DingtalkSendMsgVo">
        <result column="ID" property="id"/>
        <result column="LINK_ID" property="linkId"/>
        <result column="MSG_TITLE" property="msgTitle"/>
        <result column="MSG_CONTENT" property="msgContent"/>
        <result column="FLAG" property="flag"/>
        <result column="SEND_DATE" property="sendDate"/>
        <result column="MSG_TYPE" property="msgType"/>
        <result column="MSG_URL" property="msgUrl"/>
        <result column="MESSAGE" property="message"/>
        <result column="CREATE_DATE" property="createDate"/>
        <result column="UPDATE_DATE" property="updateDate"/>
        <result column="MSG_TASK_ID" property="msgTaskId"/>
    </resultMap>

    <select id="getIdSequences" resultType="java.lang.Long" parameterType="java.lang.String">
        select get_sequences(#{tableName}) FROM dual
    </select>
</mapper>
