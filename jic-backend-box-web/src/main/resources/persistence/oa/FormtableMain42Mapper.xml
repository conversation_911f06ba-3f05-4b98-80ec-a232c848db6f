<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.oa.mapper.FormtableMain42Mapper">

    <select id="selectByCustomerIds" resultType="com.jnby.dto.oa.FormattableMain42Dto">
        select htkrrq , jhfll , xxfll , hpzk, xpzk , thl , htqjhzb , xtsyf , lybzj ,dfsfzhsh,jsxbjjd,dflxr,dflxrdh,hzdwmode, dfdz
        from formtable_main_42 where jsxbjjd   in
        <foreach
                collection="customerIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        and HTQK1 = 0  and htkrrq is not null order by htkrrq desc

    </select>

    <select id="selectByConsSalesCustomerIds" resultType="com.jnby.dto.oa.FormattableMain42Dto">

        select htkrrq , jhfll , xxfll , hpzk, xpzk , thl , htqjhzb , xtsyf , lybzj ,dfsfzhsh,jsxbjjd   from formtable_main_42 where jsxbjjd   in
        <foreach
                collection="customerIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        and HTQK1 = 2  and htkrrq is not null order by htkrrq desc

    </select>


    <select id="selectByRequestId" resultType="com.jnby.dto.oa.FormattableMain42Dto">
        select htkrrq , jhfll , xxfll , hpzk, xpzk , thl , htqjhzb , xtsyf , lybzj ,dfsfzhsh,jsxbjjd ,id  from formtable_main_42
        where  REQUESTID=#{requestId}
    </select>



    <resultMap id="HtBaseResultMap" type="com.jnby.infrastructure.oa.model.HtCustomerWithBrand">
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="request_id" jdbcType="VARCHAR" property="requestId"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="customer_id" jdbcType="VARCHAR" property="customerId"/>
        <result column="arc_brand_id" jdbcType="VARCHAR" property="arcBrandId"/>
        <result column="ht_start_time" jdbcType="VARCHAR" property="htStartTime"/>
    </resultMap>


    <select id="getHtCustomerWithBrand" resultMap="HtBaseResultMap">
        SELECT
        'fm42-' || fm42.id as id,
        fm42.REQUESTID as request_id,
        cc.C_ARCBRAND_ID as arc_brand_id,
        cc.id AS customer_id ,
        cc.name AS customer_name,
        fm42.htkrrq as ht_start_time
        FROM
        formtable_main_42 fm42
        LEFT JOIN C_CUSTOMER cc ON fm42.jsxbjjd = cc.id
        WHERE
        fm42.htqk1 IS NOT NULL and
        fm42.REQUESTID in
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
    </select>


    <select id="selectUfCustomerNameById" resultType="java.lang.String">
        select name from uf_customerinfo where id =#{id}
    </select>


</mapper>
