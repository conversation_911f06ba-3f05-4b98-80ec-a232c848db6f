<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.oa.mapper.FormTableMain853Mapper">
    <select id="selectByRequestId" resultType="com.jnby.dto.oa.FormTableMain853Dto">
        select id,bjjxs
        <!-- formtable_main_853正式 formtable_main_846测试-->
        from formtable_main_853
        where  REQUESTID=#{requestId}
    </select>


    <resultMap id="FlowDataMap" type="com.jnby.dto.FlowDataDTO">
        <result column="城市编码" property="generatorCode" />
        <result column="经销商城市" property="generatorCodeName" />
        <result column="主表流程ID" property="requestId" />
        <result column="所属经销商" property="cCustomerId" />
        <result column="店铺名称" property="name" />
        <result column="品牌" property="cArcbrandId" />
        <result column="销售区域" property="cAreaId" />
        <result column="省" property="cProvinceId" />
        <result column="市" property="cCityId" />
        <result column="区" property="cDistrictId" />
        <result column="详细地址" property="zrztxxdz" />
        <result column="经纬度" property="jwd" />
        <result column="店铺地址" property="address" />
        <result column="店铺性质" property="cStoreattrib7Id" />
        <result column="店铺分类" property="cStoreattrib8Id" />
        <result column="店铺管理模式" property="cStoreattrib9Id" />
        <result column="新开店仓楼层" property="cStoreattrib15Id" />
        <result column="新开店仓电话号码" property="phone" />
        <result column="预计开业时间" property="preopendate" />
        <result column="系统建档类别" property="systemCreateType" />
        <result column="道具仓建档类别" property="storePropCreateType" />
            <result column="销售区域ID" property="newAreaId" />
        <result column="建筑面积" property="dpmj" />
        <result column="甲方体系" property="jftx" />
        <result column="细化城市" property="xhcs" />
        <result column="细化区县" property="xhq" />
        <result column="商场名" property="scm" />
        <result column="是否正价" property="sfzj" />
        <result column="NC经销商名称" property="jxsssgsqc" />
        <result column="伯俊城市级别" property="bojuncsjb" />
        <result column="客户主键" property="khid" />
        <result column="大区经理" property="dqjl" />
        <result column="sfwgs" property="sfwgs" />
    </resultMap>


    <select id="getOaBatchDataUseFlow"  resultMap="FlowDataMap">
        select
            tmp.code      城市编码,
            tmp.name      经销商城市,
            a.requestid 主表流程ID,
            c.pinpai 品牌,
            a.xsqy      销售区域ID,
            a.ksqc    NC经销商名称,
            a.KCQJNID 客户主键,
            a.dqjl 大区经理
        from
        formtable_main_853 a,
        formtable_main_413 c,
        c_city_tmp tmp
        <where>
            a.PP = c.id(+)
            and a.CSMC2 = tmp.id(+)
            and a.requestid in
            <foreach  item="item" index="index" collection="flowDataParam.requestIdList" open="(" separator="," close=" )">
                #{item}
            </foreach>
        </where>
    </select>
    <select id="selectByBjjxsid" resultMap="FlowDataMap">
        select
        a.xsqy      销售区域ID,
        a.dqjl 大区经理,
        a.jxs 省,
        a.jxshi 市,
        a.q 区,
        a.zrztxxdz 详细地址,
        a.sfwgs
        from
        formtable_main_853 a
        <where>
                a.bjjxs = #{bjjxsid}
        </where>
    </select>
    <select id="selectNameByProviceId" resultType="java.lang.String">
        select sheng from uf_jxs where id = #{id}

    </select>

    <select id="selectNameByCityId" resultType="java.lang.String">
        select jxshi  from uf_jxshi where id = #{id}

    </select>

    <select id="selectNameByReigonID" resultType="java.lang.String">
        select jxqy   from uf_jxqy where id = #{id}

    </select>

</mapper>
