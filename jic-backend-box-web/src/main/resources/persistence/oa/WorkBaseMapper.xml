<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.oa.mapper.WorkBaseMapper">

    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.oa.model.WorkFlowEntity">
        <result column="title" property="title"/>
        <result column="request_id" property="requestId"/>
        <result column="type" property="type"/>
        <result column="customer_name" property="customerName"/>
        <result column="customer_id" property="customerId"/>
<!--        <result column="arc_brand_name" property="arcBrandName"/>-->
        <result column="create_by" property="createBy"/>
        <result column="create_date" property="createDate"/>
        <result column="current_node_type" property="currentNodeType"/>
    </resultMap>


    <select id="selectHtList" resultMap="BaseResultMap">
        select
        a.REQUESTID AS request_id,
        wb.workflowname AS TYPE,
        a.requestname AS title,
        a.currentnodetype AS current_node_type,
        cc.id AS customer_id,
        cc.name AS customer_name,
        b1.lastname AS create_by,
        a.createdate AS create_date,
        cc.C_ARCBRAND_ID as arc_brand_id  from   formtable_main_42 f42
        LEFT JOIN workflow_requestbase a ON a.REQUESTID = f42.REQUESTID
        LEFT JOIN C_CUSTOMER cc ON cc.id = f42.jsxbjjd
        LEFT JOIN workflow_base wb ON a.workflowid = wb.id
        LEFT JOIN HrmResource b1 ON a.creater = b1.id
        where a.creatertype = 0
        <if test="title != null and title != '' ">
            and a.requestname = #{title}
        </if>
        <if test="arcBrandIdList != null and arcBrandIdList.size() > 0">
            and  cc.C_ARCBRAND_ID   in
            <foreach collection="arcBrandIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="customerId != null and customerId != '' ">
            and cc.id =#{customerId}
        </if>

        <if test="typeIdList != null and typeIdList.size() > 0">
            and wb.id  in
            <foreach collection="typeIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>

        <if test="currentNodeTypeList != null and currentNodeTypeList.size() > 0">
            and a.currentNodeType  in
            <foreach collection="currentNodeTypeList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="ktStartTime != null and ktStartTime != '' ">
            and  #{ktStartTime} >= f42.htkrrq
        </if>

        <if test="ktEndTime != null and ktEndTime != '' ">
            and   #{ktEndTime} >= f42.htkrrq
        </if>

    </select>



    <!--    <select id="selectHtList" resultMap="BaseResultMap">-->
<!--        SELECT-->
<!--        a.REQUESTID AS request_id,-->
<!--        wb.workflowname AS TYPE,-->
<!--        a.requestname AS title,-->
<!--        a.currentnodetype AS current_node_type,-->
<!--        cc.id AS customer_id,-->
<!--        cc.name AS customer_name,-->
<!--        b1.lastname AS create_by,-->
<!--        a.createdate AS create_date,-->
<!--        cc.C_ARCBRAND_ID as arc_brand_id-->
<!--        FROM-->
<!--        workflow_requestbase a-->
<!--        LEFT JOIN HrmResource b1 ON a.creater = b1.id-->
<!--        LEFT JOIN workflow_base wb ON a.workflowid = wb.id-->
<!--        LEFT JOIN formtable_main_42 f42 ON a.REQUESTID = f42.REQUESTID-->
<!--        LEFT JOIN C_CUSTOMER cc ON cc.id = f42.jsxbjjd-->
<!--        WHERE-->
<!--        a.creatertype = 0-->
<!--        AND wb.workflowname IN ( 'XMGL（HT）-02、合同审批申请流程（经销业务）' )-->
<!--        <if test="title != null and title != '' ">-->
<!--            and a.requestname = #{title}-->
<!--        </if>-->

<!--        <if test="arcBrandIdList != null and arcBrandIdList.size() > 0">-->
<!--            and  cc.C_ARCBRAND_ID   in-->
<!--            <foreach collection="arcBrandIdList" item="id" open="(" close=")" separator=",">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <if test="customerId != null and customerId != '' ">-->
<!--            and cc.id =#{customerId}-->
<!--        </if>-->

<!--        <if test="typeIdList != null and typeIdList.size() > 0">-->
<!--            and wb.id  in-->
<!--            <foreach collection="typeIdList" item="id" open="(" close=")" separator=",">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->

<!--        <if test="currentNodeTypeList != null and currentNodeTypeList.size() > 0">-->
<!--            and a.currentNodeType  in-->
<!--            <foreach collection="currentNodeTypeList" item="id" open="(" close=")" separator=",">-->
<!--                #{id}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <if test="ktStartTime != null and ktStartTime != '' ">-->
<!--            and  #{ktStartTime} >= f42.htkrrq-->
<!--        </if>-->

<!--        <if test="ktEndTime != null and ktEndTime != '' ">-->
<!--            and   #{ktEndTime} >= f42.htkrrq-->
<!--        </if>-->

<!--    </select>-->



    <select id="selectCwList" resultMap="BaseResultMap">
        SELECT
        a.REQUESTID AS request_id,
        wb.workflowname AS TYPE,
        a.requestname AS title,
        a.currentnodetype AS current_node_type,
        b1.lastname AS create_by,
        a.createdate AS create_date
        FROM
        workflow_requestbase a
        LEFT JOIN HrmResource b1 ON a.creater = b1.id
        LEFT JOIN workflow_base wb ON a.workflowid = wb.id
        WHERE
        a.creatertype = 0
        AND a.workflowid IN ( SELECT id FROM workflow_base WHERE workflowname IN ( 'JXGL-03、（客户）经销商-装修支持申请流程', 'JXGL-04、经销商-装修支持结算申请流程（第一次结算）', 'JXGL-05、经销商-装修支持结算申请流程（第二次结算）', 'JXGL-07、经销商-代销结账单申请流程', 'JXGL-11、经销商-扣/返款流程' ) )
        <if test="title != null and title != ''">
            and a.requestname = #{title}
        </if>
        <if test="currentNodeTypeList != null and currentNodeTypeList.size() > 0">
            and a.currentNodeType  in
            <foreach collection="currentNodeTypeList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>


        <if test="typeIdList != null and typeIdList.size() > 0">
            and a.workflowid  in
            <foreach collection="typeIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>

        <if test="customerId != null and customerId != '' ">
            and a.REQUESTID in
          (
            (
            <!--JXGL-03、（客户）经销商-装修支持申请流程  formtable_main_25-->
            select REQUESTID from formtable_main_25 fm25 LEFT JOIN c_store_new csn
            on fm25.dianpmc= csn.id
            where csn.C_CUSTOMER_ID = #{customerId}
            )
            union(
            <!--JXGL-04、经销商-装修支持结算申请流程（第一次结算）formtable_main_36-->
            select REQUESTID from formtable_main_36 fm36
            where fm36.jxs = #{customerId}
            )
            union(
            <!--JXGL-05、经销商-装修支持结算申请流程（第二次结算）formtable_main_278-->
            select REQUESTID from formtable_main_278 fm278
            where fm278.jxs = #{customerId}
            )

            union(
            <!-- JXGL-07、经销商-代销结账单申请流程）formtable_main_756-->
            select REQUESTID from formtable_main_756 fm756
            where fm756.bjjxs = #{customerId}
            )
            union (
            <!--JXGL-11、经销商-扣/返款流程 formtable_main_471-->
            select REQUESTID from formtable_main_471 fm471
            left join formtable_main_471_dt1 fm471dt1
            on fm471.id=fm471dt1.mainid
            where fm471dt1.jxs = #{customerId}
            )
          )
        </if>
        <if test="arcBrandIdList != null and arcBrandIdList.size() > 0">
            and a.REQUESTID in
            (
            (
            <!--JXGL-03、（客户）经销商-装修支持申请流程  formtable_main_25-->
            select REQUESTID from formtable_main_25 fm25 LEFT JOIN c_store_new csn
            on fm25.dianpmc= csn.id
            where csn.C_ARCBRAND_ID  in
            <foreach collection="arcBrandIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
            )
            union(
            <!--JXGL-04、经销商-装修支持结算申请流程（第一次结算）formtable_main_36-->
            select REQUESTID from formtable_main_36 fm36
            LEFT JOIN C_CUSTOMER cc on TO_CHAR(cc.id) =fm36.jxs
            where cc.C_ARCBRAND_ID   in
            <foreach collection="arcBrandIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
            )
            union(
            <!--JXGL-05、经销商-装修支持结算申请流程（第二次结算）formtable_main_278-->
            select REQUESTID from  formtable_main_278 fm278
            LEFT JOIN C_CUSTOMER cc on cc.id =fm278.jxs
            where cc.C_ARCBRAND_ID in
            <foreach collection="arcBrandIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
            )
            union(
            <!-- JXGL-07、经销商-代销结账单申请流程）formtable_main_756-->
            select REQUESTID from formtable_main_756 fm756
            LEFT JOIN C_CUSTOMER cc on cc.id = fm756.bjjxs
            where cc.C_ARCBRAND_ID in
            <foreach collection="arcBrandIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
            )
            union(
            <!--JXGL-11、经销商-扣/返款流程 formtable_main_471-->
            select REQUESTID from formtable_main_471 fm471
            left join formtable_main_471_dt1 fm471dt1
            on fm471.id=fm471dt1.mainid
            LEFT JOIN C_CUSTOMER cc on cc.id =fm471dt1.jxs
            where cc.C_ARCBRAND_ID in
            <foreach collection="arcBrandIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
            )
            )
        </if>
    </select>
    <select id="selectJyList" resultMap="BaseResultMap">
        SELECT
            a.REQUESTID AS request_id,
            wb.workflowname AS TYPE,
            a.requestname AS title,
            a.currentnodetype AS current_node_type,
            b1.lastname AS create_by,
            a.createdate AS create_date
        FROM
            workflow_requestbase a
                LEFT JOIN HrmResource b1 ON a.creater = b1.id
                LEFT JOIN workflow_base wb ON a.workflowid = wb.id
        WHERE
            a.creatertype = 0
        AND wb.workflowname IN (
        'JXGL-08、经销商-解约结算/对账申请流程',
        'XXZX-06、加密狗申请、退回流程'
        )

        <if test="title != null and title != ''">
            and a.requestname = #{title}
        </if>

        <if test="typeIdList != null and typeIdList.size() > 0">
            and wb.id  in
            <foreach collection="typeIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="currentNodeTypeList != null and currentNodeTypeList.size() > 0">
            and a.currentNodeType  in
            <foreach collection="currentNodeTypeList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>

        <if test="customerId != null and customerId != '' ">
            and a.REQUESTID in(

            ( SELECT
            fm48.REQUESTID
            FROM
            formtable_main_48 fm48
            where fm48.bjjxsmc = #{customerId}
            )
            union (
            SELECT fm88.REQUESTID from
            formtable_main_88 fm88
            where fm88.jxs = #{customerId}
            )

            )
        </if>

        <if test="arcBrandIdList != null and arcBrandIdList.size() > 0">
            and a.REQUESTID in(
            ( SELECT
            fm48.REQUESTID
            FROM
            formtable_main_48 fm48
            LEFT JOIN C_CUSTOMER cc ON fm48.bjjxsmc = cc.id
            where cc.C_ARCBRAND_ID  in
            <foreach collection="arcBrandIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
            )
            union (
            SELECT fm88.REQUESTID from
            formtable_main_88 fm88
            LEFT JOIN C_CUSTOMER cc2 ON fm88.jxs = cc2.id
            where cc2.C_ARCBRAND_ID   in
            <foreach collection="arcBrandIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
            )

            )
        </if>


    </select>



    <resultMap id="CbBaseResultMap" type="com.jnby.infrastructure.oa.model.CustomerWithBrand">
        <result column="request_id" jdbcType="VARCHAR" property="requestId"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="customer_id" jdbcType="VARCHAR" property="customerId"/>
        <result column="arc_brand_id" jdbcType="VARCHAR" property="arcBrandId"/>
    </resultMap>


    <select id="getJyCustomerWithBrand" resultMap="CbBaseResultMap">
        ( SELECT
        fm48.REQUESTID as request_id,
        cc.id AS customer_id,
        cc.name AS customer_name,
        cc.C_ARCBRAND_ID as arc_brand_id
        FROM
        formtable_main_48 fm48
        LEFT JOIN C_CUSTOMER cc ON fm48.bjjxsmc = cc.id
        where fm48.REQUESTID in
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        )
        union
(
        SELECT fm88.REQUESTID as request_id,
        cc2.id AS customer_id,
        cc2.name AS customer_name,
        cc2.C_ARCBRAND_ID as arc_brand_id
        from
        formtable_main_88 fm88
        LEFT JOIN C_CUSTOMER cc2 ON fm88.jxs = cc2.id
        where fm88.REQUESTID in
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>

)


    </select>
    <select id="selectDcList" resultMap="BaseResultMap">
        SELECT
        a.REQUESTID AS request_id,
        wb.workflowname AS TYPE,
        a.requestname AS title,
        a.currentnodetype AS current_node_type,
        b1.lastname AS create_by,
        a.createdate AS create_date,
        cc.id AS customer_id,
        cc.name AS customer_name,
        cc.C_ARCBRAND_ID AS C_ARCBRAND_ID
        FROM
        workflow_requestbase a
        LEFT JOIN HrmResource b1 ON a.creater = b1.id
        LEFT JOIN workflow_base wb ON a.workflowid = wb.id
        LEFT JOIN formtable_main_53 fm53 ON a.REQUESTID = fm53.REQUESTID
        LEFT JOIN C_CUSTOMER cc ON cc.id = fm53.bojunjxs
        WHERE
        a.creatertype = 0
        AND wb.workflowname IN ( 'JXGL-01、经销商-开店及建档申请流程' )
        <if test="title != null and title != ''">
            and a.requestname = #{title}
        </if>

        <if test="customerId != null and customerId != '' ">
            and cc.id =#{customerId}
        </if>

        <if test="arcBrandIdList != null and arcBrandIdList.size() > 0">
            and cc.C_ARCBRAND_ID   in
            <foreach collection="arcBrandIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>

        <if test="typeIdList != null and typeIdList.size() > 0">
            and wb.id  in
            <foreach collection="typeIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="currentNodeTypeList != null and currentNodeTypeList.size() > 0">
            and a.currentNodeType  in
            <foreach collection="currentNodeTypeList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="selectZxList" resultMap="BaseResultMap">
        SELECT
            a.REQUESTID AS request_id,
            wb.workflowname AS TYPE,
            a.requestname AS title,
            a.currentnodetype AS current_node_type,
            b1.lastname AS create_by,
            a.createdate AS create_date
        FROM
            workflow_requestbase a
                LEFT JOIN HrmResource b1 ON a.creater = b1.id
                LEFT JOIN workflow_base wb ON a.workflowid = wb.id
                LEFT JOIN  formtable_main_26 fm26 on fm26.REQUESTID=a.REQUESTID
        WHERE
            a.creatertype = 0
          AND wb.workflowname IN ( 'JXGL-02、（客户）经销商-装修申请流程' )
        <if test="title != null and title != ''">
            and a.requestname = #{title}
        </if>

        <if test="typeIdList != null and typeIdList.size() > 0">
            and wb.id  in
            <foreach collection="typeIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="currentNodeTypeList != null and currentNodeTypeList.size() > 0">
            and a.currentNodeType  in
            <foreach collection="currentNodeTypeList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>


    <resultMap id="WorkFlowBaseBaseResultMap" type="com.jnby.infrastructure.oa.model.WorkflowBase">
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="workflowname" jdbcType="VARCHAR" property="workFlowName"/>
    </resultMap>



    <select id="getWorkFlowBaseList" resultType="com.jnby.infrastructure.oa.model.WorkflowBase">
        select id ,workflowname from workflow_base wb where workflowname
        in
        <foreach collection="workFlowNameList" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
    </select>


    <select id="getWorkFlowBaseListByWfIdentKey" resultType="com.jnby.infrastructure.oa.model.WorkflowBase">
        select id ,workflowname from workflow_base wb where WFIDENTKEY
        in
        <foreach collection="wfIdentKeyList" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
    </select>



    <select id="getZxCustomerWithBrand" resultMap="CbBaseResultMap">
        (
        SELECT
        fm26.REQUESTID as request_id,
        cc.C_ARCBRAND_ID as arc_brand_id,
        cc.id AS customer_id,
        cc.name AS customer_name
        FROM
        formtable_main_26 fm26
        LEFT JOIN C_STORE_NEW csn ON csn.id = fm26.DCMCHB
        LEFT JOIN C_CUSTOMER cc ON csn.C_CUSTOMER_ID = cc.id
        WHERE
        fm26.DCMCHB IS NOT NULL and
        fm26.REQUESTID in
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        )
        union
        (
        SELECT
        fm26.REQUESTID,
        cc.C_ARCBRAND_ID as arc_brand_id,
        cc.id AS customer_id,
        cc.name AS customer_name
        FROM
        formtable_main_26 fm26
        LEFT JOIN C_STORE_NEW csn ON csn.id = fm26.DCMC
        LEFT JOIN C_CUSTOMER cc ON csn.C_CUSTOMER_ID = cc.id
        WHERE
        fm26.DCMCHB  is NULL and
        fm26.REQUESTID in
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        )

    </select>
    <select id="selectZrList"  resultMap="CbBaseResultMap">

    </select>

    <select id="getCw1CustomerWithBrand" resultMap="CbBaseResultMap">
        (
        SELECT
        fm25.REQUESTID AS request_id,
        cc.C_ARCBRAND_ID as arc_brand_id,
        cc.id AS customer_id,
        cc.name AS customer_name
        FROM
        formtable_main_25 fm25
        LEFT JOIN c_store_new csn ON csn.id = fm25.dianpmc
        LEFT JOIN C_CUSTOMER cc ON cc.id = csn.C_CUSTOMER_ID
        where
        fm25.REQUESTID in
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        )
       union
        (
        SELECT
        fm36.REQUESTID AS request_id,
        cc.C_ARCBRAND_ID as arc_brand_id,
        cc.id AS customer_id,
        cc.name AS customer_name
        FROM
        formtable_main_36 fm36
        LEFT JOIN C_CUSTOMER cc ON  TO_CHAR(cc.id) = fm36.jxs
        WHERE
        fm36.REQUESTID in
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        )union(
        SELECT
        fm278.REQUESTID as request_id,
        cc.C_ARCBRAND_ID as arc_brand_id,
        cc.id AS customer_id,
        cc.name AS customer_name
        FROM
        formtable_main_278 fm278
        LEFT JOIN C_CUSTOMER cc ON TO_CHAR( cc.id ) = fm278.jxs
        WHERE
        fm278.REQUESTID in
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        )union(
        SELECT
        fm756.REQUESTID AS request_id,
        cc.C_ARCBRAND_ID as arc_brand_id,
        cc.id AS customer_id,
        cc.name AS customer_name
        FROM
        formtable_main_756 fm756
        LEFT JOIN C_CUSTOMER cc ON TO_CHAR( cc.id )= fm756.bjjxs
        WHERE
        fm756.REQUESTID in
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        )
        union(
        SELECT
        fm471.REQUESTID AS request_id,
        cc.C_ARCBRAND_ID as arc_brand_id,
        cc.id AS customer_id,
        cc.name AS customer_name
        FROM
        formtable_main_471 fm471
        LEFT JOIN formtable_main_471_dt1 fm471dt1 ON fm471.id = fm471dt1.mainid
        LEFT JOIN C_CUSTOMER cc ON TO_CHAR( cc.id ) = fm471dt1.jxs
        WHERE
        fm471.REQUESTID in
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        )
    </select>





    <select id="selectXsList" resultType="com.jnby.infrastructure.oa.model.WorkFlowEntity">
        (
            SELECT
            a.REQUESTID AS request_id,
            wb.workflowname AS TYPE,
            a.requestname AS title,
            a.currentnodetype AS current_node_type,
            b1.lastname AS create_by,
            a.createdate AS create_date
            FROM
            workflow_requestbase a
            LEFT JOIN HrmResource b1 ON a.creater = b1.id
            LEFT JOIN workflow_base wb ON a.workflowid = wb.id
            LEFT JOIN  formtable_main_469 fm469 on a.REQUESTID=fm469.REQUESTID
            LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm469.bjjxs
            WHERE
            a.creatertype = 0
            AND wb.workflowname = ( 'JXGL-10、（客户）经销商-信用申请流程流程表单-新')
            <if test="title != null and title != '' ">
                and a.requestname = #{title}
            </if>
            <if test="typeIdList != null and typeIdList.size() > 0">
                and wb.id in
                <foreach collection="typeIdList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="currentNodeTypeList != null and currentNodeTypeList.size() > 0">
                and a.currentNodeType in
                <foreach collection="currentNodeTypeList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="arcBrandIdList != null and arcBrandIdList.size() > 0">
                and cc.C_ARCBRAND_ID in
                <foreach collection="arcBrandIdList" item="id" open="(" close=")" separator=",">#{id}
                </foreach>

            </if>
            <if test="customerId != null and customerId != '' ">
                and cc.id =#{customerId}
            </if>
        )union(
            SELECT
            a.REQUESTID AS request_id,
            wb.workflowname AS TYPE,
            a.requestname AS title,
            a.currentnodetype AS current_node_type,
            b1.lastname AS create_by,
            a.createdate AS create_date
            FROM
            workflow_requestbase a
            LEFT JOIN HrmResource b1 ON a.creater = b1.id
            LEFT JOIN workflow_base wb ON a.workflowid = wb.id
            LEFT JOIN formtable_main_469 fm469 on a.REQUESTID=fm469.REQUESTID
            LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm469.bjjxs
            WHERE
            a.creatertype = 0
            AND wb.workflowname = ('JXGL-23、经销商-期货转放量申请流程')
            <if test="title != null and title != '' ">
                and a.requestname = #{title}
            </if>
            <if test="typeIdList != null and typeIdList.size() > 0">
                and wb.id in
                <foreach collection="typeIdList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="currentNodeTypeList != null and currentNodeTypeList.size() > 0">
                and a.currentNodeType in
                <foreach collection="currentNodeTypeList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="arcBrandIdList != null and arcBrandIdList.size() > 0">
                   and  cc.C_ARCBRAND_ID in
                            <foreach collection="arcBrandIdList" item="id" open="(" close=")" separator=",">
                                #{id}
                            </foreach>
            </if>
            <if test="customerId != null and customerId != '' ">
                and cc.id =#{customerId}
            </if>
        )union(
            SELECT
              a.REQUESTID AS request_id,
              wb.workflowname AS TYPE,
              a.requestname AS title,
              a.currentnodetype AS current_node_type,
              b1.lastname AS create_by,
              a.createdate AS create_date
          FROM
              workflow_requestbase a
                  LEFT JOIN HrmResource b1 ON a.creater = b1.id
                  LEFT JOIN workflow_base wb ON a.workflowid = wb.id
          WHERE
              a.creatertype = 0
            AND wb.workflowname IN (  'JXGL-18、（总部）经销商-退换货申请流程', 'JXGL-19、经销商-货品出库折扣申请流程', 'JXGL-20、经销商-退货率设置申请流程' )
            <if test="title != null and title != '' ">
                and a.requestname = #{title}
            </if>
            <if test="typeIdList != null and typeIdList.size() > 0">
                and wb.id  in
                <foreach collection="typeIdList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="currentNodeTypeList != null and currentNodeTypeList.size() > 0">
                and a.currentNodeType  in
                <foreach collection="currentNodeTypeList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="arcBrandIdList != null and arcBrandIdList.size() > 0">
                and a.REQUESTID  in
                (

                    (
                        SELECT
                        fm457.REQUESTID
                        FROM
                        formtable_main_457 fm457
                        LEFT JOIN formtable_main_457_dt1 fm457dt1 on fm457dt1.mainid=fm457.id
                        LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm457dt1.bjjxs
                        where cc.C_ARCBRAND_ID in
                        <foreach collection="arcBrandIdList" item="id" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
                    )union(
                        SELECT
                        fm457.REQUESTID
                        FROM
                        formtable_main_457 fm457
                        LEFT JOIN formtable_main_457_dt2 fm457dt2 on fm457dt2.mainid=fm457.id
                        LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm457dt2.bjjxs1
                        where cc.C_ARCBRAND_ID in
                        <foreach collection="arcBrandIdList" item="id" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
                    )union(
                        SELECT
                        fm422.REQUESTID
                        FROM
                        formtable_main_422 fm422
                        LEFT JOIN formtable_main_422_dt1 fm422dt1 on fm422dt1.mainid=fm422.id
                        LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm422dt1.SSJXS
                        where cc.C_ARCBRAND_ID in
                        <foreach collection="arcBrandIdList" item="id" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
                    )union(
                        SELECT
                        fm423.REQUESTID
                        FROM
                        formtable_main_423 fm423
                        LEFT JOIN formtable_main_423_dt1 fm423dt1 on fm423dt1.mainid=fm423.id
                        LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm423dt1.bjjxs
                        where cc.C_ARCBRAND_ID in
                        <foreach collection="arcBrandIdList" item="id" open="(" close=")" separator=",">
                            #{id}
                        </foreach>
                    )
                )
            </if>
            <if test="customerId != null and customerId != '' ">
                and  a.REQUESTID in
                (
                    (
                        SELECT
                        fm469.REQUESTID
                        FROM
                        formtable_main_469 fm469
                        LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm469.bjjxs
                        where   cc.id =#{customerId}
                    )union(
                        SELECT
                        fm457.REQUESTID
                        FROM
                        formtable_main_457 fm457
                        LEFT JOIN formtable_main_457_dt1 fm457dt1 on fm457dt1.mainid=fm457.id
                        LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm457dt1.bjjxs
                        where cc.id =#{customerId}
                    )union(
                        SELECT
                        fm457.REQUESTID
                        FROM
                        formtable_main_457 fm457
                        LEFT JOIN formtable_main_457_dt2 fm457dt2 on fm457dt2.mainid=fm457.id
                        LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm457dt2.bjjxs1
                        where cc.id =#{customerId}
                    )union(
                        SELECT
                        fm422.REQUESTID
                        FROM
                        formtable_main_422 fm422
                        LEFT JOIN formtable_main_422_dt1 fm422dt1  on fm422dt1.mainid=fm422.id
                        LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm422dt1.SSJXS
                        where cc.id =#{customerId}
                    )union(
                        SELECT
                        fm423.REQUESTID
                        FROM
                        formtable_main_423 fm423
                        LEFT JOIN formtable_main_423_dt1 fm423dt1  on fm423dt1.mainid=fm423.id
                        LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm423dt1.bjjxs
                        where cc.id =#{customerId}
                    )
                )
            </if>

        ) UNION
        (
            SELECT
                a.REQUESTID AS request_id,
                wb.workflowname AS TYPE,
                a.requestname AS title,
                a.currentnodetype AS current_node_type,
                b1.lastname AS create_by,
                a.createdate AS create_date
            FROM
                workflow_requestbase a
                    LEFT JOIN HrmResource b1 ON a.creater = b1.id
                    LEFT JOIN workflow_base wb ON a.workflowid = wb.id
                    LEFT JOIN formtable_main_545 fm545 ON fm545.REQUESTID = a.REQUESTID
            WHERE
                a.creatertype = 0
              AND fm545.mdlx = 1
              AND wb.workflowname IN ( 'ZYGL-04、直营/经销-内淘不发货设置申请流程' )
            <if test="title != null and title != '' ">
                and a.requestname = #{title}
            </if>
            <if test="typeIdList != null and typeIdList.size() > 0">
                and wb.id  in
                <foreach collection="typeIdList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="currentNodeTypeList != null and currentNodeTypeList.size() > 0">
                and a.currentNodeType  in
                <foreach collection="currentNodeTypeList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="arcBrandIdList != null and arcBrandIdList.size() > 0">
                and a.REQUESTID  in
                (
                    SELECT
                    fm545.REQUESTID
                    FROM
                    formtable_main_545  fm545
                    LEFT JOIN formtable_main_545_dt1 fm545dt1 on fm545.ID = fm545dt1.mainid
                    LEFT join c_store_new csn on csn.id =fm545dt1.dcmc
                    LEFT JOIN C_CUSTOMER cc ON csn.C_CUSTOMER_ID = cc.id
                    WHERE cc.C_ARCBRAND_ID   in
                    <foreach collection="arcBrandIdList" item="id" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                )
            </if>
            <if test="customerId != null and customerId != '' ">
                and  a.REQUESTID in (
                    SELECT
                    fm545.REQUESTID
                    FROM
                    formtable_main_545   fm545
                    LEFT JOIN formtable_main_545_dt1 fm545dt1 on fm545.ID = fm545dt1.mainid
                    LEFT join c_store_new csn on csn.id =fm545dt1.dcmc
                    LEFT JOIN C_CUSTOMER cc ON csn.C_CUSTOMER_ID = cc.id
                    WHERE cc.id =#{customerId}
                )
            </if>
        ) UNION(
            SELECT
                a.REQUESTID AS request_id,
                wb.workflowname AS TYPE,
                a.requestname AS title,
                a.currentnodetype AS current_node_type,
                b1.lastname AS create_by,
                a.createdate AS create_date
            FROM
                workflow_requestbase a
                    LEFT JOIN HrmResource b1 ON a.creater = b1.id
                    LEFT JOIN workflow_base wb ON a.workflowid = wb.id
                    LEFT JOIN formtable_main_790 fm790 ON fm790.REQUESTID = a.REQUESTID
                    LEFT JOIN C_CUSTOMER cc ON fm790.bjjxs = TO_CHAR( cc.id )
            WHERE
                fm790.hdfqgs = 1
              AND a.creatertype = 0
            AND wb.workflowname IN ( 'ZYGL-09、直营/经销-（客户）销售及会员活动申请流程' )
            <if test="title != null and title != '' ">
                and a.requestname = #{title}
            </if>
            <if test="typeIdList != null and typeIdList.size() > 0">
                and wb.id in
                <foreach collection="typeIdList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="currentNodeTypeList != null and currentNodeTypeList.size() > 0">
                and a.currentNodeType in
                <foreach collection="currentNodeTypeList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="arcBrandIdList != null and arcBrandIdList.size() > 0">
                AND cc.C_ARCBRAND_ID in
                <foreach collection="arcBrandIdList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="customerId != null and customerId != '' ">
                AND cc.id =#{customerId}
            </if>
        )

    </select>
    <select id="getXsCustomerWithBrand" resultType="com.jnby.infrastructure.oa.model.CustomerWithBrand">
        (
        SELECT
            fm469.REQUESTID AS request_id,
            cc.C_ARCBRAND_ID,
            cc.id AS customer_id,
            cc.name AS customer_name
        FROM
            formtable_main_469 fm469
            LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm469.bjjxs
        where fm469.REQUESTID in
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        )union(
        SELECT
        fm457.REQUESTID AS request_id,
        cc.C_ARCBRAND_ID,
        cc.id AS customer_id,
        cc.name AS customer_name
        FROM
        formtable_main_457 fm457
        LEFT JOIN formtable_main_457_dt1 fm457dt1 on fm457dt1.mainid=fm457.id
        LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm457dt1.bjjxs
        where fm457.REQUESTID in
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        )union(
        SELECT
        fm457.REQUESTID AS request_id,
        cc.C_ARCBRAND_ID,
        cc.id AS customer_id,
        cc.name AS customer_name
        FROM
        formtable_main_457 fm457
        LEFT JOIN formtable_main_457_dt2 fm457dt2 on fm457dt2.mainid=fm457.id
        LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm457dt2.bjjxs1
        where fm457.REQUESTID in
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        )union(
        SELECT
        fm422.REQUESTID AS request_id,
        cc.C_ARCBRAND_ID,
        cc.id AS customer_id,
        cc.name AS customer_name
        FROM
        formtable_main_422 fm422
        LEFT JOIN formtable_main_422_dt1 fm422dt1  on fm422dt1.mainid=fm422.id
        LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm422dt1.SSJXS
        where fm422.REQUESTID in
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        )union(
        SELECT
        fm423.REQUESTID AS request_id,
        cc.C_ARCBRAND_ID,
        cc.id AS customer_id,
        cc.name AS customer_name
        FROM
        formtable_main_423 fm423
        LEFT JOIN formtable_main_423_dt1 fm423dt1  on fm423dt1.mainid=fm423.id
        LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm423dt1.bjjxs
        where fm423.REQUESTID in
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        )union(
        SELECT
        fm443.REQUESTID AS request_id,
        cc.C_ARCBRAND_ID,
        cc.id AS customer_id,
        cc.name AS customer_name
        FROM
        formtable_main_443 fm443
        LEFT JOIN C_CUSTOMER cc ON TO_CHAR(cc.id) = fm443.bjjxs

        where fm443.REQUESTID in
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        )union(
        SELECT
        fm545.REQUESTID AS request_id,
        cc.C_ARCBRAND_ID,
        cc.id AS customer_id,
        cc.name AS customer_name
        FROM
        formtable_main_545   fm545
        LEFT JOIN formtable_main_545_dt1 fm545dt1 on fm545.ID = fm545dt1.mainid
        LEFT join c_store_new csn on csn.id =fm545dt1.dcmc
        LEFT JOIN C_CUSTOMER cc ON csn.C_CUSTOMER_ID = cc.id
        WHERE fm545.mdlx=1 and
        fm545.REQUESTID IN
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        )union(
        SELECT
        fm790.REQUESTID AS request_id,
        cc.C_ARCBRAND_ID,
        cc.id AS customer_id,
        cc.name AS customer_name
        FROM
        formtable_main_790  fm790
        LEFT JOIN C_CUSTOMER cc ON fm790.bjjxs = cc.id
        WHERE fm790.hdfqgs=1 and
        fm790.REQUESTID IN
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
        )
    </select>


    <resultMap id="CommonBaseResultMap" type="com.jnby.infrastructure.oa.model.WorkCommonEntity">
        <result column="REQUEST_ID" property="id"/>
        <result column="REQUEST_ID" property="requestId"/>
        <result column="REQUEST_NAME" property="requestName"/>
        <result column="current_node_type" property="currentNodeType"/>
        <result column="work_flow_id" property="workFlowId"/>
        <result column="work_flow_name" property="workFlowName"/>
        <result column="create_by" property="createBy"/>
        <result column="create_id" property="createId"/>
        <result column="CREATE_TYPE" property="createType"/>

        <result column="CREATE_DATE" property="createDate"/>
        <result column="CREATE_TIME" property="createTime"/>


    </resultMap>


    <select id="selectFromIdByRequestId" resultType="java.lang.String">
        SELECT
        wb.formid
        FROM
        workflow_requestbase a
        LEFT JOIN workflow_base wb ON a.workflowid = wb.id
        where  a.REQUESTID  =#{id}
    </select>


    <!--查询 经销商合同-->
    <select id="selectNeedHtList" resultMap="CommonBaseResultMap">
        SELECT
        a.REQUESTID as REQUEST_ID,
        a.requestname as REQUEST_NAME,
        a.currentnodetype as current_node_type,
        a.createdate as create_date,
        a.createtime as create_time,
        a.workflowid as work_flow_id,
        a.creater as create_id,
        a.creatertype as CREATE_TYPE,
        wb.workflowname as work_flow_name,
        b1.lastname AS create_by
        FROM
        workflow_requestbase a
        LEFT JOIN HrmResource b1 ON a.creater = b1.id
        LEFT JOIN workflow_base wb ON a.workflowid = wb.id
        LEFT JOIN formtable_main_42 fm42 ON a.REQUESTID = fm42.REQUESTID
        WHERE
        a.creatertype = 0
        AND a.workflowid IN ( SELECT id FROM workflow_base WHERE formid IN ( '-42' ) )
        <!--htqk1 不等于空的时候表示是经销业务-->
        and fm42.htqk1 is not null
        <if test="id != null and id != '' ">
            and a.REQUESTID  =#{id}
        </if>
    </select>


    <select id="selectNeedZrList" resultMap="CommonBaseResultMap">
        SELECT
        a.REQUESTID as REQUEST_ID,
        a.requestname as REQUEST_NAME,
        a.currentnodetype as current_node_type,
        a.createdate as create_date,
        a.createtime as create_time,
        a.workflowid as work_flow_id,
        a.creater as create_id,
        a.creatertype as CREATE_TYPE,
        wb.workflowname as work_flow_name,
        b1.lastname AS create_by
        FROM
        workflow_requestbase a
        LEFT JOIN HrmResource b1 ON a.creater = b1.id
        LEFT JOIN workflow_base wb ON a.workflowid = wb.id
        WHERE
         a.creatertype = 0
        AND a.workflowid IN ( SELECT id FROM workflow_base WHERE formid IN ( '-853' ) )
        <if test="id != null and id != '' ">
            and a.REQUESTID  =#{id}
        </if>

    </select>
    <select id="selectNeedDcList" resultMap="CommonBaseResultMap">
        SELECT
        a.REQUESTID as REQUEST_ID,
        a.requestname as REQUEST_NAME,
        a.currentnodetype as current_node_type,
        a.createdate as create_date,
        a.createtime as create_time,
        a.workflowid as work_flow_id,
        a.creater as create_id,
        a.creatertype as CREATE_TYPE,
        wb.workflowname as work_flow_name,
        b1.lastname AS create_by
        FROM
        workflow_requestbase a
        LEFT JOIN HrmResource b1 ON a.creater = b1.id
        LEFT JOIN workflow_base wb ON a.workflowid = wb.id
        WHERE
        a.creatertype = 0
        AND a.workflowid IN ( SELECT id FROM workflow_base WHERE formid IN ('-53','-261') )
        <if test="id != null and id != '' ">
            and a.REQUESTID  =#{id}
        </if>
    </select>
    <select id="selectNeedJyList" resultMap="CommonBaseResultMap">
        SELECT
        a.REQUESTID as REQUEST_ID,
        a.requestname as REQUEST_NAME,
        a.currentnodetype as current_node_type,
        a.createdate as create_date,
        a.createtime as create_time,
        a.workflowid as work_flow_id,
        a.creater as create_id,
        a.creatertype as CREATE_TYPE,
        wb.workflowname as work_flow_name,
        b1.lastname AS create_by
        FROM
        workflow_requestbase a
        LEFT JOIN HrmResource b1 ON a.creater = b1.id
        LEFT JOIN workflow_base wb ON a.workflowid = wb.id
        WHERE
        a.creatertype = 0
        AND a.workflowid IN ( SELECT id FROM workflow_base WHERE formid IN ('-48','-88') )
        <if test="id != null and id != '' ">
            and a.REQUESTID  =#{id}
        </if>
    </select>
    <select id="selectNeedZxList" resultMap="CommonBaseResultMap">
        SELECT
        a.REQUESTID as REQUEST_ID,
        a.requestname as REQUEST_NAME,
        a.currentnodetype as current_node_type,
        a.createdate as create_date,
        a.createtime as create_time,
        a.workflowid as work_flow_id,
        a.creater as create_id,
        a.creatertype as CREATE_TYPE,
        wb.workflowname as work_flow_name,
        b1.lastname AS create_by
        FROM
        workflow_requestbase a
        LEFT JOIN HrmResource b1 ON a.creater = b1.id
        LEFT JOIN workflow_base wb ON a.workflowid = wb.id
        WHERE
        a.creatertype = 0
        AND a.workflowid IN ( SELECT id FROM workflow_base WHERE formid IN ('-26') )
        <if test="id != null and id != '' ">
            and a.REQUESTID  =#{id}
        </if>

    </select>
    <select id="selectNeedXsList" resultMap="CommonBaseResultMap">
        SELECT
        a.REQUESTID as REQUEST_ID,
        a.requestname as REQUEST_NAME,
        a.currentnodetype as current_node_type,
        a.createdate as create_date,
        a.createtime as create_time,
        a.workflowid as work_flow_id,
        a.creater as create_id,
        a.creatertype as CREATE_TYPE,
        wb.workflowname as work_flow_name,
        b1.lastname AS create_by
        FROM
        workflow_requestbase a
        LEFT JOIN HrmResource b1 ON a.creater = b1.id
        LEFT JOIN workflow_base wb ON a.workflowid = wb.id
        WHERE
        a.creatertype = 0
        AND a.workflowid IN (
            SELECT id FROM workflow_base WHERE formid IN ('-469','-457','-422','-423','-443','-545','-790') )
        <if test="id != null and id != '' ">
            and a.REQUESTID  =#{id}
        </if>

    </select>




    <select id="selectNeedCwList" resultMap="CommonBaseResultMap">
        SELECT
        a.REQUESTID as REQUEST_ID,
        a.requestname as REQUEST_NAME,
        a.currentnodetype as current_node_type,
        a.createdate as create_date,
        a.createtime as create_time,
        a.workflowid as work_flow_id,
        a.creater as create_id,
        a.creatertype as CREATE_TYPE,
        wb.workflowname as work_flow_name,
        b1.lastname AS create_by
        FROM
        workflow_requestbase a
        LEFT JOIN HrmResource b1 ON a.creater = b1.id
        LEFT JOIN workflow_base wb ON a.workflowid = wb.id
        WHERE
        a.creatertype = 0
        AND a.workflowid IN (
        SELECT id FROM workflow_base WHERE formid IN ('-25','-36','-278','-756','-471') )
        <if test="id != null and id != '' ">
            and a.REQUESTID  =#{id}
        </if>

    </select>


    <select id="getWorkFlowBaseListByFormIds" resultType="com.jnby.infrastructure.oa.model.WorkflowBase">
        select id ,workflowname from workflow_base wb where formid
        in
        <foreach collection="formIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
    </select>


    <resultMap id="FormBaseResultMap" type="com.jnby.infrastructure.oa.model.WorkBaseWithFormEntity">
        <result column="REQUEST_ID" property="requestId"/>
        <result column="FORM_ID" property="formId"/>
    </resultMap>

    <select id="selectNeedFormList" resultMap="FormBaseResultMap">
        SELECT
        a.REQUESTID AS REQUEST_ID,
        wb.formid AS form_id
        FROM
        workflow_requestbase a
        LEFT JOIN workflow_base wb ON a.workflowid = wb.id
        WHERE
        a.creatertype = 0
        AND a.workflowid IN (
             SELECT id FROM workflow_base WHERE formid IN
                  <foreach collection="formIds" separator="," item="formId" close=")" open="(">
                      #{formId}
                  </foreach>
        )
        and a.LASTOPERATEDATE IN
        <foreach collection="days" separator="," item="day" close=")" open="(">
            #{day}
        </foreach>
    </select>


</mapper>
