<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.oa.mapper.FormtableMain48Mapper">

    <select id="selectByBjjxsmcs" resultType="com.jnby.dto.oa.FormattableMain48Dto">

        select htjsrq as  jysj2 , jsxbjjd as bjjxsmc from formtable_main_42 where jsxbjjd in
                                                            <foreach collection="bjjxsmcs" separator="," item="item" close=")" open="(">
                                                                #{item}
                                                            </foreach>
                                                            and  htqk1 =4  and htjsrq is not null
                                                            order by htjsrq desc

    </select>



    <select id="selectById" resultType="com.jnby.dto.oa.FormattableMain48Dto">

        select  jsxbjjd as bjjxsmc from formtable_main_42 where requestid = #{requestId}

    </select>



    <select id="selectByBjjxsId" resultType="com.jnby.dto.oa.FormattableMain48Dto">

        select  bjjxsmc as bjjxsmc , sfzzy , jyyy  from formtable_main_48 where bjjxsmc = #{customerId}

    </select>


</mapper>
