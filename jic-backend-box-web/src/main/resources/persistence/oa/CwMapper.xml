<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.oa.mapper.CwMapper">

    <resultMap id="CbBaseResultMap" type="com.jnby.infrastructure.oa.model.CustomerWithBrand">
        <result column="request_id" jdbcType="VARCHAR" property="requestId"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="customer_id" jdbcType="VARCHAR" property="customerId"/>
        <result column="arc_brand_id" jdbcType="VARCHAR" property="arcBrandId"/>
    </resultMap>



    <select id="getCwCustomerWithBrand" resultMap="CbBaseResultMap">
        (
            SELECT
            'fm36-' ||  fm36.id as id,
            fm36.REQUESTID AS request_id,
            cc.C_ARCBRAND_ID as arc_brand_id,
            cc.id AS customer_id,
            cc.name AS customer_name
            FROM
            formtable_main_36 fm36
            LEFT JOIN C_CUSTOMER cc ON  TO_CHAR(cc.id) = fm36.jxs
            WHERE
            fm36.REQUESTID in
            <foreach collection="requestIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        )union(
            SELECT
            'fm278-' ||  fm278.id as id,
            fm278.REQUESTID as request_id,
            cc.C_ARCBRAND_ID as arc_brand_id,
            cc.id AS customer_id,
            cc.name AS customer_name
            FROM
            formtable_main_278 fm278
            LEFT JOIN C_CUSTOMER cc ON TO_CHAR( cc.id ) = fm278.jxs
            WHERE
            fm278.REQUESTID in
            <foreach collection="requestIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        )union(
                SELECT
                'fm756-' ||  fm756.id as id,
                fm756.REQUESTID AS request_id,
                cc.C_ARCBRAND_ID as arc_brand_id,
                cc.id AS customer_id,
                cc.name AS customer_name
                FROM
                formtable_main_756 fm756
                LEFT JOIN C_CUSTOMER cc ON TO_CHAR( cc.id )= fm756.bjjxs
                WHERE
                fm756.REQUESTID in
                <foreach collection="requestIds" separator="," item="item" close=")" open="(">
                    #{item}
                </foreach>
        )
    </select>



    <select id="getCwCustomerWithBrand25" resultMap="CbBaseResultMap">
        SELECT
        'fm25-' ||  fm25.id as id,
        fm25.REQUESTID AS request_id,
        cc.C_ARCBRAND_ID as arc_brand_id,
        cc.id AS customer_id,
        cc.name AS customer_name
        FROM
        formtable_main_25 fm25
        LEFT JOIN c_store_new csn ON csn.id = fm25.dianpmc
        LEFT JOIN C_CUSTOMER cc ON cc.id = csn.C_CUSTOMER_ID
        where
        fm25.REQUESTID in
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
    </select>




    <select id="getCwCustomerWithBrand471" resultMap="CbBaseResultMap">
        SELECT
        'fm471dt1-' ||  fm471dt1.id as id,
        fm471.REQUESTID AS request_id,
        cc.C_ARCBRAND_ID as arc_brand_id,
        cc.id AS customer_id,
        cc.name AS customer_name
        FROM
        formtable_main_471_dt1 fm471dt1
        LEFT JOIN  formtable_main_471 fm471   ON fm471.id = fm471dt1.mainid
        LEFT JOIN C_CUSTOMER cc ON TO_CHAR( cc.id ) = fm471dt1.jxs
        WHERE
        fm471.REQUESTID in
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
    </select>
</mapper>
