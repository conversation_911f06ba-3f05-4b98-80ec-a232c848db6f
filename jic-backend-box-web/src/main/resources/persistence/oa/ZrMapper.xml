<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.oa.mapper.ZrMapper">


    <resultMap id="CbBaseResultMap" type="com.jnby.infrastructure.oa.model.CustomerWithBrand">
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="request_id" jdbcType="VARCHAR" property="requestId"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="customer_id" jdbcType="VARCHAR" property="customerId"/>
        <result column="arc_brand_id" jdbcType="VARCHAR" property="arcBrandId"/>
    </resultMap>



    <select id="getZrCustomerWithBrand"  resultMap="CbBaseResultMap">
        <!-- formtable_main_853正式 formtable_main_846测试-->
        SELECT
        'fm853-' || fm853.id AS id,
        fm853.REQUESTID AS request_id,
        cc.C_ARCBRAND_ID as arc_brand_id,
        cc.id AS customer_id,
        cc.name AS customer_name
        FROM
        formtable_main_853 fm853
        LEFT JOIN C_CUSTOMER cc ON fm853.bjjxs = cc.id
        where
        fm853.REQUESTID in
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
    </select>


    <resultMap id="413BaseResultMap" type="com.jnby.infrastructure.oa.model.Fm853WithMain413">
        <result column="request_id" jdbcType="VARCHAR" property="requestId"/>
        <result column="brand_name" jdbcType="VARCHAR" property="brandName"/>
    </resultMap>


    <select id="getBrandWithRequestId"  resultMap="413BaseResultMap">
        SELECT
            fm853.REQUESTID AS request_id,
            fm413.PINPAI as brand_name
        FROM
            formtable_main_853 fm853
                LEFT JOIN formtable_main_413 fm413 on fm853.pp=fm413.id
        WHERE
            fm413.sfqy=0 and
        fm853.REQUESTID in
        <foreach collection="requestIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
    </select>
</mapper>
