<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.oa.mapper.DcMapper">

    <select id="select261ByRequestId" resultType="com.jnby.dto.oa.FormTableMain261Dto">
        select id, suoshubjjxs
        from formtable_main_261
        where REQUESTID = #{requestId}
    </select>
    <select id="select53ByRequestId" resultType="com.jnby.dto.oa.FormTableMain53Dto">
        select id, bojunjxs
        from formtable_main_53
        where REQUESTID = #{requestId}
    </select>


    <resultMap id="CbBaseResultMap" type="com.jnby.infrastructure.oa.model.CustomerWithBrand">
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="request_id" jdbcType="VARCHAR" property="requestId"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="customer_id" jdbcType="VARCHAR" property="customerId"/>
        <result column="arc_brand_id" jdbcType="VARCHAR" property="arcBrandId"/>
    </resultMap>


    <select id="getDcCustomerWithBrand" resultMap="CbBaseResultMap">
        (
            SELECT
           'fm261-'|| fm261.id as id,
            fm261.REQUESTID as request_id,
            cc.id AS customer_id,
            cc.name AS customer_name,
            cc.C_ARCBRAND_ID as arc_brand_id
            FROM
            formtable_main_261 fm261
            LEFT JOIN C_CUSTOMER cc ON fm261.suoshubjjxs = cc.id
            where fm261.REQUESTID in
            <foreach collection="requestIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        )
        union
        (
            SELECT
            'fm53-'||fm53.id as id,
            fm53.REQUESTID as request_id,
            cc2.id AS customer_id,
            cc2.name AS customer_name,
            cc2.C_ARCBRAND_ID as arc_brand_id
            from
            formtable_main_53 fm53
            LEFT JOIN C_CUSTOMER cc2 ON fm53.bojunjxs = cc2.id
            where fm53.REQUESTID in
            <foreach collection="requestIds" separator="," item="item" close=")" open="(">
                #{item}
            </foreach>
        )
    </select>

</mapper>
