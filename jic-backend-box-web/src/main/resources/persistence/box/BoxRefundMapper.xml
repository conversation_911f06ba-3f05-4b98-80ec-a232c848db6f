<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.BoxRefundMapper">
  <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.BoxRefund">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="REFUND_SN" jdbcType="VARCHAR" property="refundSn" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="ORDER_ID" jdbcType="VARCHAR" property="orderId" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
    <result column="MEMO" jdbcType="VARCHAR" property="memo" />
    <result column="TRACKING_NUMBER" jdbcType="VARCHAR" property="trackingNumber" />
    <result column="REFUND_AMOUNT" jdbcType="FLOAT" property="refundAmount" />
    <result column="SEND_LOGISTICS_ID" jdbcType="VARCHAR" property="sendLogisticsId" />
    <result column="GET_DATE" jdbcType="VARCHAR" property="getDate" />
    <result column="SEND_BACK" jdbcType="DECIMAL" property="sendBack" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="REFUND_REMARK" jdbcType="VARCHAR" property="refundRemark" />
    <result column="REFUND_PHOTOS" jdbcType="VARCHAR" property="refundPhotos" />
    <result column="AUTO_AMOUNT" jdbcType="DECIMAL" property="autoAmount" />
    <result column="WEIMO_REFUND_ID" jdbcType="VARCHAR" property="weimoRefundId" />
    <result column="REFUND_BALANCE" jdbcType="DECIMAL" property="refundBalance" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, REFUND_SN, UNIONID, ORDER_ID, STATUS, MEMO, TRACKING_NUMBER, REFUND_AMOUNT, SEND_LOGISTICS_ID,
    GET_DATE, SEND_BACK, CREATE_TIME, UPDATE_TIME, REFUND_REMARK, REFUND_PHOTOS, AUTO_AMOUNT,WEIMO_REFUND_ID,REFUND_BALANCE
  </sql>

  <select id="findByBoxSn" parameterType="java.lang.String" resultMap="BaseResultMap">

    select
    <include refid="Base_Column_List" />
    from BOX_REFUND
    where order_id in ( select id from order_ where BOX_SN = #{boxSn,jdbcType=VARCHAR})

  </select>
</mapper>
