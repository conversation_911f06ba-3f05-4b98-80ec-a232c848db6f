<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.CrmCustomerLabelMapper">

    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.CrmCustomerLabel">
        <id property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="crmCustomerMainId" column="CRM_CUSTOMER_MAIN_ID" jdbcType="VARCHAR"/>
        <result property="labelName" column="LABEL_NAME" jdbcType="VARCHAR"/>
        <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="isDel" column="IS_DEL" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,CRM_CUSTOMER_MAIN_ID,LABEL_NAME,
        CREATE_TIME,UPDATE_TIME,IS_DEL
    </sql>
    <select id="selectByCrmCustomerMainIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from jnby.CRM_CUSTOMER_LABEL
        where is_del =  0 and CRM_CUSTOMER_MAIN_ID  in
        <foreach collection="crmCustomerMainIds" separator="," item="item" close=")" open="(">
            #{item}
        </foreach>
    </select>

</mapper>
