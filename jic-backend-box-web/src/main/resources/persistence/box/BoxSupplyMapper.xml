<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.BoxSupplyMapper">
  <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.BoxSupply">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="BOX_ID" jdbcType="VARCHAR" property="boxId" />
    <result column="SUPPLY_SN" jdbcType="VARCHAR" property="supplySn" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="TRACK_NUMBER" jdbcType="VARCHAR" property="trackNumber" />
    <result column="SUPPLY_COUNT" jdbcType="DECIMAL" property="supplyCount" />
    <result column="SUPPLY_PRICE" jdbcType="VARCHAR" property="supplyPrice" />
    <result column="SUPPLY_REASON" jdbcType="VARCHAR" property="supplyReason" />
    <result column="LOGISTICS_ID" jdbcType="VARCHAR" property="logisticsId" />
    <result column="CUSTOMER_ID" jdbcType="VARCHAR" property="customerId" />
    <result column="CUSTOMER_ID" jdbcType="VARCHAR" property="customerId" />
    <result column="IF_VIR_DELIVERY" jdbcType="INTEGER" property="ifVirDelivery" />
    <result column="SIGN_IN_TIME" jdbcType="TIMESTAMP" property="signInTime" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, BOX_ID, SUPPLY_SN, CREATE_TIME, STATUS, UPDATE_TIME, TRACK_NUMBER,SUPPLY_COUNT,SUPPLY_PRICE,SUPPLY_REASON
    ,LOGISTICS_ID,CUSTOMER_ID,IF_VIR_DELIVERY, SIGN_IN_TIME
  </sql>

  <select id="findByBoxId" parameterType="java.lang.String" resultMap="BaseResultMap">

    select
    <include refid="Base_Column_List" />
    from BOX_SUPPLY
    where BOX_ID = #{boxId,jdbcType=VARCHAR}
    order by supply_sn ASC

  </select>
</mapper>
