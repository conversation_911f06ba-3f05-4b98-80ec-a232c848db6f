<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.OrderMapper">
  <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.Order">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="BOX_SN" jdbcType="VARCHAR" property="boxSn" />
    <result column="ORDER_SN" jdbcType="VARCHAR" property="orderSn" />
    <result column="SERVICE_SN" jdbcType="VARCHAR" property="serviceSn" />
    <result column="SERIAL_NUMBER" jdbcType="VARCHAR" property="serialNumber" />
    <result column="DAYSTR" jdbcType="VARCHAR" property="daystr" />
    <result column="CUSTOMER_ID" jdbcType="VARCHAR" property="customerId" />
    <result column="TRADE_NO" jdbcType="VARCHAR" property="tradeNo" />
    <result column="WECHAT_TRANSACTION_ID" jdbcType="VARCHAR" property="wechatTransactionId" />
    <result column="ORDER_STATUS" jdbcType="DECIMAL" property="orderStatus" />
    <result column="PAID_AMOUNT" jdbcType="FLOAT" property="paidAmount" />
    <result column="PRODUCT_TOTAL_PRICE" jdbcType="FLOAT" property="productTotalPrice" />
    <result column="DISCOUNT_AMOUNT" jdbcType="FLOAT" property="discountAmount" />
    <result column="PRODUCT_DISCOUNT" jdbcType="FLOAT" property="productDiscount" />
    <result column="VIP_DISCOUNT" jdbcType="FLOAT" property="vipDiscount" />
    <result column="PRODUCT_TOTAL_QUANTITY" jdbcType="VARCHAR" property="productTotalQuantity" />
    <result column="PAYMENT_TYPE" jdbcType="VARCHAR" property="paymentType" />
    <result column="COIN" jdbcType="FLOAT" property="coin" />
    <result column="TRACKING_NUMBER" jdbcType="VARCHAR" property="trackingNumber" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="VOUCHERS_NO" jdbcType="VARCHAR" property="vouchersNo" />
    <result column="VOU_DIS" jdbcType="FLOAT" property="vouDis" />
    <result column="IS_YD" jdbcType="DECIMAL" property="isYd" />
    <result column="SEND_TIME" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="INTEGRAL" jdbcType="DECIMAL" property="integral" />
    <result column="IS_DS" jdbcType="DECIMAL" property="isDs" />
    <result column="APPID" jdbcType="VARCHAR" property="appid" />
    <result column="ISRETAIL" jdbcType="VARCHAR" property="isretail" />
    <result column="TYPE" jdbcType="DECIMAL" property="type" />
    <result column="IS_EB" jdbcType="VARCHAR" property="isEb" />
    <result column="FLAG" jdbcType="DECIMAL" property="flag" />
    <result column="IF_HAVE_UN_REFUND_ORDER" jdbcType="INTEGER" property="ifHaveUnRefundOrder" />
    <result column="EB_EXPRESS_SYNC_FINISH" jdbcType="INTEGER" property="ebExpressSyncFinish" />
    <result column="PAY_CHANNEL" jdbcType="VARCHAR" property="payChannel" />
    <result column="SQB_SN" jdbcType="VARCHAR" property="sqbSn" />

    <result column="WEIMOB_ORDER_SN" jdbcType="VARCHAR" property="weimobOrderSn" />
    <result column="APP_ID" jdbcType="VARCHAR" property="appId" />
    <result column="CALC_ID" jdbcType="VARCHAR" property="calcId" />
    <result column="BALANCE_AMT" jdbcType="DECIMAL" property="balanceAmt" />
    <result column="SHOP_VOU_AMT" jdbcType="DECIMAL" property="shopVouAmt" />

    <result column="MERCHANT_CODE" jdbcType="DECIMAL" property="merchantCode" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, BOX_SN, ORDER_SN, SERVICE_SN, SERIAL_NUMBER, DAYSTR, CUSTOMER_ID, TRADE_NO, WECHAT_TRANSACTION_ID,
    ORDER_STATUS, PAID_AMOUNT, PRODUCT_TOTAL_PRICE, DISCOUNT_AMOUNT, PRODUCT_DISCOUNT,
    VIP_DISCOUNT, PRODUCT_TOTAL_QUANTITY, PAYMENT_TYPE, COIN, TRACKING_NUMBER, CREATE_TIME,
    UPDATE_TIME, VOUCHERS_NO, VOU_DIS, IS_YD, SEND_TIME, INTEGRAL, IS_DS, APPID, ISRETAIL,
    TYPE, IS_EB, FLAG,IF_HAVE_UN_REFUND_ORDER,EB_EXPRESS_SYNC_FINISH,PAY_CHANNEL,SQB_SN,WEIMOB_ORDER_SN,APP_ID,CALC_ID,BALANCE_AMT,SHOP_VOU_AMT,MERCHANT_CODE
  </sql>

  <sql id="Base_Column_List_alien">
    a.ID, a.BOX_SN, a.ORDER_SN, a.SERVICE_SN, a.SERIAL_NUMBER, a.DAYSTR, a.CUSTOMER_ID, a.TRADE_NO, a.WECHAT_TRANSACTION_ID,
    a.ORDER_STATUS,a.PAID_AMOUNT, a.PRODUCT_TOTAL_PRICE, a.DISCOUNT_AMOUNT, a.PRODUCT_DISCOUNT,
    a.VIP_DISCOUNT, a.PRODUCT_TOTAL_QUANTITY, a.PAYMENT_TYPE, a.COIN, a.TRACKING_NUMBER, a.CREATE_TIME,
    a.UPDATE_TIME, a.VOUCHERS_NO, a.VOU_DIS, a.IS_YD, a.SEND_TIME, a.INTEGRAL, a.IS_DS, a.APPID, a.ISRETAIL,
    a.TYPE, a.IS_EB, a.FLAG,a.IF_HAVE_UN_REFUND_ORDER,a.EB_EXPRESS_SYNC_FINISH,PAY_CHANNEL,SQB_SN,a.MERCHANT_CODE
  </sql>
  <select id="selectListBySelective" parameterType="com.jnby.infrastructure.box.model.Order" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ORDER_
    <where>
      <if test="customerId != null">
        and customer_id = #{customerId}
      </if>
      <if test="isYd != null">
        and is_yd = #{isYd}
      </if>
      <if test="boxSn != null">
        and BOX_SN = #{boxSn,jdbcType=VARCHAR}
      </if>
      <if test="orderSn != null">
        and ORDER_SN = #{orderSn,jdbcType=VARCHAR}
      </if>
      <if test="orderStatus != null">
        and order_status = #{orderStatus}
      </if>
      <if test="type != null">
        and type = #{type}
      </if>
      <if test="createTime != null">
        and create_time >= #{createTime,jdbcType=VARCHAR}
      </if>
      <if test="daystr != null">
        and daystr = #{daystr}
      </if>
      <if test="serialNumber != null">
        and serial_number = #{serialNumber}
      </if>
      <if test="weimobOrderSn != null">
        and WEIMOB_ORDER_SN=#{weimobOrderSn,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
  <select id="selectOrderByBoxSns"  resultMap="BaseResultMap">
    select <include refid="Base_Column_List"></include> from ORDER_
      where BOX_SN in
      <foreach collection="list" separator="," open="(" close=")" item="item">
        #{item}
      </foreach>
  </select>
  <select id="selectWebPosOrderByTradeNo" resultType="java.math.BigDecimal">

        select paid_amount from webpos.order_ where wechat_transaction_id = #{tradeNo}

  </select>
  
<!--  <select id="selectIncrementCountByTime" resultType="java.lang.Integer">-->
<!--    SELECT COUNT(*) FROM ORDER_  WHERE-->
<!--      (-->
<!--        create_time BETWEEN to_date(#{fromDate},'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate},'YYYY-MM-DD HH24:MI:SS')-->
<!--        )-->
<!--                                              OR-->
<!--      (-->
<!--        update_time IS NOT null-->
<!--          AND-->
<!--        update_time BETWEEN to_date(#{fromDate},'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate},'YYYY-MM-DD HH24:MI:SS')-->
<!--        )-->
<!--  </select>-->

<!--  <select id="selectIncrementListByTime" resultMap="BaseResultMap">-->
<!--    SELECT * FROM (-->
<!--                    SELECT rownum r, t.*-->
<!--                    FROM ORDER_ t-->
<!--                    WHERE (-->
<!--                      create_time BETWEEN to_date(#{fromDate}, 'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate}, 'YYYY-MM-DD HH24:MI:SS')-->
<!--                      )-->
<!--                       OR (-->
<!--                      update_time IS NOT null-->
<!--                        AND-->
<!--                      update_time BETWEEN to_date(#{fromDate}, 'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate}, 'YYYY-MM-DD HH24:MI:SS')-->
<!--                      )-->
<!--                    ORDER BY update_time ASC-->
<!--                  ) WHERE r BETWEEN #{start} AND #{end}-->
<!--  </select>-->

  <select id="selectIncrementCountByTime" resultType="java.lang.Integer">
    SELECT COUNT(*) FROM ORDER_  WHERE
      (
        create_time BETWEEN to_date(#{fromDate},'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate},'YYYY-MM-DD HH24:MI:SS')
        )
                                    OR
      (
        update_time BETWEEN to_date(#{fromDate},'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate},'YYYY-MM-DD HH24:MI:SS')
        )
  </select>

  <select id="selectIncrementListByTime" resultMap="BaseResultMap">
    SELECT * FROM (
                    SELECT rownum r, t.*
                    FROM ORDER_ t
                    WHERE (
                      update_time BETWEEN to_date(#{fromDate}, 'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate}, 'YYYY-MM-DD HH24:MI:SS')
                      )
                    ORDER BY update_time ASC
                  ) WHERE r BETWEEN #{start} AND #{end}
  </select>
</mapper>
