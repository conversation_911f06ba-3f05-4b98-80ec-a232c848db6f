<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.OaWorkflowMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.OaWorkflow">
        <id column="ID" property="id" />
        <result column="REQUEST_ID" property="requestId" />
        <result column="REQUEST_NAME" property="requestName" />
        <result column="CURRENT_NODE_TYPE" property="currentNodeType" />
        <result column="WORK_FLOW_ID" property="workFlowId" />
        <result column="CREATE_ID" property="createId" />
        <result column="CREATE_TYPE" property="createType" />
        <result column="WORK_FLOW_NAME" property="workFlowName" />
        <result column="MAIN_ID" property="mainId" />

        <result column="TAB_TYPE" property="tabType" />
        <result column="SYN_TIME" property="synTime" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="CREATE_BY" property="createBy" />
        <result column="UPDATE_BY" property="updateBy" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="DEL_FLAG" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, REQUEST_ID, REQUEST_NAME, CURRENT_NODE_TYPE, WORK_FLOW_ID, CREATE_ID, CREATE_TYPE, WORK_FLOW_NAME, MAIN_ID, TAB_TYPE, SYN_TIME, CREATE_TIME, CREATE_BY, UPDATE_BY, UPDATE_TIME, DEL_FLAG
    </sql>


    <resultMap id="ZBaseResultMap" type="com.jnby.infrastructure.box.model.WorkFlowModel">
        <result column="request_name" property="requestName"/>

        <result column="request_id" property="requestId"/>
        <result column="work_flow_name" property="workFlowName"/>

        <result column="customer_name" property="customerName"/>
        <result column="customer_id" property="customerId"/>

        <result column="BRAND_ID" property="brandId"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="current_node_type" property="currentNodeType"/>
    </resultMap>



    <select id="selectCommonList" resultMap="ZBaseResultMap">
        SELECT
        ow.request_id,
        ow.work_flow_name,
        ow.request_name,
        ow.current_node_type,
        ow.create_by,
        ow.create_time,
        ouw.customer_id,
        ouw.customer_name,
        ouw.BRAND_ID
        FROM
        OA_WORKFLOW ow
        LEFT JOIN OA_USER_WORKFLOW ouw ON ow.request_id = ouw.request_id
        WHERE ow.DEL_FLAG=0 and
        ow.CREATE_TYPE = 0 and ow.TAB_TYPE=#{tabType}
        <if test="title != null and title != ''">
            and ow.request_name  like concat('%',concat(#{title},'%'))
        </if>

        <if test="customerIdList != null and customerIdList.size() > 0 ">
            and  ouw.customer_id
            in
            <foreach collection="customerIdList" item="customerId" open="(" close=")" separator=",">
                #{customerId}
            </foreach>
        </if>

        <if test="arcBrandIdList != null and arcBrandIdList.size() > 0">
            and ouw.BRAND_ID   in
            <foreach collection="arcBrandIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>

        <if test="typeIdList != null and typeIdList.size() > 0">
            and ow.WORK_FLOW_ID  in
            <foreach collection="typeIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="currentNodeTypeList != null and currentNodeTypeList.size() > 0">
            and ow.current_node_type  in
            <foreach collection="currentNodeTypeList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>

        <if test="ktStartTime != null and ktStartTime != '' ">
            and ouw.HT_START_TIME>= #{ktStartTime}
        </if>

        <if test="ktEndTime != null and ktEndTime != '' ">
            and  #{ktEndTime} >= ouw.HT_START_TIME
        </if>
        order by ow.CREATE_TIME  desc
    </select>
    <select id="selectCwList" resultMap="ZBaseResultMap">
        SELECT
        ow.request_id,
        ow.work_flow_name,
        ow.request_name,
        ow.current_node_type,
        ow.create_by,
        ow.create_time
        FROM
        OA_WORKFLOW ow
        WHERE  ow.DEL_FLAG=0 and ow.CREATE_TYPE = 0 and ow.TAB_TYPE=#{tabType}
        <if test="title != null and title != ''">
            and ow.request_name like concat('%',concat(#{title},'%'))
        </if>

        <if test="customerIdList != null and customerIdList.size() > 0 ">
            and ow.request_id in (
            select request_id from
            OA_USER_WORKFLOW ouw where ouw.DEL_FLAG=0 and ouw.customer_id
                in
            <foreach collection="customerIdList" item="customerId" open="(" close=")" separator=",">
                #{customerId}
            </foreach>
            )
        </if>

        <if test="arcBrandIdList != null and arcBrandIdList.size() > 0">
            and ow.request_id in (
                 select request_id from
            OA_USER_WORKFLOW ouw where ouw.DEL_FLAG=0 and ouw.BRAND_ID in
            <foreach collection="arcBrandIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
            )
        </if>

        <if test="typeIdList != null and typeIdList.size() > 0">
            and ow.WORK_FLOW_ID  in
            <foreach collection="typeIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="currentNodeTypeList != null and currentNodeTypeList.size() > 0">
            and ow.current_node_type  in
            <foreach collection="currentNodeTypeList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        order by ow.CREATE_TIME  desc
    </select>

    <select id="selectXsList" resultMap="ZBaseResultMap">
        SELECT
        ow.request_id,
        ow.work_flow_name,
        ow.request_name,
        ow.current_node_type,
        ow.create_by,
        ow.create_time
        FROM
        OA_WORKFLOW ow
        WHERE  ow.DEL_FLAG=0 and ow.CREATE_TYPE = 0 and ow.TAB_TYPE=#{tabType}
        <if test="title != null and title != ''">
            and ow.request_name like concat('%',concat(#{title},'%'))
        </if>


        <if test="customerIdList != null and customerIdList.size() > 0 ">
            and ow.request_id in (
            select request_id from
            OA_USER_WORKFLOW ouw where ouw.DEL_FLAG=0 and ouw.customer_id
            in
            <foreach collection="customerIdList" item="customerId" open="(" close=")" separator=",">
                #{customerId}
            </foreach>
            )
        </if>


        <if test="arcBrandIdList != null and arcBrandIdList.size() > 0">
            and ow.request_id in (
            select request_id from
            OA_USER_WORKFLOW ouw where ouw.DEL_FLAG=0 and ouw.BRAND_ID in
            <foreach collection="arcBrandIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
            )
        </if>

        <if test="typeIdList != null and typeIdList.size() > 0">
            and ow.WORK_FLOW_ID  in
            <foreach collection="typeIdList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="currentNodeTypeList != null and currentNodeTypeList.size() > 0">
            and ow.current_node_type  in
            <foreach collection="currentNodeTypeList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>

        order by ow.CREATE_TIME  desc
    </select>
</mapper>