<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.CustomerAskBoxMapper">
  <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.CustomerAskBox">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="LOGISTICS_ID" jdbcType="VARCHAR" property="logisticsId" />
    <result column="CREATE_FAS_ID" jdbcType="VARCHAR" property="createFasId" />
    <result column="SCENE_TAG" jdbcType="VARCHAR" property="sceneTag" />
    <result column="GOODS_TAG" jdbcType="VARCHAR" property="goodsTag" />
    <result column="WEIGHT" jdbcType="VARCHAR" property="weight" />
    <result column="CENNECT_PHONE" jdbcType="VARCHAR" property="cennectPhone" />
    <result column="WECHAT_NUMBER" jdbcType="VARCHAR" property="wechatNumber" />
    <result column="IMGS" jdbcType="VARCHAR" property="imgs" />
    <result column="MSG" jdbcType="VARCHAR" property="msg" />
    <result column="BOX_ID" jdbcType="VARCHAR" property="boxId" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="HEIGHT" jdbcType="VARCHAR" property="height" />
    <result column="BOX_SN" jdbcType="VARCHAR" property="boxSn" />
    <result column="CUS_GENDER" jdbcType="DECIMAL" property="cusGender" />
    <result column="ATTR_VALUE_IDS" jdbcType="VARCHAR" property="attrValueIds" />
    <result column="CONTACT_TYPE" jdbcType="DECIMAL" property="contactType" />
    <result column="SKIP" jdbcType="DECIMAL" property="skip" />
    <result column="PRODUCT_SIZE" jdbcType="VARCHAR" property="productSize" />
    <result column="THEME_ATTR_ID" jdbcType="VARCHAR" property="themeAttrId" />
    <result column="TOT_BUY_AMOUNT" jdbcType="DECIMAL" property="totBuyAmount" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="VERSION" jdbcType="DECIMAL" property="version" />
    <result column="LAST_FASHIONER_ID" jdbcType="VARCHAR" property="lastFashionerId"/>
    <result column="LAST_FASHIONER_NAME" jdbcType="VARCHAR" property="lastFashionerName"/>
    <result column="IS_SALES" jdbcType="VARCHAR" property="isSales"/>
    <result column="IS_CONNECT" jdbcType="DECIMAL" property="isConnect"/>
    <result column="CONNECT_TIME" jdbcType="TIMESTAMP" property="connectTime"/>
    <result column="NO_PERFORMANCE" jdbcType="INTEGER" property="noPerformance"/>
    <result column="SALES_PHONE" jdbcType="VARCHAR" property="salesPhone"/>
    <result column="SUBMIT_SALES_FASHIONER_ID" jdbcType="VARCHAR" property="submitSalesFashionerId"/>
    <result column="ACTIVITY_ID" jdbcType="VARCHAR" property="activityId"/>
    <result column="CONNECT_SUCCESS" jdbcType="DECIMAL" property="connectSuccess"/>
    <result column="SUB_ID" jdbcType="VARCHAR" property="subId"/>
    <result column="SUB_PLAN_ID" jdbcType="VARCHAR" property="subPlanId"/>
    <result column="ASK_TYPE" jdbcType="DECIMAL" property="askType"/>
    <result column="CREATE_BY" jdbcType="DECIMAL" property="createBy"/>
    <result column="CHANNEL_ID" jdbcType="VARCHAR" property="channelId"/>
  </resultMap>
  <sql id="Base_Column_List">
    ID, UNIONID, LOGISTICS_ID, CREATE_FAS_ID, SCENE_TAG, GOODS_TAG, WEIGHT, CENNECT_PHONE,
    WECHAT_NUMBER, IMGS, MSG, BOX_ID, STATUS, CREATE_TIME, UPDATE_TIME, HEIGHT, BOX_SN,
    CUS_GENDER, ATTR_VALUE_IDS, CONTACT_TYPE, PRODUCT_SIZE,THEME_ATTR_ID,TOT_BUY_AMOUNT,REMARK,VERSION,last_fashioner_id,
    last_fashioner_name,is_sales,is_connect,CONNECT_TIME,NO_PERFORMANCE,SALES_PHONE,SUBMIT_SALES_FASHIONER_ID,ACTIVITY_ID,CONNECT_SUCCESS,
    SUB_ID,SUB_PLAN_ID,ASK_TYPE,CREATE_BY,CHANNEL_ID
  </sql>

  <select id="selectListBySelective" parameterType="com.jnby.infrastructure.box.model.CustomerAskBox" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from CUSTOMER_ASK_BOX
    <where>
      <if test="unionid != null">
        and UNIONID = #{unionid,jdbcType=VARCHAR}
      </if>
      <if test="logisticsId != null">
        and LOGISTICS_ID = #{logisticsId,jdbcType=VARCHAR}
      </if>
      <if test="createFasId != null">
        and CREATE_FAS_ID = #{createFasId,jdbcType=VARCHAR}
      </if>
      <if test="cennectPhone != null">
        and CENNECT_PHONE = #{cennectPhone,jdbcType=VARCHAR}
      </if>
      <if test="boxId != null">
        and BOX_ID = #{boxId,jdbcType=VARCHAR}
      </if>
      <if test="status != null">
        and STATUS = #{status,jdbcType=DECIMAL}
      </if>
      <if test="createTime != null">
        and CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null">
        and UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
      </if>
      <if test="height != null">
        and  HEIGHT = #{height,jdbcType=VARCHAR}
      </if>
      <if test="boxSn != null">
        and BOX_SN = #{boxSn,jdbcType=VARCHAR}
      </if>
      <if test="createBy != null and createBy == -1">
        and CREATE_BY != 0
      </if>
      <if test="createBy != null and createBy != -1">
        and CREATE_BY = #{createBy,jdbcType=DECIMAL}
      </if>
      <if test="subId != null">
        and SUB_ID = #{subId,jdbcType=VARCHAR}
      </if>
    </where>
    and status > -1
    order by create_time desc
  </select>

<!--  <select id="selectIncrementCountByTime" resultType="java.lang.Integer">-->
<!--    SELECT COUNT(*) FROM CUSTOMER_ASK_BOX  WHERE-->
<!--      (-->
<!--        create_time BETWEEN to_date(#{fromDate},'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate},'YYYY-MM-DD HH24:MI:SS')-->
<!--        )-->
<!--                                              OR-->
<!--      (-->
<!--        update_time IS NOT null-->
<!--          AND-->
<!--        update_time BETWEEN to_date(#{fromDate},'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate},'YYYY-MM-DD HH24:MI:SS')-->
<!--        )-->
<!--  </select>-->

<!--  <select id="selectIncrementListByTime" resultMap="BaseResultMap">-->
<!--    SELECT * FROM (-->
<!--                    SELECT rownum r, t.*-->
<!--                    FROM CUSTOMER_ASK_BOX t-->
<!--                    WHERE (-->
<!--                      create_time BETWEEN to_date(#{fromDate}, 'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate}, 'YYYY-MM-DD HH24:MI:SS')-->
<!--                      )-->
<!--                       OR (-->
<!--                      update_time IS NOT null-->
<!--                        AND-->
<!--                      update_time BETWEEN to_date(#{fromDate}, 'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate}, 'YYYY-MM-DD HH24:MI:SS')-->
<!--                      )-->
<!--                    ORDER BY update_time ASC-->
<!--                  ) WHERE r BETWEEN #{start} AND #{end}-->
<!--  </select>-->
  <select id="selectIncrementCountByTime" resultType="java.lang.Integer">
    SELECT COUNT(*) FROM CUSTOMER_ASK_BOX  WHERE
      (
        create_time BETWEEN to_date(#{fromDate},'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate},'YYYY-MM-DD HH24:MI:SS')
        )
                                              OR
      (
        update_time BETWEEN to_date(#{fromDate},'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate},'YYYY-MM-DD HH24:MI:SS')
        )
  </select>

  <select id="selectIncrementListByTime" resultMap="BaseResultMap">
    SELECT * FROM (
                    SELECT rownum r, t.*
                    FROM CUSTOMER_ASK_BOX t
                    WHERE (
                      create_time BETWEEN to_date(#{fromDate}, 'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate}, 'YYYY-MM-DD HH24:MI:SS')
                      )
                       OR (
                      update_time BETWEEN to_date(#{fromDate}, 'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate}, 'YYYY-MM-DD HH24:MI:SS')
                      )
                    ORDER BY update_time ASC
                  ) WHERE r BETWEEN #{start} AND #{end}
  </select>
</mapper>
