<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.ShortLinkToolsMapper">

    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.ShortLinkTools">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="weid" column="WEID" jdbcType="VARCHAR"/>
            <result property="pathType" column="PATH_TYPE" jdbcType="VARCHAR"/>
            <result property="pageName" column="PAGE_NAME" jdbcType="VARCHAR"/>
            <result property="paramsSettingType" column="PARAMS_SETTING_TYPE" jdbcType="VARCHAR"/>
            <result property="paramsSettingContent" column="PARAMS_SETTING_CONTENT" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="IS_DEL" jdbcType="DECIMAL"/>
            <result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"/>
            <result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR"/>
            <result property="sysOrgCode" column="SYS_ORG_CODE" jdbcType="VARCHAR"/>
            <result property="effectType" column="EFFECT_TYPE" jdbcType="DECIMAL"/>
            <result property="effectDays" column="EFFECT_DAYS" jdbcType="DECIMAL"/>
            <result property="remark" column="REMARK" jdbcType="VARCHAR"/>
            <result property="path" column="PATH" jdbcType="VARCHAR"/>
            <result property="shortLink" column="SHORT_LINK" jdbcType="VARCHAR"/>

            <result property="permanentShortLink" column="PERMANENT_SHORT_LINK" jdbcType="VARCHAR"/>
            <result property="permanentDate" column="PERMANENT_DATE" jdbcType="TIMESTAMP"/>
            <result property="imgUrl" column="IMG_URL" jdbcType="VARCHAR"/>
            <result property="h5UrlLink" column="H5_URL_LINK" jdbcType="VARCHAR"/>

        <result property="pageType" column="PAGE_TYPE" jdbcType="DECIMAL"/>
        <result property="systemAddParam" column="SYSTEM_ADD_PARAM" jdbcType="VARCHAR"/>
        <result property="systemAddHourOrDay" column="SYSTEM_ADD_HOUR_OR_DAY" jdbcType="VARCHAR"/>
        <result property="createShortLinkPathParam" column="CREATE_SHORT_LINK_PATH_PARAM" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,WEID,PATH_TYPE,
        PAGE_NAME,PARAMS_SETTING_TYPE,PARAMS_SETTING_CONTENT,
        CREATE_TIME,UPDATE_TIME,IS_DEL,
        CREATE_BY,UPDATE_BY,SYS_ORG_CODE,
        EFFECT_TYPE,EFFECT_DAYS,REMARK,
        PATH,SHORT_LINK,PERMANENT_SHORT_LINK,PERMANENT_DATE,IMG_URL,H5_URL_LINK,PAGE_TYPE,SYSTEM_ADD_PARAM,SYSTEM_ADD_HOUR_OR_DAY,CREATE_SHORT_LINK_PATH_PARAM
    </sql>
    <select id="selectByParams" resultMap="BaseResultMap"
            parameterType="com.jnby.dto.ShortLinkToolsListReq">
        select <include refid="Base_Column_List"></include> from jnby.SHORT_LINK_TOOLS
        where is_del = 0
        <if test="path != null and path != '' ">
            and path like concat('%',concat(#{path},'%'))
        </if>
        <if test="pageName != null and pageName != '' ">
            and PAGE_NAME like concat('%',concat(#{pageName},'%'))
        </if>
        <if test="remark != null and remark != '' ">
            and REMARK like concat('%',concat(#{remark},'%'))
        </if>

        <if test="pathType != null and pathType != '' ">
            and PATH_TYPE = #{pathType}
        </if>
        <if test="weid != null and weid != '' ">
            and WEID = #{weid}
        </if>
        <if test="paramsSettingType != null and paramsSettingType != '' ">
            and PARAMS_SETTING_TYPE = #{paramsSettingType}
        </if>
        <if test="paramsSettingContent != null and paramsSettingContent != '' ">
            and PARAMS_SETTING_CONTENT like  concat('%',concat(#{paramsSettingContent},'%'))
        </if>
        order by CREATE_TIME desc
    </select>
</mapper>
