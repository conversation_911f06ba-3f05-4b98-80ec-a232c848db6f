<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.BoxDetailsMapper">
  <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.BoxDetails">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="BOX_ID" jdbcType="VARCHAR" property="boxId" />
    <result column="PRODUCT_NO" jdbcType="DECIMAL" property="productNo" />
    <result column="PRODUCT_ID" jdbcType="VARCHAR" property="productId" />
    <result column="ORIG_PRODUCT_ID" jdbcType="VARCHAR" property="origProductId" />
    <result column="PRODUCT_NAME" jdbcType="VARCHAR" property="productName" />
    <result column="PRODUCT_SIZE" jdbcType="VARCHAR" property="productSize" />
    <result column="PRODUCT_PRICE" jdbcType="VARCHAR" property="productPrice" />
    <result column="PRODUCT_FAVORABLE_PRICE" jdbcType="VARCHAR" property="productFavorablePrice" />
    <result column="VIP_PRICE" jdbcType="VARCHAR" property="vipPrice" />
    <result column="PRODUCT_QUANTITY" jdbcType="VARCHAR" property="productQuantity" />
    <result column="PRODUCT_COLOR_NO" jdbcType="VARCHAR" property="productColorNo" />
    <result column="PRODUCT_COLOR" jdbcType="VARCHAR" property="productColor" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="PRODUCT_CODE" jdbcType="VARCHAR" property="productCode" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="OUT_ID" jdbcType="VARCHAR" property="outId" />
    <result column="PRODUCT_BRAND" jdbcType="VARCHAR" property="productBrand" />
    <result column="EX_STATUS" jdbcType="DECIMAL" property="exStatus" />
    <result column="AS_STATUS" jdbcType="DECIMAL" property="asStatus" />
    <result column="AFTER_SALE_ID" jdbcType="VARCHAR" property="afterSaleId" />
    <result column="SUPPLY_ID" jdbcType="VARCHAR" property="supplyId" />
    <result column="BIG_SEASON" jdbcType="VARCHAR" property="bigSeason" />
    <result column="YEAR" jdbcType="VARCHAR" property="year" />
    <result column="PROMOTION_NAME" jdbcType="VARCHAR" property="promotionName" />
    <result column="PROMOTION_ID" jdbcType="DECIMAL" property="promotionId" />
    <result column="TOPIC" jdbcType="VARCHAR" property="topic" />
    <result column="GBCODE" jdbcType="VARCHAR" property="gbcode" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="ORDER_PRICE" jdbcType="VARCHAR" property="orderPrice" />
    <result column="BIG_CLASS" jdbcType="VARCHAR" property="bigClass" />
    <result column="SMALL_CLASS" jdbcType="VARCHAR" property="smallClass" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="TYPE" jdbcType="DECIMAL" property="type" />
    <result column="ORIGINALID" jdbcType="VARCHAR" property="originalid" />
    <result column="EB" jdbcType="DECIMAL" property="eb" />
    <result column="POCKET_TEL_ID" jdbcType="VARCHAR" property="pocketTelId" />
    <result column="IS_WARN" jdbcType="DECIMAL" property="isWarn" />
    <result column="POCKET_DETAILS_ID" jdbcType="VARCHAR" property="pocketDetailsId" />
    <result column="EXPRESS_ID" jdbcType="VARCHAR" property="expressId" />
    <result column="CHANGE" jdbcType="CHAR" property="change" />
    <result column="REFUND_STATUS" jdbcType="DECIMAL" property="refundStatus" />
    <result column="ORDER_STATUS" jdbcType="DECIMAL" property="orderStatus" />
    <result column="JOIN_SHOP" jdbcType="CHAR" property="joinShop" />
    <result column="SEND_STORE_ID" jdbcType="DECIMAL" property="sendStoreId" />
    <result column="SEND_UNIONSTORE_ID" jdbcType="DECIMAL" property="sendUnionstoreId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.jnby.infrastructure.box.model.BoxDetailsWithBLOBs">
    <result column="IMG_URL" jdbcType="CLOB" property="imgUrl" />
    <result column="SALES_FREE_TAGS" jdbcType="CLOB" property="salesFreeTags" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, BOX_ID, PRODUCT_NO, PRODUCT_ID, PRODUCT_NAME, PRODUCT_SIZE, PRODUCT_PRICE, PRODUCT_FAVORABLE_PRICE,
    VIP_PRICE, PRODUCT_QUANTITY, PRODUCT_COLOR_NO, PRODUCT_COLOR, SKU, PRODUCT_CODE,
    STATUS, CREATE_TIME, OUT_ID, PRODUCT_BRAND, EX_STATUS, AS_STATUS, AFTER_SALE_ID,
    SUPPLY_ID, BIG_SEASON, YEAR, PROMOTION_NAME, PROMOTION_ID, TOPIC, GBCODE, UPDATE_TIME,
    ORDER_PRICE, BIG_CLASS, SMALL_CLASS, REMARK, TYPE, ORIGINALID, EB, POCKET_TEL_ID,
    IS_WARN, POCKET_DETAILS_ID, EXPRESS_ID, CHANGE,REFUND_STATUS,ORDER_STATUS,JOIN_SHOP,SEND_STORE_ID,SEND_UNIONSTORE_ID,ORIG_PRODUCT_ID
  </sql>
  <sql id="Base_Alias_Column_List">
    a.ID, a.BOX_ID, a.PRODUCT_NO, a.PRODUCT_ID, a.PRODUCT_NAME, a.PRODUCT_SIZE, a.PRODUCT_PRICE, a.PRODUCT_FAVORABLE_PRICE,
    a.VIP_PRICE, a.PRODUCT_QUANTITY, a.PRODUCT_COLOR_NO, a.PRODUCT_COLOR, a.SKU, a.PRODUCT_CODE,
    a.STATUS, a.CREATE_TIME, a.OUT_ID, a.PRODUCT_BRAND, a.EX_STATUS, a.AS_STATUS, a.AFTER_SALE_ID,
    a.SUPPLY_ID, a.BIG_SEASON, a.YEAR, a.PROMOTION_NAME, a.PROMOTION_ID, a.TOPIC, a.GBCODE, a.UPDATE_TIME,
    a.ORDER_PRICE, a.BIG_CLASS, a.SMALL_CLASS, a.REMARK, a.TYPE, a.ORIGINALID, a.EB, a.POCKET_TEL_ID,
    a.IS_WARN, a.POCKET_DETAILS_ID, a.EXPRESS_ID, a.CHANGE
  </sql>
  <sql id="Blob_Column_List">
    IMG_URL, SALES_FREE_TAGS
  </sql>

  <select id="selectListByBoxId" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from BOX_DETAILS
    where BOX_ID = #{boxId,jdbcType=VARCHAR}
  </select>

  <update id="batchUpdateById" parameterType="list">
    update BOX_DETAILS
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="status =case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          <if test="item.status!=null">
            when id=#{item.id}
            then #{item.status}
          </if>
        </foreach>
      </trim>

      <trim prefix="REFUND_STATUS =case" suffix="end,">
        <foreach collection="list" item="item" index="index">
          <if test="item.refundStatus!=null">
            when id=#{item.id}
            then #{item.refundStatus}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
      #{item.id}
    </foreach>
  </update>

</mapper>
