<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.CrmCustomerMainMapper">

    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.CrmCustomerMain">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="crmId" column="CRM_ID" jdbcType="VARCHAR"/>
            <result property="customerName" column="CUSTOMER_NAME" jdbcType="VARCHAR"/>
            <result property="companyFlag" column="COMPANY_FLAG" jdbcType="VARCHAR"/>
            <result property="companyName" column="COMPANY_NAME" jdbcType="VARCHAR"/>
            <result property="companyProvince" column="COMPANY_PROVINCE" jdbcType="VARCHAR"/>
            <result property="companyProvinceId" column="COMPANY_PROVINCE_ID" jdbcType="VARCHAR"/>
            <result property="companyCity" column="COMPANY_CITY" jdbcType="VARCHAR"/>
            <result property="companyCityId" column="COMPANY_CITY_ID" jdbcType="VARCHAR"/>
            <result property="companyDistrict" column="COMPANY_DISTRICT" jdbcType="VARCHAR"/>
            <result property="companyDistrictId" column="COMPANY_DISTRICT_ID" jdbcType="VARCHAR"/>
            <result property="companyAddress" column="COMPANY_ADDRESS" jdbcType="VARCHAR"/>
            <result property="companyLegalPerson" column="COMPANY_LEGAL_PERSON" jdbcType="VARCHAR"/>
            <result property="companyPhone" column="COMPANY_PHONE" jdbcType="VARCHAR"/>
            <result property="companyEmail" column="COMPANY_EMAIL" jdbcType="VARCHAR"/>
            <result property="connectName" column="CONNECT_NAME" jdbcType="VARCHAR"/>
            <result property="connectPhone" column="CONNECT_PHONE" jdbcType="VARCHAR"/>
            <result property="connectEmail" column="CONNECT_EMAIL" jdbcType="VARCHAR"/>
            <result property="erpSystem" column="ERP_SYSTEM" jdbcType="VARCHAR"/>
            <result property="createBy" column="CREATE_BY" jdbcType="VARCHAR"/>
            <result property="updateBy" column="UPDATE_BY" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="IS_DEL" jdbcType="DECIMAL"/>
            <result property="status" column="STATUS" jdbcType="DECIMAL"/>
            <result property="investigatePerson" column="INVESTIGATE_PERSON" jdbcType="VARCHAR"/>
            <result property="investigatePersonId" column="INVESTIGATE_PERSON_ID" jdbcType="VARCHAR"/>
            <result property="totalScore" column="TOTAL_SCORE" jdbcType="VARCHAR"/>
            <result property="storeScore" column="STORE_SCORE" jdbcType="VARCHAR"/>
            <result property="interviewScore" column="INTERVIEW_SCORE" jdbcType="VARCHAR"/>
            <result property="independenceInvestigationUrl" column="INDEPENDENCE_INVESTIGATION_URL" jdbcType="VARCHAR"/>
            <result property="creditUrl" column="CREDIT_URL" jdbcType="VARCHAR"/>
            <result property="scoreUrl" column="SCORE_URL" jdbcType="VARCHAR"/>
            <result property="investigationUrl" column="INVESTIGATION_URL" jdbcType="VARCHAR"/>
            <result property="terminateReason" column="TERMINATE_REASON" jdbcType="VARCHAR"/>
            <result property="firstFranchiseTime" column="FIRST_FRANCHISE_TIME" jdbcType="VARCHAR"/>
            <result property="firstFranchiseBrand" column="FIRST_FRANCHISE_BRAND" jdbcType="VARCHAR"/>
            <result property="firstAreaManager" column="FIRST_AREA_MANAGER" jdbcType="VARCHAR"/>
            <result property="sellArea" column="SELL_AREA" jdbcType="VARCHAR"/>
            <result property="unionid" column="UNIONID" jdbcType="VARCHAR"/>

        <result property="customerControlCitys" column="CUSTOMER_CONTROL_CITYS" jdbcType="VARCHAR"/>
        <result property="customerOperateBrands" column="CUSTOMER_OPERATE_BRANDS" jdbcType="VARCHAR"/>
        <result property="operateStore" column="OPERATE_STORE" jdbcType="VARCHAR"/>
        <result property="yearScale" column="YEAR_SCALE" jdbcType="VARCHAR"/>
        <result property="clothOperateYears" column="CLOTH_OPERATE_YEARS" jdbcType="VARCHAR"/>
        <result property="isDirectTransfer" column="IS_DIRECT_TRANSFER" jdbcType="VARCHAR"/>
        <result property="jnbyAccountFor" column="JNBY_ACCOUNT_FOR" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,CRM_ID,CUSTOMER_NAME,
        COMPANY_FLAG,COMPANY_NAME,COMPANY_PROVINCE,
        COMPANY_PROVINCE_ID,COMPANY_CITY,COMPANY_CITY_ID,
        COMPANY_DISTRICT,COMPANY_DISTRICT_ID,COMPANY_ADDRESS,
        COMPANY_LEGAL_PERSON,COMPANY_PHONE,COMPANY_EMAIL,
        CONNECT_NAME,CONNECT_PHONE,CONNECT_EMAIL,
        ERP_SYSTEM,CREATE_BY,UPDATE_BY,
        CREATE_TIME,UPDATE_TIME,IS_DEL,
        STATUS,INVESTIGATE_PERSON,INVESTIGATE_PERSON_ID,
        TOTAL_SCORE,STORE_SCORE,INTERVIEW_SCORE,
        INDEPENDENCE_INVESTIGATION_URL,CREDIT_URL,SCORE_URL,
        INVESTIGATION_URL,TERMINATE_REASON,FIRST_FRANCHISE_TIME,
        FIRST_FRANCHISE_BRAND,FIRST_AREA_MANAGER,SELL_AREA,UNIONID,CUSTOMER_CONTROL_CITYS,
        CUSTOMER_OPERATE_BRANDS,OPERATE_STORE,YEAR_SCALE,CLOTH_OPERATE_YEARS,IS_DIRECT_TRANSFER,JNBY_ACCOUNT_FOR
    </sql>
    <select id="selectCanBindingCustomerMain" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from jnby.CRM_CUSTOMER_MAIN
        where is_del  = 0 and status in (3)
    </select>
    <select id="selectByParams" resultType="java.lang.String"
            parameterType="com.jnby.dto.CrmCustomerListReq">
        select ccm.id from jnby.CRM_CUSTOMER_MAIN ccm
            left join JNBY.CRM_CUSTOMER_RELATION ccr on ccm.id = ccr.CRM_CUSTOMER_MAIN_ID
            left join JNBY.CRM_CUSTOMER_LABEL ccl on ccm.id = ccl.CRM_CUSTOMER_MAIN_ID
        where  ccm.is_del = 0
        <if test="area != null and area != '' ">
            and ccm.SELL_AREA = #{area}
        </if>
        <if test="coopBrand != null and coopBrand != '' ">
            and ccr.C_ARCBRAND_ID = #{coopBrand} and ccr.is_del = 0
        </if>
        <if test="id != null and id != '' ">
            and ccm.ID = #{id}
        </if>

        <if test="status != 3 and status != 4">
            <if test="investigatePersonId != null and investigatePersonId != '' ">
                and ccm.INVESTIGATE_PERSON_ID = #{investigatePersonId}
            </if>
        </if>

        <if test="province != null and province != '' ">
            and ccm.COMPANY_PROVINCE = #{province}
        </if>

        <if test="city != null and city != '' ">
            and ccm.COMPANY_CITY = #{city}
        </if>
        <if test="status != null ">
            and ccm.status = #{status}
        </if>
        <if test="unionid != null and unionid != ''">
            and ccm.UNIONID = #{unionid}
        </if>
        <if test="customerCode != null  and customerCode != '' ">
            and ccr.CUSTOMER_CODE =#{customerCode}
        </if>

        <if test="status != 3 and status != 4">
            <if test="interviewBrandIds != null and interviewBrandIds.size() > 0 ">
                and ccm.INTERVIEW_BRAND_ID in
                <foreach collection="interviewBrandIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        </if>

        <if test="status == 3 or status == 4">
            <if test="interviewBrandIds != null and interviewBrandIds.size() > 0 ">
                and ccr.C_ARCBRAND_ID in
                <foreach collection="interviewBrandIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="customerIds != null and customerIds.size() > 0 ">
            and ccr.customer_id in
            <foreach collection="customerIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>


        <if test="search != null and search != '' ">
            and (ccm.crm_id = #{search} or ccm.CUSTOMER_NAME like concat('%',concat(#{search},'%'))
                     or ccl.LABEL_NAME like concat(#{search},'%')
                     or ccm.COMPANY_NAME like concat('%',concat(#{search},'%'))
                     or ccm.COMPANY_PHONE like concat(#{search},'%')
                     or ccm.CONNECT_PHONE like concat(#{search},'%')
                    or ccm.COMPANY_PROVINCE like concat(#{search},'%')
                    or ccm.COMPANY_CITY like concat(#{search},'%')
                    or ccm.COMPANY_DISTRICT like concat(#{search},'%')
                    or ccm.COMPANY_ADDRESS like concat(#{search},'%')
                )
        </if>
        <if test="customerName != null and customerName != '' ">
            and ( ccm.CUSTOMER_NAME like concat('%',concat(#{customerName},'%')) )
        </if>
        group by ccm.id
        order by ccm.id desc
    </select>
    <select id="selectCountByParams" resultType="java.lang.Integer" parameterType="com.jnby.dto.ListCountReq">

        select count(distinct  ccm.id) from jnby.CRM_CUSTOMER_MAIN ccm
        left join JNBY.CRM_CUSTOMER_RELATION ccr on ccm.id = ccr.CRM_CUSTOMER_MAIN_ID
        left join JNBY.CRM_CUSTOMER_LABEL ccl on ccm.id = ccl.CRM_CUSTOMER_MAIN_ID
        where  ccm.is_del = 0
        <if test="area != null and area != '' ">
            and ccm.SELL_AREA = #{area}
        </if>
        <if test="coopBrand != null and coopBrand != '' ">
            and ccr.C_ARCBRAND_ID = #{coopBrand} and ccr.is_del = 0
        </if>

        <if test="status != null ">
            and ccm.status = #{status}
        </if>
        <if test="status != 3 and status != 4">
            <if test="investigatePersonId != null and investigatePersonId != '' ">
                and ccm.INVESTIGATE_PERSON_ID = #{investigatePersonId}
            </if>
        </if>

        <if test="status != 3 and status != 4">
            <if test="interviewBrandIds != null and interviewBrandIds.size() > 0 ">
                and ccm.INTERVIEW_BRAND_ID in
                <foreach collection="interviewBrandIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        </if>

        <if test="status == 3 or status == 4">
            <if test="interviewBrandIds != null and interviewBrandIds.size() > 0 ">
                and ccr.C_ARCBRAND_ID in
                <foreach collection="interviewBrandIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        </if>

        <if test="customerIds != null and customerIds.size() > 0 ">
            and ccr.customer_id in
            <foreach collection="customerIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>


        <if test="search != null and search != '' ">
            and (ccm.crm_id = #{search} or ccm.CUSTOMER_NAME like concat('%',concat(#{search},'%'))
            or ccl.LABEL_NAME like concat(#{search},'%')
            or ccm.COMPANY_NAME like concat('%',concat(#{search},'%'))
            or ccm.COMPANY_PHONE like concat(#{search},'%')
            or ccm.CONNECT_PHONE like concat(#{search},'%')
            or ccm.COMPANY_PROVINCE like concat(#{search},'%')
            or ccm.COMPANY_CITY like concat(#{search},'%')
            or ccm.COMPANY_DISTRICT like concat(#{search},'%')
            or ccm.COMPANY_ADDRESS like concat(#{search},'%')
            )
        </if>

    </select>
</mapper>
