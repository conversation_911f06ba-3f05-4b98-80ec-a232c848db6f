<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.BoxRefundDetailsMapper">
  <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.BoxRefundDetails">
    <result column="ID" jdbcType="VARCHAR" property="id" />
    <result column="BOX_REFUND_ID" jdbcType="VARCHAR" property="boxRefundId" />
    <result column="ORDER_DETAIL_ID" jdbcType="VARCHAR" property="orderDetailId" />
    <result column="REFUND_QTY" jdbcType="VARCHAR" property="refundQty" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="EXPRESSID" jdbcType="VARCHAR" property="expressid" />
    <result column="REFUND_AMOUNT" jdbcType="FLOAT" property="refundAmount" />
    <result column="REFUND_BALANCE" jdbcType="FLOAT" property="refundBalance" />
    <result column="RETAIL_ITEM_ID" jdbcType="VARCHAR" property="retailItemId" />
  </resultMap>

  <sql id="Base_Column_List">
    ID, BOX_REFUND_ID, ORDER_DETAIL_ID, REFUND_QTY, STATUS, CREATE_TIME, UPDATE_TIME,
    EXPRESSID,REFUND_AMOUNT,REFUND_BALANCE,RETAIL_ITEM_ID
  </sql>

  <select id="selectByRefundIds"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from JNBY.BOX_REFUND_DETAILS
    where  1 = 1
      and BOX_REFUND_ID  in
      <foreach collection="list" open="(" close=")" separator="," item="item">
        #{item}
      </foreach>
  </select>

</mapper>