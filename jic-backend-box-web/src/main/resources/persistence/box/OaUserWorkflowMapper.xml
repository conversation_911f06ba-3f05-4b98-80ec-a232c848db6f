<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.OaUserWorkflowMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.OaUserWorkflow">
        <id column="ID" property="id"/>
        <result column="REQUEST_ID" property="requestId"/>
        <result column="MAIN_ID" property="mainId"/>
        <result column="DETAIL_ID" property="detailId"/>
        <result column="CUSTOMER_ID" property="customerId"/>
        <result column="CUSTOMER_NAME" property="customerName"/>
        <result column="BRAND_ID" property="brandId"/>
        <result column="BRAND_NAME" property="brandName"/>
        <result column="SYN_TIME" property="synTime"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="CREATE_BY" property="createBy"/>
        <result column="UPDATE_BY" property="updateBy"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="DEL_FLAG" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID
        , REQUEST_ID, MAIN_ID, DETAIL_ID, CUSTOMER_ID, CUSTOMER_NAME, BRAND_ID, BRAND_NAME, SYN_TIME,CREATE_TIME, CREATE_BY, UPDATE_BY, UPDATE_TIME, DEL_FLAG
    </sql>




    <select id="selectByRequestIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from OA_USER_WORKFLOW where DEL_FLAG=0 and
        REQUEST_ID in
        <foreach collection="requestIds" item="requestId" open="(" close=")" separator=",">
            #{requestId}
        </foreach>
    </select>






</mapper>