<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.OrderDetailMapper">
  <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.OrderDetail">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="ORDER_ID" jdbcType="VARCHAR" property="orderId" />
    <result column="PRODUCT_ID" jdbcType="VARCHAR" property="productId" />
    <result column="PRODUCT_NAME" jdbcType="VARCHAR" property="productName" />
    <result column="PRODUCT_PRICE" jdbcType="VARCHAR" property="productPrice" />
    <result column="PRODUCT_FAVORABLE_PRICE" jdbcType="VARCHAR" property="productFavorablePrice" />
    <result column="VIP_PRICE" jdbcType="VARCHAR" property="vipPrice" />
    <result column="PRODUCT_SIZE" jdbcType="VARCHAR" property="productSize" />
    <result column="PRODUCT_QUANTITY" jdbcType="VARCHAR" property="productQuantity" />
    <result column="PRODUCT_COLOR_NO" jdbcType="VARCHAR" property="productColorNo" />
    <result column="PRODUCT_COLOR" jdbcType="VARCHAR" property="productColor" />
    <result column="PRODUCT_BRAND" jdbcType="VARCHAR" property="productBrand" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
    <result column="REASON" jdbcType="VARCHAR" property="reason" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="PRODUCT_CODE" jdbcType="VARCHAR" property="productCode" />
    <result column="OUT_ID" jdbcType="VARCHAR" property="outId" />
    <result column="BOX_DETAIL_ID" jdbcType="VARCHAR" property="boxDetailId" />
    <result column="BIG_SEASON" jdbcType="VARCHAR" property="bigSeason" />
    <result column="YEAR" jdbcType="VARCHAR" property="year" />
    <result column="PAID_AMOUNT" jdbcType="FLOAT" property="paidAmount" />
    <result column="USE_VOU" jdbcType="DECIMAL" property="useVou" />
    <result column="PRICEACTUAL" jdbcType="VARCHAR" property="priceactual" />
    <result column="IS_REFUND" jdbcType="DECIMAL" property="isRefund" />
    <result column="REFUND_QTY" jdbcType="VARCHAR" property="refundQty" />
    <result column="EB_NUM" jdbcType="DECIMAL" property="ebNum" />
    <result column="EXPRESS_ID" jdbcType="VARCHAR" property="expressId" />
    <result column="VOUCHER_AMOUNT" jdbcType="DECIMAL" property="voucherAmount" />
    <result column="BALANCE_AMT" jdbcType="DECIMAL" property="balanceAmt" />
    <result column="SHOP_VOU_AMT" jdbcType="DECIMAL" property="shopVouAmt" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.jnby.infrastructure.box.model.OrderDetail">
    <result column="IMG_URL" jdbcType="CLOB" property="imgUrl" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, ORDER_ID, PRODUCT_ID, PRODUCT_NAME, PRODUCT_PRICE, PRODUCT_FAVORABLE_PRICE, VIP_PRICE,
    PRODUCT_SIZE, PRODUCT_QUANTITY, PRODUCT_COLOR_NO, PRODUCT_COLOR, PRODUCT_BRAND, STATUS,
    REASON, CREATE_TIME, UPDATE_TIME, SKU, PRODUCT_CODE, OUT_ID, BOX_DETAIL_ID, BIG_SEASON,
    YEAR, PAID_AMOUNT, USE_VOU, PRICEACTUAL, IS_REFUND, REFUND_QTY, EB_NUM, EXPRESS_ID,VOUCHER_AMOUNT,BALANCE_AMT,SHOP_VOU_AMT
  </sql>
  <sql id="Blob_Column_List">
    IMG_URL
  </sql>

  <select id="selectListByOrderIds" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ORDER_DETAIL
    where
    <if test="ids != null and ids.size() > 0">
       ORDER_ID in
      <foreach collection="ids" item="id" open="(" close=")" separator=",">
        #{id}
      </foreach>
    </if>
  </select>

</mapper>
