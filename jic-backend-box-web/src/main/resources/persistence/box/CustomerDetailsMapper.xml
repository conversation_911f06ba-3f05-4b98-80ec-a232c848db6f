<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.CustomerDetailsMapper">
  <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.CustomerDetails">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="OUT_ID" jdbcType="DECIMAL" property="outId" />
    <result column="OPENID" jdbcType="VARCHAR" property="openid" />
    <result column="NICK_NAME" jdbcType="VARCHAR" property="nickName" />
    <result column="HEAD_URL" jdbcType="VARCHAR" property="headUrl" />
    <result column="PHONE" jdbcType="VARCHAR" property="phone" />
    <result column="COIN" jdbcType="VARCHAR" property="coin" />
    <result column="WEIGHT" jdbcType="VARCHAR" property="weight" />
    <result column="HEIGHT" jdbcType="VARCHAR" property="height" />
    <result column="SKIN" jdbcType="VARCHAR" property="skin" />
    <result column="BODY" jdbcType="VARCHAR" property="body" />
    <result column="GENDER" jdbcType="DECIMAL" property="gender" />
    <result column="TROUSERS" jdbcType="VARCHAR" property="trousers" />
    <result column="SHOES" jdbcType="VARCHAR" property="shoes" />
    <result column="COAT" jdbcType="VARCHAR" property="coat" />
    <result column="SHIRT" jdbcType="VARCHAR" property="shirt" />
    <result column="UNDERWEAR" jdbcType="VARCHAR" property="underwear" />
    <result column="BRAND" jdbcType="VARCHAR" property="brand" />
    <result column="TOP_CLOTHING" jdbcType="VARCHAR" property="topClothing" />
    <result column="UNDER_CLOTHING" jdbcType="VARCHAR" property="underClothing" />
    <result column="SUB_EXPIRE_TIME" jdbcType="TIMESTAMP" property="subExpireTime" />
    <result column="FASHIONER_ID" jdbcType="VARCHAR" property="fashionerId" />
    <result column="SURVEY_ID" jdbcType="VARCHAR" property="surveyId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="HAS_ANSWER" jdbcType="DECIMAL" property="hasAnswer" />
    <result column="SHOW_ADDR_NOTICE" jdbcType="DECIMAL" property="showAddrNotice" />
    <result column="REG_FROM" jdbcType="VARCHAR" property="regFrom" />
    <result column="REG_TYPE" jdbcType="DECIMAL" property="regType" />
    <result column="CATEGORY_ID" jdbcType="VARCHAR" property="categoryId" />
    <result column="PUSH_MEMO" jdbcType="VARCHAR" property="pushMemo" />
    <result column="UDESK_ID" jdbcType="DECIMAL" property="udeskId" />
    <result column="SHOULDER_WIDTH" jdbcType="VARCHAR" property="shoulderWidth" />
    <result column="BUST" jdbcType="VARCHAR" property="bust" />
    <result column="HIPLINE" jdbcType="VARCHAR" property="hipline" />
    <result column="WAISTLINE" jdbcType="VARCHAR" property="waistline" />
    <result column="INVITE_CODE" jdbcType="VARCHAR" property="inviteCode" />
    <result column="MIN_QRCODE" jdbcType="VARCHAR" property="minQrcode" />
    <result column="CREDIT_STATUS" jdbcType="DECIMAL" property="creditStatus" />
    <result column="BODY_FEATURE" jdbcType="VARCHAR" property="bodyFeature" />
    <result column="BASE_COLOR" jdbcType="VARCHAR" property="baseColor" />
    <result column="COLOR" jdbcType="VARCHAR" property="color" />
    <result column="FABRIC" jdbcType="VARCHAR" property="fabric" />
    <result column="CONSTELLATION" jdbcType="VARCHAR" property="constellation" />
    <result column="BIRTHDAY" jdbcType="VARCHAR" property="birthday" />
    <result column="CUSTOMER_TYPE_ID" jdbcType="VARCHAR" property="customerTypeId" />
    <result column="EQUITY_TYPE" jdbcType="DECIMAL" property="equityType" />
    <result column="REFERRER_ID" jdbcType="VARCHAR" property="referrerId" />
    <result column="CASH" jdbcType="FLOAT" property="cash" />
    <result column="PARTNER_ID" jdbcType="VARCHAR" property="partnerId" />
    <result column="EQUITY_ID" jdbcType="VARCHAR" property="equityId" />
    <result column="VIPTYPE_ID" jdbcType="DECIMAL" property="viptypeId"/>
    <result column="VIPTYPE_NAME" jdbcType="VARCHAR" property="viptypeName"/>
    <result column="STYLES" jdbcType="VARCHAR" property="styles"/>
    <result column="JNBY_CARDNO" jdbcType="VARCHAR" property="jnbyCardNo"/>
    <result column="BG_REMARK" jdbcType="VARCHAR" property="bgRemark"/>
    <result column="HEAD" jdbcType="VARCHAR" property="head"/>
    <result column="GB" jdbcType="VARCHAR" property="gb"/>
    <result column="YAO" jdbcType="VARCHAR" property="yao"/>
    <result column="LEG" jdbcType="VARCHAR" property="leg"/>
    <result column="tot_actual_amount" jdbcType="VARCHAR" property="totActualAmount" />

    <result column="CHANNEL_ID" jdbcType="VARCHAR" property="channelId" />
    <result column="FIRST_ASK_BOX" jdbcType="DECIMAL" property="firstAskBox" />

  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.jnby.infrastructure.box.model.CustomerDetails">
    <result column="STYLE" jdbcType="CLOB" property="style" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, UNIONID, OUT_ID, OPENID, NICK_NAME, HEAD_URL, PHONE, COIN, WEIGHT, HEIGHT, SKIN,
    BODY, GENDER, TROUSERS, SHOES, COAT, SHIRT, UNDERWEAR, BRAND, TOP_CLOTHING, UNDER_CLOTHING,
    SUB_EXPIRE_TIME, FASHIONER_ID, SURVEY_ID, CREATE_TIME, UPDATE_TIME, HAS_ANSWER, SHOW_ADDR_NOTICE,
    REG_FROM, REG_TYPE, CATEGORY_ID, PUSH_MEMO, UDESK_ID, SHOULDER_WIDTH, BUST, HIPLINE,
    WAISTLINE, INVITE_CODE, MIN_QRCODE, CREDIT_STATUS, BODY_FEATURE, BASE_COLOR, COLOR,
    FABRIC, CONSTELLATION, BIRTHDAY, CUSTOMER_TYPE_ID, EQUITY_TYPE, REFERRER_ID, CASH,
    PARTNER_ID, EQUITY_ID,VIPTYPE_ID,VIPTYPE_NAME,STYLES,JNBY_CARDNO,BG_REMARK,head,gb,yao,leg,tot_actual_amount,CHANNEL_ID, FIRST_ASK_BOX
  </sql>
  <sql id="Blob_Column_List">
    STYLE
  </sql>

    <select id="batchSelectByPrimaryKey" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from CUSTOMER_DETAILS
        where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

  <select id="selectIncrementCountByTime" resultType="java.lang.Integer">
    SELECT COUNT(*) FROM CUSTOMER_DETAILS  WHERE
      (
        create_time BETWEEN to_date(#{fromDate},'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate},'YYYY-MM-DD HH24:MI:SS')
        )
                                              OR
      (
        update_time BETWEEN to_date(#{fromDate},'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate},'YYYY-MM-DD HH24:MI:SS')
        )
  </select>

  <select id="selectIncrementListByTime" resultMap="BaseResultMap">
    SELECT * FROM (
                    SELECT rownum r, t.*
                    FROM CUSTOMER_DETAILS t
                    WHERE (
                      create_time BETWEEN to_date(#{fromDate}, 'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate}, 'YYYY-MM-DD HH24:MI:SS')
                      )
                       OR (
                      update_time BETWEEN to_date(#{fromDate}, 'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate}, 'YYYY-MM-DD HH24:MI:SS')
                      )
                    ORDER BY update_time ASC
                  ) WHERE r BETWEEN #{start} AND #{end}
  </select>
</mapper>
