<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.BUserPointAccountMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.BUserPointAccount">
        <id column="ID" property="id" />
        <result column="UNION_ID" property="unionId" />
        <result column="TOTAL_POINT" property="totalPoint" />
        <result column="CAN_USE_POINT" property="canUsePoint" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="CREATE_BY" property="createBy" />
        <result column="UPDATE_BY" property="updateBy" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="DEL_FLAG" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, UNION_ID, TOTAL_POINT, CAN_USE_POINT, CREATE_TIME, CREATE_BY, UPDATE_BY, UPDATE_TIME, DEL_FLAG
    </sql>

    <select id="selectIncrementCountByTime" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM B_USER_POINT_ACCOUNT WHERE
            (
                create_time BETWEEN to_date(#{fromDate},'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate},'YYYY-MM-DD HH24:MI:SS')
                )
                                                  OR
            (
                update_time BETWEEN to_date(#{fromDate},'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate},'YYYY-MM-DD HH24:MI:SS')
                )
    </select>

    <select id="selectIncrementListByTime" resultMap="BaseResultMap">
        SELECT * FROM (
                          SELECT rownum r, t.*
                          FROM B_USER_POINT_ACCOUNT t
                          WHERE (
                              create_time BETWEEN to_date(#{fromDate}, 'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate}, 'YYYY-MM-DD HH24:MI:SS')
                              )
                             OR (
                              update_time BETWEEN to_date(#{fromDate}, 'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate}, 'YYYY-MM-DD HH24:MI:SS')
                              )
                          ORDER BY update_time ASC
                      ) WHERE r BETWEEN #{start} AND #{end}
    </select>

</mapper>
