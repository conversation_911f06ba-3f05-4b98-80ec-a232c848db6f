<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.BSubscribeInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.BSubscribeInfo">
        <id column="ID" property="id"/>
        <result column="APP_ID" property="appId"/>
        <result column="APP_NAME" property="appName"/>
        <result column="CUST_ID" property="custId"/>
        <result column="UNIONID" property="unionid"/>
        <result column="SUB_ORDER_ID" property="subOrderId"/>
        <result column="START_TIME" property="startTime"/>
        <result column="END_TIME" property="endTime"/>
        <result column="TOTAL_PRICE" property="totalPrice"/>

        <result column="STATUS" property="status"/>
        <result column="CARD_TYPE" property="cardType"/>
        <result column="USER_CARD_LEVEL" property="userCardLevel"/>

        <result column="PAY_WAY" property="payWay"/>
        <result column="UNSUB_TIME" property="unsubTime"/>

        <result column="CARD_ID" property="cardId"/>
        <result column="FIRST_RECOVER" property="firstRecover"/>
        <result column="OUT_NO" property="outNo"/>
        <result column="BIND_FASHIONER_ID" property="bindFashionerId"/>
        <result column="RECOMMENDER" property="recommender"/>
        <result column="RENEW" property="renew"/>
        <result column="INVITE_PEOPLE" property="invitePeople"/>

        <result column="TYPE" property="type"/>

        <result column="CREATE_CARD_TYPE" property="createCardType"/>
        <result column="CREATE_CARD_STORE_ID" property="createCardStoreId"/>

        <result column="CREATE_CARD_BRAND_ID" property="createCardBrandId"/>
        <result column="CREATE_CARD_HR_ID" property="createCardHrId"/>
        <result column="UNSUB_MEMO" property="unsubMemo"/>


        <result column="CREATE_TIME" property="createTime"/>
        <result column="CREATE_BY" property="createBy"/>
        <result column="UPDATE_BY" property="updateBy"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="DEL_FLAG" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, APP_ID, APP_NAME,STATUS, CUST_ID, UNIONID, SUB_ORDER_ID, START_TIME, END_TIME, TOTAL_PRICE,CARD_ID,USER_CARD_LEVEL,PAY_WAY,UNSUB_TIME, FIRST_RECOVER,OUT_NO,BIND_FASHIONER_ID, RECOMMENDER,RENEW,INVITE_PEOPLE,TYPE, CREATE_CARD_TYPE, CREATE_CARD_STORE_ID,CREATE_CARD_HR_ID,CREATE_CARD_BRAND_ID, UNSUB_MEMO, CREATE_TIME, CREATE_BY, UPDATE_BY, UPDATE_TIME, DEL_FLAG
    </sql>

<!--    <select id="selectIncrementCountByTime" resultType="java.lang.Integer">-->
<!--        SELECT COUNT(*) FROM B_SUBSCRIBE_INFO  WHERE-->
<!--            (-->
<!--                create_time BETWEEN to_date(#{fromDate},'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate},'YYYY-MM-DD HH24:MI:SS')-->
<!--                )-->
<!--                                                  OR-->
<!--            (-->
<!--                update_time IS NOT null-->
<!--                    AND-->
<!--                update_time BETWEEN to_date(#{fromDate},'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate},'YYYY-MM-DD HH24:MI:SS')-->
<!--                )-->
<!--    </select>-->

<!--    <select id="selectIncrementListByTime" resultMap="BaseResultMap">-->
<!--        SELECT * FROM (-->
<!--                          SELECT rownum r, t.*-->
<!--                          FROM B_SUBSCRIBE_INFO t-->
<!--                          WHERE (-->
<!--                              create_time BETWEEN to_date(#{fromDate}, 'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate}, 'YYYY-MM-DD HH24:MI:SS')-->
<!--                              )-->
<!--                             OR (-->
<!--                              update_time IS NOT null-->
<!--                                  AND-->
<!--                              update_time BETWEEN to_date(#{fromDate}, 'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate}, 'YYYY-MM-DD HH24:MI:SS')-->
<!--                              )-->
<!--                          ORDER BY update_time ASC-->
<!--                      ) WHERE r BETWEEN #{start} AND #{end}-->
<!--    </select>-->
    <select id="selectIncrementCountByTime" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM B_SUBSCRIBE_INFO  WHERE
            (
                create_time BETWEEN to_date(#{fromDate},'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate},'YYYY-MM-DD HH24:MI:SS')
                )
                                                  OR
            (
                update_time BETWEEN to_date(#{fromDate},'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate},'YYYY-MM-DD HH24:MI:SS')
                )
    </select>

    <select id="selectIncrementListByTime" resultMap="BaseResultMap">
        SELECT * FROM (
                          SELECT rownum r, t.*
                          FROM B_SUBSCRIBE_INFO t
                          WHERE (
                              create_time BETWEEN to_date(#{fromDate}, 'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate}, 'YYYY-MM-DD HH24:MI:SS')
                              )
                             OR (
                              update_time BETWEEN to_date(#{fromDate}, 'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate}, 'YYYY-MM-DD HH24:MI:SS')
                              )
                          ORDER BY update_time ASC
                      ) WHERE r BETWEEN #{start} AND #{end}
    </select>
</mapper>