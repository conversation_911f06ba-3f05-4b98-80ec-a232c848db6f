<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.BoxMapper">
  <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.Box">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="BOX_SN" jdbcType="VARCHAR" property="boxSn" />
    <result column="SERIAL_NUMBER" jdbcType="VARCHAR" property="serialNumber" />
    <result column="DAYSTR" jdbcType="VARCHAR" property="daystr" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="ASK_ID" jdbcType="VARCHAR" property="askId" />
    <result column="MATCH_ID" jdbcType="VARCHAR" property="matchId" />
    <result column="ORDER_ID" jdbcType="VARCHAR" property="orderId" />
    <result column="LOGISTICS_ID" jdbcType="VARCHAR" property="logisticsId" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
    <result column="SCORE" jdbcType="VARCHAR" property="score" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CREATE_FAS_ID" jdbcType="VARCHAR" property="createFasId" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="TRACK_NUMBER" jdbcType="VARCHAR" property="trackNumber" />
    <result column="HAS_LOGISTICS_NOTICE" jdbcType="DECIMAL" property="hasLogisticsNotice" />
    <result column="HAS_RECEIVE_NOTICE" jdbcType="DECIMAL" property="hasReceiveNotice" />
    <result column="IS_WARN" jdbcType="DECIMAL" property="isWarn" />
    <result column="IS_WARN_NOTICE" jdbcType="DECIMAL" property="isWarnNotice" />
    <result column="WARN_TIME" jdbcType="TIMESTAMP" property="warnTime" />
    <result column="IS_EVAL" jdbcType="DECIMAL" property="isEval" />
    <result column="OUT_ID" jdbcType="FLOAT" property="outId" />
    <result column="IS_READ" jdbcType="DECIMAL" property="isRead" />
    <result column="STOCK_TYPE" jdbcType="DECIMAL" property="stockType" />
    <result column="IS_YD" jdbcType="DECIMAL" property="isYd" />
    <result column="BOX_TYPE" jdbcType="DECIMAL" property="boxType" />
    <result column="CUSTOMER_MEMO" jdbcType="VARCHAR" property="customerMemo" />
    <result column="CALC_BILLNO" jdbcType="VARCHAR" property="calcBillno" />
    <result column="IS_SHOPVIP" jdbcType="DECIMAL" property="isShopvip" />
    <result column="SHOP_DISCOUNT" jdbcType="DECIMAL" property="shopDiscount" />
    <result column="TYPE" jdbcType="DECIMAL" property="type" />
    <result column="EVALUATION_COUPON" jdbcType="VARCHAR" property="evaluationCoupon" />
    <result column="SEND_TIME" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="SIGNIN_TIME" jdbcType="TIMESTAMP" property="signinTime" />
    <result column="EVAL_TIME" jdbcType="TIMESTAMP" property="evalTime" />
    <result column="FINISH_TIME" jdbcType="TIMESTAMP" property="finishTime" />
    <result column="BUY_TIME" jdbcType="TIMESTAMP" property="buyTime" />
    <result column="RETURN_TIME" jdbcType="TIMESTAMP" property="returnTime" />
    <result column="EBYH_NO" jdbcType="VARCHAR" property="ebyhNo" />
    <result column="READ" jdbcType="DECIMAL" property="read" />
    <result column="SALESREP_ID" jdbcType="DECIMAL" property="salesrepId" />
    <result column="FASHIONER" jdbcType="VARCHAR" property="fashioner" />
    <result column="SALES" jdbcType="VARCHAR" property="sales" />
    <result column="PLACLE_ORDER" jdbcType="DECIMAL" property="placleOrder" />
    <result column="POCKET_TEL_ID" jdbcType="VARCHAR" property="pocketTelId" />
    <result column="MERGE_ID" jdbcType="VARCHAR" property="mergeId" />
    <result column="CANCEL_REASON" jdbcType="VARCHAR" property="cancelReason" />
    <result column="IF_FEEDBACK" jdbcType="DECIMAL" property="ifFeedback" />
    <result column="SALES_EVAL" jdbcType="VARCHAR" property="salesEval" />
    <result column="CONTACTS" jdbcType="DECIMAL" property="contacts" />
    <result column="LAST_CONTACT_TIME" jdbcType="TIMESTAMP" property="lastContactTime" />
    <result column="EXTEND" jdbcType="VARCHAR" property="extend" />
    <result column="BRCODEURL" jdbcType="VARCHAR" property="brCodeUrl" />
    <result column="TRY_OUT_TIME" jdbcType="TIMESTAMP" property="tryOutTime" />
    <result column="NO_PAY_REASON" jdbcType="VARCHAR" property="noPayReason" />
    <result column="PLATFORM" jdbcType="DECIMAL" property="platform" />
    <result column="explicit_brand" jdbcType="VARCHAR" property="explicitBrand" />
    <result column="IS_EB" jdbcType="INTEGER" property="isEb" />

    <result column="SOURCE_CODE" jdbcType="VARCHAR" property="sourceCode" />
    <result column="THEME_ACTIVITY_IDS" jdbcType="VARCHAR" property="themeActivityIds" />
    <result column="IF_WMS" jdbcType="INTEGER" property="ifWms" />
    <result column="CHANNEL_ID" jdbcType="VARCHAR" property="channelId" />
    <result column="HR_EMP_ID" jdbcType="VARCHAR" property="hrEmpId" />
    <result column="C_STORE_ID" jdbcType="VARCHAR" property="cStoreId" />
    <result column="IF_VIR_DELIVERY" jdbcType="INTEGER" property="ifVirDelivery" />
    <result column="APP_ID" jdbcType="VARCHAR" property="appId" />
    <result column="CLOSE_REFUND" jdbcType="DECIMAL" property="closeRefund" />
    <result column="SOURCE_ID" jdbcType="VARCHAR" property="sourceId" />
    <result column="SOURCE_TYPE" jdbcType="DECIMAL" property="sourceType" />
    <result column="CREDIT_ID" jdbcType="VARCHAR" property="creditId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.jnby.infrastructure.box.model.BoxWithBLOBs">
    <result column="MATCH_MEMO" jdbcType="CLOB" property="matchMemo" />
    <result column="MEMO" jdbcType="CLOB" property="memo" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, BOX_SN, SERIAL_NUMBER, DAYSTR, UNIONID, ASK_ID, MATCH_ID, ORDER_ID, LOGISTICS_ID,
    STATUS, SCORE, CREATE_TIME, CREATE_FAS_ID, UPDATE_TIME, TRACK_NUMBER, HAS_LOGISTICS_NOTICE,
    HAS_RECEIVE_NOTICE, IS_WARN, IS_WARN_NOTICE, WARN_TIME, IS_EVAL, OUT_ID, IS_READ,
    STOCK_TYPE, IS_YD, BOX_TYPE, CUSTOMER_MEMO, CALC_BILLNO, IS_SHOPVIP, SHOP_DISCOUNT,
    TYPE, EVALUATION_COUPON, SEND_TIME, SIGNIN_TIME, EVAL_TIME, FINISH_TIME, BUY_TIME,
    RETURN_TIME, EBYH_NO, READ, SALESREP_ID, FASHIONER, SALES, PLACLE_ORDER, POCKET_TEL_ID,
    MERGE_ID, CANCEL_REASON, IF_FEEDBACK, SALES_EVAL, CONTACTS, LAST_CONTACT_TIME,EXTEND,BRCODEURL,
    TRY_OUT_TIME,NO_PAY_REASON,PLATFORM,explicit_brand,IS_EB,SOURCE_CODE,THEME_ACTIVITY_IDS,IF_WMS,CHANNEL_ID,HR_EMP_ID,C_STORE_ID,
    IF_VIR_DELIVERY,APP_ID,CLOSE_REFUND, SOURCE_ID,SOURCE_TYPE,CREDIT_ID
  </sql>
  <sql id="Blob_Column_List">
    MATCH_MEMO, MEMO
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from BOX
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <select id="selectByBoxSn" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from BOX
    where box_sn = #{boxSn,jdbcType=VARCHAR}
  </select>

  <select id="selectListByUnionId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from BOX
    where unionid = #{unionId,jdbcType=VARCHAR}
    order by CREATE_TIME desc
  </select>


<!--  <select id="selectIncrementCountByTime" resultType="java.lang.Integer">-->
<!--    SELECT COUNT(*) FROM BOX  WHERE-->
<!--      (-->
<!--        create_time BETWEEN to_date(#{fromDate},'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate},'YYYY-MM-DD HH24:MI:SS')-->
<!--        )-->
<!--                                              OR-->
<!--      (-->
<!--        update_time IS NOT null-->
<!--          AND-->
<!--        update_time BETWEEN to_date(#{fromDate},'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate},'YYYY-MM-DD HH24:MI:SS')-->
<!--        )-->
<!--  </select>-->

<!--  <select id="selectIncrementListByTime" resultMap="BaseResultMap">-->
<!--    SELECT * FROM (-->
<!--                    SELECT rownum r, t.*-->
<!--                    FROM BOX t-->
<!--                    WHERE (-->
<!--                      create_time BETWEEN to_date(#{fromDate}, 'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate}, 'YYYY-MM-DD HH24:MI:SS')-->
<!--                      )-->
<!--                       OR (-->
<!--                      update_time IS NOT null-->
<!--                        AND-->
<!--                      update_time BETWEEN to_date(#{fromDate}, 'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate}, 'YYYY-MM-DD HH24:MI:SS')-->
<!--                      )-->
<!--                    ORDER BY update_time ASC-->
<!--                  ) WHERE r BETWEEN #{start} AND #{end}-->
<!--  </select>-->

  <select id="selectIncrementCountByTime" resultType="java.lang.Integer">
    SELECT COUNT(*) FROM BOX  WHERE
      (
        create_time BETWEEN to_date(#{fromDate},'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate},'YYYY-MM-DD HH24:MI:SS')
        )
                                 OR
      (
        update_time BETWEEN to_date(#{fromDate},'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate},'YYYY-MM-DD HH24:MI:SS')
        )
  </select>
  <select id="selectIncrementListByTime" resultMap="BaseResultMap">
    SELECT * FROM (
                    SELECT rownum r, t.*
                    FROM BOX t
                    WHERE (
                      update_time BETWEEN to_date(#{fromDate}, 'YYYY-MM-DD HH24:MI:SS') AND to_date(#{toDate}, 'YYYY-MM-DD HH24:MI:SS')
                      )
                    ORDER BY update_time ASC
                  ) WHERE r BETWEEN #{start} AND #{end}
  </select>

  <select id="selectUnionIdListByBoxSnList" resultMap="BaseResultMap">
    select box_sn,unionid from box where box_sn in
    <foreach collection="boxSnList" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <update id="updateByPrimaryKeySelective" parameterType="com.jnby.infrastructure.box.model.BoxWithBLOBs">
    update BOX
    <set>
      <if test="boxSn != null">
        BOX_SN = #{boxSn,jdbcType=VARCHAR},
      </if>
      <if test="serialNumber != null">
        SERIAL_NUMBER = #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="daystr != null">
        DAYSTR = #{daystr,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        UNIONID = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="askId != null">
        ASK_ID = #{askId,jdbcType=VARCHAR},
      </if>
      <if test="matchId != null">
        MATCH_ID = #{matchId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        ORDER_ID = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="logisticsId != null">
        LOGISTICS_ID = #{logisticsId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=DECIMAL},
      </if>
      <if test="score != null">
        SCORE = #{score,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createFasId != null">
        CREATE_FAS_ID = #{createFasId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="trackNumber != null">
        TRACK_NUMBER = #{trackNumber,jdbcType=VARCHAR},
      </if>
      <if test="hasLogisticsNotice != null">
        HAS_LOGISTICS_NOTICE = #{hasLogisticsNotice,jdbcType=DECIMAL},
      </if>
      <if test="hasReceiveNotice != null">
        HAS_RECEIVE_NOTICE = #{hasReceiveNotice,jdbcType=DECIMAL},
      </if>
      <if test="isWarn != null">
        IS_WARN = #{isWarn,jdbcType=DECIMAL},
      </if>
      <if test="isWarnNotice != null">
        IS_WARN_NOTICE = #{isWarnNotice,jdbcType=DECIMAL},
      </if>
      <if test="warnTime != null">
        WARN_TIME = #{warnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isEval != null">
        IS_EVAL = #{isEval,jdbcType=DECIMAL},
      </if>
      <if test="outId != null">
        OUT_ID = #{outId,jdbcType=FLOAT},
      </if>
      <if test="isRead != null">
        IS_READ = #{isRead,jdbcType=DECIMAL},
      </if>
      <if test="stockType != null">
        STOCK_TYPE = #{stockType,jdbcType=DECIMAL},
      </if>
      <if test="isYd != null">
        IS_YD = #{isYd,jdbcType=DECIMAL},
      </if>
      <if test="boxType != null">
        BOX_TYPE = #{boxType,jdbcType=DECIMAL},
      </if>
      <if test="customerMemo != null">
        CUSTOMER_MEMO = #{customerMemo,jdbcType=VARCHAR},
      </if>
      <if test="calcBillno != null">
        CALC_BILLNO = #{calcBillno,jdbcType=VARCHAR},
      </if>
      <if test="isShopvip != null">
        IS_SHOPVIP = #{isShopvip,jdbcType=DECIMAL},
      </if>
      <if test="shopDiscount != null">
        SHOP_DISCOUNT = #{shopDiscount,jdbcType=DECIMAL},
      </if>
      <if test="type != null">
        TYPE = #{type,jdbcType=DECIMAL},
      </if>
      <if test="evaluationCoupon != null">
        EVALUATION_COUPON = #{evaluationCoupon,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null">
        SEND_TIME = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="signinTime != null">
        SIGNIN_TIME = #{signinTime,jdbcType=TIMESTAMP},
      </if>
      <if test="evalTime != null">
        EVAL_TIME = #{evalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="finishTime != null">
        FINISH_TIME = #{finishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="buyTime != null">
        BUY_TIME = #{buyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="returnTime != null">
        RETURN_TIME = #{returnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ebyhNo != null">
        EBYH_NO = #{ebyhNo,jdbcType=VARCHAR},
      </if>
      <if test="read != null">
        READ = #{read,jdbcType=DECIMAL},
      </if>
      <if test="salesrepId != null">
        SALESREP_ID = #{salesrepId,jdbcType=DECIMAL},
      </if>
      <if test="fashioner != null">
        FASHIONER = #{fashioner,jdbcType=VARCHAR},
      </if>
      <if test="sales != null">
        SALES = #{sales,jdbcType=VARCHAR},
      </if>
      <if test="placleOrder != null">
        PLACLE_ORDER = #{placleOrder,jdbcType=DECIMAL},
      </if>
      <if test="pocketTelId != null">
        POCKET_TEL_ID = #{pocketTelId,jdbcType=VARCHAR},
      </if>
      <if test="mergeId != null">
        MERGE_ID = #{mergeId,jdbcType=VARCHAR},
      </if>
      <if test="cancelReason != null">
        CANCEL_REASON = #{cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="ifFeedback != null">
        IF_FEEDBACK = #{ifFeedback,jdbcType=DECIMAL},
      </if>
      <if test="salesEval != null">
        SALES_EVAL = #{salesEval,jdbcType=VARCHAR},
      </if>
      <if test="contacts != null">
        CONTACTS = #{contacts,jdbcType=DECIMAL},
      </if>
      <if test="lastContactTime != null">
        LAST_CONTACT_TIME = #{lastContactTime,jdbcType=TIMESTAMP},
      </if>
      <if test="matchMemo != null">
        MATCH_MEMO = #{matchMemo,jdbcType=CLOB},
      </if>
      <if test="memo != null">
        MEMO = #{memo,jdbcType=CLOB},
      </if>
      <if test="extend != null">
        EXTEND = #{extend,jdbcType=CLOB},
      </if>
      <if test="brCodeUrl != null">
        BRCODEURL = #{brCodeUrl,jdbcType=VARCHAR},
      </if>
      <if test="tryOutTime != null">
        TRY_OUT_TIME = #{tryOutTime,jdbcType=TIMESTAMP},
      </if>
      <if test="noPayReason != null">
        NO_PAY_REASON=#{noPayReason,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        PLATFORM=#{platform,jdbcType=DECIMAL},
      </if>
      <if test="explicitBrand != null">
        explicit_brand=#{explicitBrand,jdbcType=DECIMAL},
      </if>
      <if test="isEb != null">
        is_eb=#{isEb,jdbcType=INTEGER},
      </if>
      <if test="ifWms != null">
        if_wms=#{ifWms,jdbcType=INTEGER},
      </if>
      <if test="hrEmpId != null">
        HR_EMP_ID= #{hrEmpId,jdbcType=VARCHAR},
      </if>
      <if test="cStoreId != null">
        C_STORE_ID=#{cStoreId,jdbcType=VARCHAR},
      </if>
      <if test="ifVirDelivery != null">
        IF_VIR_DELIVERY=#{ifVirDelivery,jdbcType=INTEGER},
      </if>
      <if test="closeRefund != null">
        CLOSE_REFUND = #{closeRefund},
      </if>
      <if test="sourceId != null">
        SOURCE_ID = #{sourceId,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null">
        SOURCE_TYPE = #{sourceType,jdbcType=DECIMAL},
      </if>
      <if test="creditId != null">
        CREDIT_ID = #{creditId,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>
