<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.CrmGenCodeMapper">

    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.CrmGenCode">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="provinceName" column="PROVINCE_NAME" jdbcType="VARCHAR"/>
            <result property="cityName" column="CITY_NAME" jdbcType="VARCHAR"/>
            <result property="districtName" column="DISTRICT_NAME" jdbcType="VARCHAR"/>
            <result property="code" column="CODE" jdbcType="VARCHAR"/>
            <result property="idLeaf" column="ID_LEAF" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,PROVINCE_NAME,CITY_NAME,DISTRICT_NAME,CODE,ID_LEAF
    </sql>
    <select id="selectByProviceCityDistrictName" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from jnby.CRM_GEN_CODE
        where PROVINCE_NAME like concat(#{companyProvince},'%') and CITY_NAME like concat(#{companyCity},'%')  and DISTRICT_NAME like concat(#{companyDistrict},'%')
    </select>
</mapper>
