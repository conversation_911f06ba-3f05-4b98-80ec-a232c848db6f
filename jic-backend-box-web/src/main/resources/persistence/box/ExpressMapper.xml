<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.ExpressMapper">
  <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.Express">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="SOURCE_ID" jdbcType="VARCHAR" property="sourceId" />
    <result column="EXPRESS_DOCNO" jdbcType="VARCHAR" property="expressDocno" />
    <result column="EXPRESS_NAME" jdbcType="VARCHAR" property="expressName" />
    <result column="EXPRESS_NO" jdbcType="VARCHAR" property="expressNo" />
    <result column="TYPE" jdbcType="DECIMAL" property="type" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="BOX_ID" jdbcType="VARCHAR" property="boxId" />
    <result column="ORIG_ID" jdbcType="VARCHAR" property="origId" />
    <result column="EXTEND_JSON" jdbcType="CLOB" property="extendJson" />
    <result column="ORIG_ID" jdbcType="VARCHAR" property="origId" />
    <result column="EXPRESS_ORDER_ID" jdbcType="VARCHAR" property="expressOrderId" />
    <result column="LOGISTICS_ID" jdbcType="VARCHAR" property="logisticsId" />
    <result column="EMP_CODE" jdbcType="VARCHAR" property="empCode" />
    <result column="EMP_PHONE" jdbcType="VARCHAR" property="empPhone" />
    <result column="LOGISTICS_SNAPSHOT_ID" jdbcType="VARCHAR" property="logisticsSnapshotId" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, SOURCE_ID, EXPRESS_DOCNO, EXPRESS_NAME, EXPRESS_NO, TYPE, STATUS, CREATE_TIME,
    UPDATE_TIME,BOX_ID,ORIG_ID,EXTEND_JSON,EXPRESS_ORDER_ID,LOGISTICS_ID,EMP_CODE,EMP_PHONE,LOGISTICS_SNAPSHOT_ID
  </sql>

  <select id="selectListBySelective" parameterType="com.jnby.infrastructure.box.model.Express" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from EXPRESS
    <where>
    <if test="sourceId != null">
      and SOURCE_ID = #{sourceId,jdbcType=VARCHAR}
    </if>
    <if test="expressDocno != null">
      and EXPRESS_DOCNO = #{expressDocno,jdbcType=VARCHAR}
    </if>
    <if test="expressName != null">
      and EXPRESS_NAME = #{expressName,jdbcType=VARCHAR}
    </if>
    <if test="expressNo != null">
      and EXPRESS_NO = #{expressNo,jdbcType=VARCHAR}
    </if>
    <if test="type != null">
      and TYPE = #{type,jdbcType=DECIMAL}
    </if>
    <if test="status != null">
      and STATUS = #{status,jdbcType=DECIMAL}
    </if>
    <if test="origId != null">
      and ORIG_ID = #{origId,jdbcType=VARCHAR}
    </if>
    <if test="boxId != null">
      and BOX_ID = #{boxId,jdbcType=VARCHAR}
    </if>
    <if test="expressOrderId != null">
      and EXPRESS_ORDER_ID= #{expressOrderId,jdbcType=VARCHAR}
    </if>
    </where>
  </select>

</mapper>
