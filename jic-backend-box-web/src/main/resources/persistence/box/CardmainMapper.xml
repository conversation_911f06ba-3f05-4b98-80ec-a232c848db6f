<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.CardmainMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.Cardmain">
        <id column="ID" property="id" />
        <result column="RENTID" property="rentid" />
        <result column="WEID" property="weid" />
        <result column="OPENID" property="openid" />
        <result column="CARDTYPE" property="cardtype" />
        <result column="CARDNO" property="cardno" />
        <result column="USERNAME" property="username" />
        <result column="JFYE" property="jfye" />
        <result column="JFTOTAL" property="jftotal" />
        <result column="JFXF" property="jfxf" />
        <result column="JFSIGN" property="jfsign" />
        <result column="XFJE" property="xfje" />
        <result column="TEL" property="tel" />
        <result column="BIRTHDAY" property="birthday" />
        <result column="ADDRESS" property="address" />
        <result column="SEX" property="sex" />
        <result column="EMAIL" property="email" />
        <result column="STATUS" property="status" />
        <result column="REMARK" property="remark" />
        <result column="MODIFYDATE" property="modifydate" />
        <result column="INPUTDATE" property="inputdate" />
        <result column="VALIDDATE" property="validdate" />
        <result column="ISLINK" property="islink" />
        <result column="LINKSOURCE" property="linksource" />
        <result column="LINKID" property="linkid" />
        <result column="ROLEID" property="roleid" />
        <result column="KFID" property="kfid" />
        <result column="KKSTORE" property="kkstore" />
        <result column="ISSEND" property="issend" />
        <result column="SENDTIME" property="sendtime" />
        <result column="CDMUNIONID" property="cdmunionid" />
        <result column="CDMSOURCETYPE" property="cdmsourcetype" />
        <result column="CDMSOURCEVAL" property="cdmsourceval" />
        <result column="ISPOSTFITTING" property="ispostfitting" />
        <result column="POSTFITTINGTIME" property="postfittingtime" />
        <result column="OYEARPAYAMOUNT" property="oyearpayamount" />
        <result column="DYEARPAYAMOUNT" property="dyearpayamount" />
        <result column="OYEARPAYCOUNT" property="oyearpaycount" />
        <result column="DYEARPAYCOUNT" property="dyearpaycount" />
        <result column="OMONTHPAYAMOUNT" property="omonthpayamount" />
        <result column="DMONTHPAYAMOUNT" property="dmonthpayamount" />
        <result column="OMONTHPAYCOUNT" property="omonthpaycount" />
        <result column="DMONTHPAYCOUNT" property="dmonthpaycount" />
        <result column="JNBEAN" property="jnbean" />
        <result column="BY1" property="by1" />
        <result column="BY2" property="by2" />
        <result column="BY3" property="by3" />
        <result column="WXOPENID" property="wxopenid" />
        <result column="UUID" property="uuid" />
        <result column="UNIONID" property="unionid" />
        <result column="NICKNAME" property="nickname" />
        <result column="WID" property="wid" />
        <result column="HEADIMGURL" property="headimgurl" />
        <result column="CARDID" property="cardid" />
        <result column="CARDTYPENAME" property="cardtypename" />
        <result column="CARDTYPEID" property="cardtypeid" />
        <result column="MEMBERID" property="memberid" />
        <result column="MEMBERTYPEID" property="membertypeid" />
        <result column="MEMBERTYPENAME" property="membertypename" />
        <result column="STOREID" property="storeid" />
        <result column="STORENAME" property="storename" />
        <result column="STORECODE" property="storecode" />
        <result column="CLERKID" property="clerkid" />
        <result column="CLERKNAME" property="clerkname" />
        <result column="CLERKCODE" property="clerkcode" />
        <result column="CUSTOMERID" property="customerid" />
        <result column="OPENTIME" property="opentime" />
        <result column="MEMBERCODE" property="membercode" />
        <result column="ISACTIVE" property="isactive" />
        <result column="OPENCHANNEL" property="openchannel" />
        <result column="INTEGRAL" property="integral" />
        <result column="ISSUBSCRIBE" property="issubscribe" />
        <result column="ISEXTERNALUSER" property="isexternaluser" />
        <result column="ISSUBSCRIBEBOX" property="issubscribebox" />
        <result column="ISRECEIVEBIRTH" property="isreceivebirth" />
        <result column="LASTCONSUMTIME" property="lastconsumtime" />
        <result column="CONSUMTIMES" property="consumtimes" />
        <result column="TOTCONSUMAMT" property="totconsumamt" />
        <result column="EXT1" property="ext1" />
        <result column="EXT2" property="ext2" />
        <result column="EXT3" property="ext3" />
        <result column="EXT4" property="ext4" />
        <result column="EXT5" property="ext5" />
        <result column="EXT6" property="ext6" />
        <result column="EXT7" property="ext7" />
        <result column="EXT8" property="ext8" />
        <result column="EXT9" property="ext9" />
        <result column="EXT10" property="ext10" />
        <result column="MEMBERTIME" property="membertime" />
        <result column="MEMBERINTEGRAL" property="memberintegral" />
        <result column="CLERKPHONE" property="clerkphone" />
        <result column="CARDSTATUS" property="cardstatus" />
        <result column="FIRSTCONSUMTIME" property="firstconsumtime" />
        <result column="EXTERNALUSERID" property="externaluserid" />
        <result column="CARDLVL" property="cardlvl" />
        <result column="MEMBERLVL" property="memberlvl" />
        <result column="CARDNUM" property="cardnum" />
        <result column="FIRSTCONSUMETIME" property="firstconsumetime" />
        <result column="LASTCONSUMETIME" property="lastconsumetime" />
        <result column="CONSUMETIMES" property="consumetimes" />
        <result column="TOTCONSUMEAMT" property="totconsumeamt" />
        <result column="EXT11" property="ext11" />
        <result column="EXT12" property="ext12" />
        <result column="EXT13" property="ext13" />
        <result column="EXT14" property="ext14" />
        <result column="EXT15" property="ext15" />
        <result column="FIRSTSTOREID" property="firststoreid" />
        <result column="FIRSTSTORENAME" property="firststorename" />
        <result column="FIRSTSTORECODE" property="firststorecode" />
        <result column="FIRSTCUSTOMERID" property="firstcustomerid" />
        <result column="FIRSTCLERKPHONE" property="firstclerkphone" />
        <result column="FIRSTCLERKID" property="firstclerkid" />
        <result column="FIRSTCLERKNAME" property="firstclerkname" />
        <result column="FIRSTCLERKCODE" property="firstclerkcode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, RENTID, WEID, OPENID, CARDTYPE, CARDNO, USERNAME, JFYE, JFTOTAL, JFXF, JFSIGN, XFJE, TEL, BIRTHDAY, ADDRESS, SEX, EMAIL, STATUS, REMARK, MODIFYDATE, INPUTDATE, VALIDDATE, ISLINK, LINKSOURCE, LINKID, ROLEID, KFID, KKSTORE, ISSEND, SENDTIME, CDMUNIONID, CDMSOURCETYPE, CDMSOURCEVAL, ISPOSTFITTING, POSTFITTINGTIME, OYEARPAYAMOUNT, DYEARPAYAMOUNT, OYEARPAYCOUNT, DYEARPAYCOUNT, OMONTHPAYAMOUNT, DMONTHPAYAMOUNT, OMONTHPAYCOUNT, DMONTHPAYCOUNT, JNBEAN, BY1, BY2, BY3, WXOPENID, UUID, UNIONID, NICKNAME, WID, HEADIMGURL, CARDID, CARDTYPENAME, CARDTYPEID, MEMBERID, MEMBERTYPEID, MEMBERTYPENAME, STOREID, STORENAME, STORECODE, CLERKID, CLERKNAME, CLERKCODE, CUSTOMERID, OPENTIME, MEMBERCODE, ISACTIVE, OPENCHANNEL, INTEGRAL, ISSUBSCRIBE, ISEXTERNALUSER, ISSUBSCRIBEBOX, ISRECEIVEBIRTH, LASTCONSUMTIME, CONSUMTIMES, TOTCONSUMAMT, EXT1, EXT2, EXT3, EXT4, EXT5, EXT6, EXT7, EXT8, EXT9, EXT10, MEMBERTIME, MEMBERINTEGRAL, CLERKPHONE, CARDSTATUS, FIRSTCONSUMTIME, EXTERNALUSERID, CARDLVL, MEMBERLVL, CARDNUM, FIRSTCONSUMETIME, LASTCONSUMETIME, CONSUMETIMES, TOTCONSUMEAMT, EXT11, EXT12, EXT13, EXT14, EXT15, FIRSTSTOREID, FIRSTSTORENAME, FIRSTSTORECODE, FIRSTCUSTOMERID, FIRSTCLERKPHONE, FIRSTCLERKID, FIRSTCLERKNAME, FIRSTCLERKCODE
    </sql>
    <select id="listCardmainByUnionId" resultMap="BaseResultMap">
        select * from userwx.cardmain where unionid in
        <foreach collection="unionIds" separator="," item="unionId" close=")" open="(">
            #{unionId}
        </foreach>
    </select>

</mapper>