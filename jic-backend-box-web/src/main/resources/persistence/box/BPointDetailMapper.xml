<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.BPointDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.BPointDetail">
        <id column="ID" property="id"/>
        <result column="SUB_ID" property="subId"/>
        <result column="UNION_ID" property="unionId"/>
        <result column="CUSTOMER_ID" property="customerId"/>

        <result column="OUT_ID" property="outId"/>
        <result column="OUT_SN" property="outSn"/>

        <result column="POINT" property="point"/>
        <result column="MEMO" property="memo"/>
        <result column="TYPE" property="type"/>

        <result column="ACTIVE_TIME" property="activeTime"/>
        <result column="EXPIRE_TIME" property="expireTime"/>

        <result column="CREATE_TIME" property="createTime"/>
        <result column="CREATE_BY" property="createBy"/>
        <result column="UPDATE_BY" property="updateBy"/>
        <result column="UPDATE_TIME" property="updateTime"/>
        <result column="DEL_FLAG" property="delFlag"/>
    </resultMap>
    <select id="getListByUnionIdAndTime" resultMap="BaseResultMap">
        select
        *
        from
        B_POINT_DETAIL
        where
        DEL_FLAG=0
        and UNION_ID in
        <foreach collection="unionIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        and EXPIRE_TIME <![CDATA[ >= ]]> SYSDATE and EXPIRE_TIME  <![CDATA[ <= ]]> SYSDATE+#{days}
    </select>

</mapper>