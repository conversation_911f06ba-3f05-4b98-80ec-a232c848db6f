<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.CrmCustomerRelationMapper">

    <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.CrmCustomerRelation">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="crmCustomerMainId" column="CRM_CUSTOMER_MAIN_ID" jdbcType="VARCHAR"/>
            <result property="customerCode" column="CUSTOMER_CODE" jdbcType="VARCHAR"/>
            <result property="createTime" column="CREATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="IS_DEL" jdbcType="DECIMAL"/>
            <result property="cArcbrandId" column="C_ARCBRAND_ID" jdbcType="VARCHAR"/>
        <result property="customerId" column="CUSTOMER_ID" jdbcType="VARCHAR"/>
        <result property="isStop" column="IS_STOP" jdbcType="VARCHAR"/>

        <result property="customerLevel" column="CUSTOMER_LEVEL" jdbcType="VARCHAR"/>
        <result property="customerEvaluate" column="CUSTOMER_EVALUATE" jdbcType="VARCHAR"/>

        <result property="legalPerson" column="LEGAL_PERSON" jdbcType="VARCHAR"/>
        <result property="legalPersonPhone" column="LEGAL_PERSON_PHONE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,CRM_CUSTOMER_MAIN_ID,CUSTOMER_CODE,
        CREATE_TIME,UPDATE_TIME,IS_DEL,C_ARCBRAND_ID,CUSTOMER_ID,IS_STOP,CUSTOMER_LEVEL,CUSTOMER_EVALUATE,LEGAL_PERSON,LEGAL_PERSON_PHONE
    </sql>
    <update id="updateByCrmCustomerMainId">
        update JNBY.CRM_CUSTOMER_RELATION set CRM_CUSTOMER_MAIN_ID = #{newCrmCustomerMainId} , update_time = sysdate
                                          where CRM_CUSTOMER_MAIN_ID = #{oldCrmCustomerMainId}

    </update>
    <select id="selectByCrmCustomerMainId" resultMap="BaseResultMap"
            parameterType="java.lang.String">
        select <include refid="Base_Column_List"></include> from jnby.CRM_CUSTOMER_RELATION
        where is_del =  0 and CRM_CUSTOMER_MAIN_ID = #{crmCustomerMainId}
    </select>
    <select id="selectByCrmCustomerMainIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include> from jnby.CRM_CUSTOMER_RELATION
        where is_del =  0 and CRM_CUSTOMER_MAIN_ID  in
                              <foreach collection="crmCustomerMainIds" separator="," item="item" close=")" open="(">
                                  #{item}
                              </foreach>

    </select>
</mapper>
