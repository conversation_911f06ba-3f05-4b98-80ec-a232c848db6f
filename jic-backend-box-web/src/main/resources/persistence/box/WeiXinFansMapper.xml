<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jnby.infrastructure.box.mapper.WeiXinFansMapper">
  <resultMap id="BaseResultMap" type="com.jnby.infrastructure.box.model.WeiXinFans">
    <result column="ID" jdbcType="DECIMAL" property="id" />
    <result column="SUBSCRIBE" jdbcType="DECIMAL" property="subscribe" />
    <result column="OPENID" jdbcType="VARCHAR" property="openid" />
    <result column="NICKNAME" jdbcType="VARCHAR" property="nickname" />
    <result column="CITY" jdbcType="VARCHAR" property="city" />
    <result column="PROVINCE" jdbcType="VARCHAR" property="province" />
    <result column="COUNTRY" jdbcType="VARCHAR" property="country" />
    <result column="HEADIMGURL" jdbcType="VARCHAR" property="headimgurl" />
    <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="SUB_FROM" jdbcType="VARCHAR" property="subFrom" />
  </resultMap>

  <sql id="Base_Column_List">
    ID, SUBSCRIBE, OPENID, NICKNAME, CITY, PROVINCE,
    COUNTRY, HEADIMGURL, UNIONID, CREATE_TIME, UPDATE_TIME, SUB_FROM
  </sql>


  <insert id="insert" parameterType="com.jnby.infrastructure.box.model.WeiXinFans">
    insert into JNBY.WEIXIN_FANS (ID, SUBSCRIBE, OPENID,
      NICKNAME, CITY, PROVINCE,
      COUNTRY, HEADIMGURL, UNIONID,
      CREATE_TIME, UPDATE_TIME, SUB_FROM
      )
    values (#{id,jdbcType=DECIMAL}, #{subscribe,jdbcType=DECIMAL}, #{openid,jdbcType=VARCHAR},
      #{nickname,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR},
      #{country,jdbcType=VARCHAR}, #{headimgurl,jdbcType=VARCHAR}, #{unionid,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{subFrom,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.jnby.infrastructure.box.model.WeiXinFans">
    insert into JNBY.WEIXIN_FANS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="subscribe != null">
        SUBSCRIBE,
      </if>
      <if test="openid != null">
        OPENID,
      </if>
      <if test="nickname != null">
        NICKNAME,
      </if>
      <if test="city != null">
        CITY,
      </if>
      <if test="province != null">
        PROVINCE,
      </if>
      <if test="country != null">
        COUNTRY,
      </if>
      <if test="headimgurl != null">
        HEADIMGURL,
      </if>
      <if test="unionid != null">
        UNIONID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="subFrom != null">
        SUB_FROM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=DECIMAL},
      </if>
      <if test="subscribe != null">
        #{subscribe,jdbcType=DECIMAL},
      </if>
      <if test="openid != null">
        #{openid,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="country != null">
        #{country,jdbcType=VARCHAR},
      </if>
      <if test="headimgurl != null">
        #{headimgurl,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="subFrom != null">
        #{subFrom,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <!--"select * from " + WeixinFans.ENTITY + " where unionid = ? and subscribe = 1", unionId-->
  <!--根据unionId查询关注过公众号的用户-->
  <select id="findWeiXinFans" parameterType="java.lang.String" resultMap="BaseResultMap">

    select
    <include refid="Base_Column_List" />
    from JNBY.WEIXIN_FANS
     where subscribe = 1
     <if test="unionid != null">
       and UNIONID = #{unionid,jdbcType=VARCHAR}
     </if>


  </select>

  <select id="findByOpenid" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from JNBY.WEIXIN_FANS
    where 1 = 1 and openid = #{openId,jdbcType=VARCHAR}
  </select>
</mapper>
