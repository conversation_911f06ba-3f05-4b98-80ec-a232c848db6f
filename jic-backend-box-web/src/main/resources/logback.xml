<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="true" scanPeriod="1 seconds">

    <!-- 定义日志文件 输入位置 -->
    <property name="common_log" value="jic-backend-box-web.common.log"/>
    <property name="report_log" value="jic-backend-box-web.report.log"/>
    <property name="log_dir" value="/opt/logs/jic-backend-box-web" />

    <!-- 日志最大的历史 30天 -->
    <property name="maxHistory" value="7"/>

    <!-- 定义日志文件 输入位置 -->
    <contextName>logback</contextName>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
<!--            <pattern>%d{HH:mm:ss.SSS} %contextName [%thread] %-5level [%X{X-B3-TraceId:-}] %logger{36} - %msg%n</pattern>-->
            <pattern>%d{HH:mm:ss.SSS} %contextName [%thread] %-5level [%X{X-B3-TraceId:-} %X{X-B3-SpanId:-} %X{spanExportable:-}] %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="report" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log_dir}/${report_log}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log_dir}/${report_log}.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>${maxHistory}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="common" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log_dir}/${common_log}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log_dir}/${common_log}.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>${maxHistory}</maxHistory>
        </rollingPolicy>
        <encoder>
<!--            <pattern>%date [%thread] %level [%X{X-B3-TraceId}] %logger{36} [%file : %line] - %msg%n</pattern>-->
            <pattern>%date [%thread] %level [%X{X-B3-TraceId} %X{X-B3-SpanId:-} %X{spanExportable:-}] %logger{36} [%file : %line] - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <logger name="report" level="INFO" additivity="false">
        <appender-ref ref="report" />
    </logger>
    <!--<logger name="com.jnby.infrastructure.box.mapper" level="DEBUG"></logger>-->

    <root>
        <springProfile name="dev">
            <level value="DEBUG" />
            <appender-ref ref="common" />
            <appender-ref ref="console" />
        </springProfile>
    </root>

    <root>
        <springProfile name="!dev">
            <level value="INFO" />
            <appender-ref ref="common" />
            <appender-ref ref="console" />
        </springProfile>
    </root>


</configuration>
