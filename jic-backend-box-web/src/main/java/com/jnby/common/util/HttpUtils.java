package com.jnby.common.util;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import okhttp3.*;
import okio.BufferedSink;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;
public class HttpUtils {

    private HttpUtils() {
    }

    public static OkHttpClient getInstance() {
        return Singleton.INSTANCE.getInstance();
    }

    private static enum Singleton {
        INSTANCE;
        private OkHttpClient singleton;

        private Singleton() {
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            builder.connectTimeout(6L, TimeUnit.SECONDS);
            builder.readTimeout(6L, TimeUnit.SECONDS);
            builder.writeTimeout(6L, TimeUnit.SECONDS);
            int ioWorkerCount = Math.max(Runtime.getRuntime().availableProcessors()*6, 4);
            ConnectionPool connectionPool = new ConnectionPool(ioWorkerCount, 4, TimeUnit.SECONDS);
            builder.connectionPool(connectionPool);
            singleton = builder.build();
        }

        public OkHttpClient getInstance() {
            return singleton;
        }
    }

    public static String get(String url){
        Request request = new Request.Builder()
                .url(url).get()
                .build();
        try {
            ResponseBody responseBody = getInstance().newCall(request).execute().body();
            return responseBody.string();
        } catch (Exception e) {
            return null;
        }
    }

    public static String get(String url, okhttp3.Headers headers){
        Request request = new Request.Builder()
                .url(url)
                .headers(headers)
                .get()
                .build();
        try {
            ResponseBody responseBody = getInstance().newCall(request).execute().body();
            return responseBody.string();
        } catch (Exception e) {
            return null;
        }
    }

    public static String post(String url, Map<String, Object> params,okhttp3.Headers headers){
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody requestBody = FormBody.create(mediaType, JSON.toJSONString(params));
        Request request = new Request.Builder()
                .url(url)
                .headers(headers)
                .post(requestBody)
                .build();
        try {
            ResponseBody responseBody = getInstance().newCall(request).execute().body();
            return responseBody.string();
        } catch (Exception e) {
            return null;
        }
    }

    public static String postForm(String url, Map<String, String> params,okhttp3.Headers headers){
        FormBody.Builder builder = new FormBody.Builder();
        for (String key : params.keySet()) {
            builder.add(key,params.get(key));
        }
        FormBody build = builder.build();
        Request request = new Request.Builder()
                .url(url)
                .headers(headers)
                .post(build)
                .build();
        try {
            ResponseBody responseBody = getInstance().newCall(request).execute().body();
            return responseBody.string();
        } catch (Exception e) {
            return null;
        }
    }


    public static String post(String url, Map<String, Object> params){
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody requestBody = FormBody.create(mediaType, JSON.toJSONString(params));
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        try {
            ResponseBody responseBody = getInstance().newCall(request).execute().body();
            return responseBody.string();
        } catch (Exception e) {
            return null;
        }
    }


    public static String post(String url, String body){
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody requestBody = FormBody.create(mediaType, body);
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        try {
            ResponseBody responseBody = getInstance().newCall(request).execute().body();
            return responseBody.string();
        } catch (Exception e) {
            return null;
        }
    }

    public static void main(String[] args) {
        System.out.println(HttpUtils.get("https://bzhz.jnbygroup.com/api/segment/get/leaf-segment-test"));
    }
}
