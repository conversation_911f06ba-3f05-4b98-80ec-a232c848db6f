package com.jnby.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jnby.common.CommonRequest;
import com.jnby.dto.GenerateShortUrlReq;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date:2023/1/5 13:54
 */
@Component
@Slf4j
public class GenerateShortUrlUtil {

    @Value("${create.short.link.perfix}")
    private String shortLinkPerfix;

    public String getAccessToken(){
        String json = HttpUtil.get(shortLinkPerfix+"/gateway/api/openapi/wx/getMaAccessToken");
        Map<String,Object> commonRequest = JSONObject.parseObject(json, Map.class);
        Map<String, Object> requestData = JSONObject.parseObject(JSONObject.toJSONString(commonRequest.get("data")));
        return requestData.get("access_token").toString();
    }


    public String generateShortUrl(String pageUrl) throws WxErrorException {
        String accessToken = getAccessToken();
        Map<String, Object> map  = new HashMap<>();
        map.put("page_url", pageUrl);
        map.put("is_permanent", true);
        String json = HttpUtil.post("https://api.weixin.qq.com/wxa/genwxashortlink?access_token="+ accessToken, map);
        JSONObject jsonObject = JSONObject.parseObject(json);
        if(jsonObject.get("errcode").toString().equals("0")){
            return jsonObject.getString("link");
        }
        return "";
    }

//    public String generateLongUrl(WxGenerateUrlReq wxGenerateUrlReq) throws WxErrorException {
//        String accessToken = getAccessToken();
//        Map<String, Object> map  = new HashMap<>();
//        map.put("path", wxGenerateUrlReq.getUrl());
//        map.put("query",wxGenerateUrlReq.getQuery());
//        String json = HttpUtil.post("https://api.weixin.qq.com/wxa/generate_urllink?access_token="+ accessToken, map);
//        JSONObject jsonObject = JSONObject.parseObject(json);
//        if(jsonObject.get("errcode").toString().equals("0")){
//            return jsonObject.getString("url_link");
//        }
//        return "";
//    }

//    public String generateProLongUrl(String s) {
//        String accessToken = null;
//        try {
//            accessToken = getAccessToken();
//        } catch (WxErrorException e) {
//            throw new RuntimeException(e);
//        }
//        Map<String, Object> map  = new HashMap<>();
//        map.put("path", s);
//        String json = HttpUtil.post("https://api.weixin.qq.com/wxa/generate_urllink?access_token="+ accessToken, map);
//        JSONObject jsonObject = JSONObject.parseObject(json);
//        if(jsonObject.get("errcode").toString().equals("0")){
//            return jsonObject.getString("url_link");
//        }
//        return "";
//    }



    public String generateShortUrl(GenerateShortUrlReq generateShortUrlReq) throws WxErrorException {
        String accessToken = getAccessToken();
        Map map = JSONObject.parseObject(JSONObject.toJSONString(generateShortUrlReq), Map.class);
        log.info("generateShortUrl req = {}",JSONObject.toJSONString(map));
        String json = HttpUtil.post("https://api.weixin.qq.com/wxa/generate_urllink?access_token="+ accessToken, map);
        JSONObject jsonObject = JSONObject.parseObject(json);
        log.info("generateShortUrl resp = {}",json);
        if(jsonObject.get("errcode").toString().equals("0")){
            return jsonObject.getString("url_link");
        }
        return "";
    }


    public String generateShortUrl(String pageUrl,String pageTitle) throws WxErrorException {
        String accessToken = getAccessToken();
        Map<String, Object> map  = new HashMap<>();
        map.put("page_url", pageUrl);
        map.put("page_title", pageTitle);
        map.put("is_permanent", false);
        log.info("generateShortUrl req = {}",JSONObject.toJSONString(map));
        String json = HttpUtil.post("https://api.weixin.qq.com/wxa/genwxashortlink?access_token="+ accessToken, map);
        log.info("generateShortUrl resp = {}",json);
        JSONObject jsonObject = JSONObject.parseObject(json);
        if(jsonObject.get("errcode").toString().equals("0")){
            return jsonObject.getString("link");
        }
        return "";
    }

    public byte[] generateQrCode(Map<String, Object> map) {
        // 生成小程序码
        String accessToken = getAccessToken();
        log.info("generateQrCode req = {}",JSONObject.toJSONString(map));
        byte[] bytes = HttpUtilsBackend.postForImg("https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token="+ accessToken, map);
        return bytes;
    }
}
