package com.jnby.common.util;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字符串工具类
 * <AUTHOR>
 * @date 2021/3/23
 */
public class StrUtil extends StringUtils{

    /**
     * 中文匹配是否包含regex_like实现
     * @param a1
     * @param a2
     * @return
     */
    public static boolean isSameCharacter(String a1, String a2){
        String maxS;
        String minS;
        //短句遍历开始处
        int start = 0;
        //词的长度最短为两个字长
        int range =2;
        int MINSIZE = 2;
        //设定短句和长句s,使得遍历更加快捷
        if (a1.length() <= a2.length()){
            maxS = a2;
            minS = a1;
        }else {
            maxS = a1;
            minS = a2;
        }
        String result = "";
        ArrayList<String> list = new ArrayList<String>();
        //防止substring时超出范围
        while (start + range <= minS.length()) {
            //如果句子或词在对象里面，则找出相应的句子或词保存在list里面
            if (maxS.indexOf(minS.substring(start, start + range)) != -1) {
                //获取最长句子,删除短句子
                list.remove(result);
                list.add(minS.substring(start, start + range));
                result = minS.substring(start, start + range);
                range++;
                continue;
            }
            range = MINSIZE;
            start++;
        }
        return list.isEmpty() ? false : true;
    }

    /**
     * @Description: 生成指定长度随机数字
     * @Param: [length]
     * @return: java.lang.String
     * @Author: Kevin.Jin
     * @Date: 2019/9/18
     */
    public static String randomStr(int length) {
        StringBuilder sb = new StringBuilder();
        Random rand = new Random();
        for (int i = 0; i < length; i++) {
            sb.append(rand.nextInt(10));
        }
        String data = sb.toString();
        return data;
    }

    /**
     * 6位验证码
     * @return
     */
    public static String getRandom(){
        Random rad=new Random();
        String result  = rad.nextInt(1000000) +"";
        if(result.length()!=6){
            return getRandom();
        }
        return result;
    }

    /**
     * 获取随机UUID
     * @return
     */
    public static String getUUID(){
        return UUID.randomUUID().toString().replace("-","").toLowerCase();
    }

    /**
     * 清除特殊字符
     * @param str
     * @return
     */
    public static String stringFilter(String str) {
        // 清除掉所有特殊字符
        String regEx="[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.replaceAll("").trim();
    }


    /**
     * 校验特殊字符
     * @param str
     * @return
     */
    public static boolean checkSpecialCharacters(String str) {
        String regEx="[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
        return Pattern.matches(regEx,str);
    }


    public static List<List<String>> getSubLists(List<String> allData, int size) {
        List<List<String>> result = new ArrayList();
        for (int begin = 0; begin < allData.size(); begin = begin + size) {
            int end = (begin + size > allData.size() ? allData.size() : begin + size);
            result.add(allData.subList(begin, end));
        }
        return result;
    }

}
