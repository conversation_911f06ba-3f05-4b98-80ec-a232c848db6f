package com.jnby.common.util;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * EXCEL解析工具类
 *
 * @Author: brian
 * @Date: 2021/8/26 10:28
 */
public class POIUtil {

    public static List<String> readExcelByUrl(String fileUrl){
        String path = StrUtil.getUUID();
        if(fileUrl.endsWith(".xls")){
            path += ".xls";
        }else if(fileUrl.endsWith(".xlsx")){
            path += ".xlsx";
        }
        // 下载文件
        FileUtil.downloadByUrl(fileUrl,path);
        return readExcel(path);
    }

    /**
     * 读取Excel中的内容。每列用,隔开
     * @param filePath
     * @return
     */
    public static List<String> readExcel(String filePath){
        List<String> result = new ArrayList<>();
        try {
            File excel = new File(filePath);
            if (excel.isFile() && excel.exists()) {   //判断文件是否存在
                Workbook wb;
                //根据文件后缀（xls/xlsx）进行判断
                if (filePath.endsWith(".xls")){
                    FileInputStream fis = new FileInputStream(excel);   //文件流对象
                    wb = new HSSFWorkbook(fis);
                }else if (filePath.endsWith(".xlsx")){
                    wb = new XSSFWorkbook(excel);
                }else {
                    System.out.println("文件类型错误!");
                    return null;
                }
                DataFormatter dataFormat = new DataFormatter();
                //开始解析,获取页签数
                for(int i=0;i<wb.getNumberOfSheets();i++){
                    Sheet sheet = wb.getSheetAt(i);     //读取sheet
                    int firstRowIndex = sheet.getFirstRowNum()+1;   //第一行是列名，所以不读
                    int lastRowIndex = sheet.getLastRowNum();
                    for(int rIndex = firstRowIndex; rIndex <= lastRowIndex; rIndex++) {   //遍历行
                        StringBuffer sb=new StringBuffer("");
                        Row row = sheet.getRow(rIndex);
                        if (row != null) {
                            int firstCellIndex = row.getFirstCellNum();
                            int lastCellIndex = row.getLastCellNum();
                            for (int cIndex = firstCellIndex; cIndex < lastCellIndex; cIndex++) {   //遍历列
                                Cell cell = row.getCell(cIndex);
                                if (cell != null) {
                                    String value = dataFormat.formatCellValue(cell);
                                    sb.append(value);
                                    if(cIndex != row.getLastCellNum()-1){
                                        sb.append(",");
                                    }
                                }
                            }
                        }
                        result.add(sb.toString());
                    }
                }
                // 删除文件
                excel.delete();
                return result;
            } else {
                System.out.println("找不到指定的文件");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 读取Excel中的内容。根据attrName组装为List
     * @param filePath
     * @return
     */
    public static List<Map<String,Object>> readExcel(List<String> attrNameList,String filePath){
        List<String> result = new ArrayList<>();
        try {
            File excel = new File(filePath);
            if (excel.isFile() && excel.exists()) {   //判断文件是否存在
                Workbook wb;
                //根据文件后缀（xls/xlsx）进行判断
                if (filePath.endsWith(".xls")){
                    FileInputStream fis = new FileInputStream(excel);   //文件流对象
                    wb = new HSSFWorkbook(fis);
                }else if (filePath.endsWith(".xlsx")){
                    wb = new XSSFWorkbook(excel);
                }else {
                    System.out.println("文件类型错误!");
                    return null;
                }
                List<Map<String,Object>> list = new ArrayList<>();
                DataFormatter dataFormat = new DataFormatter();
                //开始解析,获取页签数
                for(int i=0;i<wb.getNumberOfSheets();i++){
                    Sheet sheet = wb.getSheetAt(i);     //读取sheet
                    int firstRowIndex = sheet.getFirstRowNum()+1;   //第一行是列名，所以不读
                    int lastRowIndex = sheet.getLastRowNum();
                    for(int rIndex = firstRowIndex; rIndex <= lastRowIndex; rIndex++) {   //遍历行
                        Map map = new HashMap<>();
                        Row row = sheet.getRow(rIndex);
                        if (row != null) {
                            int firstCellIndex = row.getFirstCellNum();
                            int lastCellIndex = row.getLastCellNum();
                            for (int cIndex = firstCellIndex; cIndex < lastCellIndex; cIndex++) {   //遍历列
                                Cell cell = row.getCell(cIndex);
                                String value ;
                                if (cell != null) {
                                    value = dataFormat.formatCellValue(cell);
                                }else{
                                    value = "";
                                }
                                if(cIndex <= attrNameList.size() - 1){
                                    map.put(attrNameList.get(cIndex),value);
                                }
                            }
                        }
                        list.add(map);
                    }
                }
                // 删除文件
                excel.delete();
                return list;
            } else {
                System.out.println("找不到指定的文件");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
