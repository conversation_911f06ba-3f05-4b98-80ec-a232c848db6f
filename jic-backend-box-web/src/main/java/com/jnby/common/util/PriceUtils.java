package com.jnby.common.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class PriceUtils {
    /**
     * 元转分。注意元会被截取为小数点后2位
     * @param yuan 浮点类型的元
     * @return 分
     */
    public static Integer yuan2Fen(Double yuan) {
        Integer fen = 0;
        if (yuan == null) {
            return fen;
        }
        // 将 double 转换为 BigDecimal
        BigDecimal amountYuan = new BigDecimal(Double.toString(yuan));

        // 乘以 100 转换为分，确保结果有两位小数
        BigDecimal amountFen = amountYuan.multiply(new BigDecimal("100"))
                .setScale(2, RoundingMode.HALF_UP);
        return amountFen.intValue();
    }

    public static void main(String[] args) {
        System.out.println(yuan2Fen(0.01));
        System.out.println(yuan2Fen(0.011));
        System.out.println(yuan2Fen(1.051));
        System.out.println(yuan2Fen(1111.31));
    }
}
