//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.jnby.common.util;

import com.alibaba.fastjson.JSON;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.FormBody;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.ResponseBody;

@Slf4j
public class HttpUtilsBackend {
    private HttpUtilsBackend() {
    }

    public static OkHttpClient getInstance() {
        return HttpUtilsBackend.Singleton.INSTANCE.getInstance();
    }

    public static String get(String url) {
        Request request = (new Request.Builder()).url(url).get().build();

        try {
            ResponseBody responseBody = getInstance().newCall(request).execute().body();
            return responseBody.string();
        } catch (Exception var3) {
            return null;
        }
    }

    public static String get(String url, Headers headers) {
        Request request = (new Request.Builder()).url(url).headers(headers).get().build();

        try {
            ResponseBody responseBody = getInstance().newCall(request).execute().body();
            return responseBody.string();
        } catch (Exception var4) {
            return null;
        }
    }

    public static byte[] postForImg(String url, Map<String, Object> params) {
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody requestBody = FormBody.create(mediaType, JSON.toJSONString(params));
        Request request = (new Request.Builder()).url(url).post(requestBody).build();

        try {
            ResponseBody responseBody = getInstance().newCall(request).execute().body();
            byte[] bytes = responseBody.bytes();
            if(bytes.length > 0){
                try {
                    log.info("postForImg 打印返回参数 = {}", java.util.Base64.getEncoder().encodeToString(bytes));
                }catch (Exception e){
                    return null;
                }
            }
            return bytes;
        } catch (Exception var6) {
            return null;
        }
    }



    public static void main(String[] args) {
        System.out.println(get("https://bzhz.jnbygroup.com/api/segment/get/leaf-segment-test"));
    }

    private static enum Singleton {
        INSTANCE;

        private OkHttpClient singleton;

        private Singleton() {
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            builder.connectTimeout(6L, TimeUnit.SECONDS);
            builder.readTimeout(6L, TimeUnit.SECONDS);
            builder.writeTimeout(6L, TimeUnit.SECONDS);
            int ioWorkerCount = Math.max(Runtime.getRuntime().availableProcessors() * 6, 4);
            ConnectionPool connectionPool = new ConnectionPool(ioWorkerCount, 4L, TimeUnit.SECONDS);
            builder.connectionPool(connectionPool);
            this.singleton = builder.build();
        }

        public OkHttpClient getInstance() {
            return this.singleton;
        }
    }
}
