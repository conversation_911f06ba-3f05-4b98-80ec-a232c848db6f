package com.jnby.common.enums;

import com.jnby.common.exception.BoxException;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/3/14
 */
public enum BoxDetailsStatusEnum {
    unhandle(0, "未处理"),
    applyReturned(1, "申请退货"),
    returned(2, "已退货"),
    paid(3, "已购买"),
    handle(4, "已处理"),
    waitRefund(5, "待退款"),
    overRefund(6, "已退款"),
    cancel(7, "已取消"),

    DELETE_PARAGRAPH(12,"已删款")
    ;

    private Integer code;
    private String name;

    BoxDetailsStatusEnum(Integer code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return this.name;
    }

    public Integer getCode() {
        return this.code;
    }


    /**
     * 根据code获取枚举，不存在则认为异常
     * @param code
     * @return
     */
    public static BoxStatusEnum getEnumByCode(Integer code) {
        if (code == null) {
            throw new BoxException("BOX子订单状态不存在，请核验");
        }
        for (BoxStatusEnum e : EnumSet.allOf(BoxStatusEnum.class)) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new BoxException("BOX子订单状态不存在，请核验");
    }
}
