package com.jnby.common.enums;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/6/3015:35
 */
public enum BoxRefundStatusEnum {
    TOBEBACK(0, "待寄回"),
    TOBEREVEIVED(1, "待仓库收货"),
    TOBEREFUND(2, "待退款"),
    REFUNDSUCCESS(3, "已成功退款"),
    RJAPPLY(4, "申请驳回"),
    CANCELREFUND(5, "取消退款"),

    REFUNDING(6, "退款中 (联域商户状态)"),

    REFUNDFAIL(7, "退款失败"),
    
    ;



    private static final Map<Integer, BoxRefundStatusEnum> LOOKUP = new HashMap<>();

    static {
        for (BoxRefundStatusEnum s : EnumSet.allOf(BoxRefundStatusEnum.class)) {
            LOOKUP.put(s.getCode(), s);
        }
    }

    private Integer code;
    private String name;

    BoxRefundStatusEnum(Integer code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return this.name;
    }

    public Integer getCode() {
        return this.code;
    }

    public static String get(Integer code) {
        BoxRefundStatusEnum boxRefundStatusEnum = LOOKUP.get(code);
        return boxRefundStatusEnum.getName();
    }
}
