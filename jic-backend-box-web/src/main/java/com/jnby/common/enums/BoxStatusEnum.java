package com.jnby.common.enums;

import com.jnby.common.exception.BoxException;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/3/14
 */
public enum BoxStatusEnum {
    //
    create(-1, "待提交"),
    unship(0, "未发货"),
    shiped(1, "已发货"),
    receive(2, "已签收"),
    finish(3, "已完成未入库"),
    evaluation(4, "已评价"),
    finishall(5, "已完成已入库"),
    cancel(6, "取消"),
    invalid(7, "作废"),
    merge(8, "合并"),
    waiting_return(9, "待还货"),
    returning(10, "还货中"),
    ;
    private Integer code;
    private String name;

    BoxStatusEnum(Integer code, String name) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return this.name;
    }

    public Integer getCode() {
        return this.code;
    }

    /**
     * 根据code获取枚举，不存在则认为异常
     * @param code
     * @return
     */
    public static BoxStatusEnum getEnumByCode(Integer code) {
        if (code == null) {
            throw new BoxException("BOX单状态不存在，请核验");
        }
        for (BoxStatusEnum e : EnumSet.allOf(BoxStatusEnum.class)) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        throw new BoxException("BOX单状态不存在，请核验");
    }

}
