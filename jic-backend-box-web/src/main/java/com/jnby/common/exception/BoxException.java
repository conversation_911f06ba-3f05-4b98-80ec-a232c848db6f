package com.jnby.common.exception;

import com.jnby.common.ErrorConstants;

/**
 * 业务异常封装
 * <AUTHOR>
 * @version 1.0
 * @date 3/8/21 10:06 AM
 */
public class BoxException extends RuntimeException {
    private static final String DEFAULT_ERROR_CODE = "99999";
    private static final String DEFAULT_OK_CODE = "00000";

    private static final String DEFAULT_ERROR_MSG  = "系统异常,请稍后再试";

    /**
     * 业务信息
     */
    private String              bizMessage         = DEFAULT_ERROR_CODE;

    /**
     * 错误码
     */
    private String              code               = DEFAULT_ERROR_MSG;

    /**
     * 构造方法
     */
    public BoxException(ErrorConstants enumsError) {
        super("{'errorCode':'" + enumsError.code() + "','errorMsg':'" + enumsError.getDescription() + "'}");
        this.bizMessage = enumsError.getDescription();
        this.code = enumsError.getCode();
    }

    public BoxException(String msg) {
        super("{'errorCode':'" + DEFAULT_ERROR_CODE + "','errorMsg':'" + msg + "'}");
        this.bizMessage = msg;
        this.code = DEFAULT_ERROR_CODE;
    }

    public BoxException(String code, String msg) {
        super("{'errorCode':'" + code + "','errorMsg':'" + msg + "'}");
        this.bizMessage = msg;
        this.code = code;
    }

    public BoxException(String code, String msg, boolean noJonInCode) {
        super(msg);
        this.bizMessage = msg;
        this.code = code;
    }


    /**
     * 构造方法
     */
    public BoxException(ErrorConstants enumsError, String errMsg) {
        super("{'errorCode':'" + enumsError.code() + "','errorMsg':'" + errMsg + "'}");
        this.bizMessage = enumsError.getDescription() + errMsg;
        this.code = enumsError.getCode();
    }

    /**
     * 构造方法
     */
    public BoxException(ErrorConstants enumsError, String errMsg, Throwable t) {
        super("{'errorCode':'" + enumsError.code() + "','errorMsg':'" + errMsg + "'}");
        this.bizMessage = errMsg;
        this.code = enumsError.getCode();
    }

    /**
     * 构造方法
     * @param t
     */
    public BoxException(Throwable t) {
        super("{'errorCode':'99999','errorMsg':'系统异常,请稍后再试'}");
    }

    /**
     * 构造方法
     * @param t
     */
    public BoxException(String errMsg, Throwable t) {
        super("{'errorCode':'99999','errorMsg':'" + errMsg + "'}");
        this.bizMessage = errMsg;
    }

    public String getBizMessage() {
        return bizMessage;
    }

    public String getCode() {
        return code;
    }
}
