package com.jnby.module.oa;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.jnby.common.Page;
import com.jnby.dto.bojun.CustomerDto;
import com.jnby.dto.oa.FormattableMain42Dto;
import com.jnby.infrastructure.bojun.mapper.CCustomerMapper;
import com.jnby.infrastructure.box.model.OaUserWorkflow;
import com.jnby.infrastructure.box.model.OaWorkflow;
import com.jnby.infrastructure.oa.mapper.FormtableMain42Mapper;
import com.jnby.infrastructure.oa.mapper.WorkBaseMapper;
import com.jnby.infrastructure.oa.model.CustomerWithBrand;
import com.jnby.infrastructure.oa.model.HtCustomerWithBrand;
import com.jnby.infrastructure.oa.model.WorkCommonEntity;
import com.jnby.infrastructure.oa.model.WorkFlowEntity;
import com.jnby.module.crm.IOaUserWorkflowService;
import com.jnby.module.crm.IOaWorkflowService;
import groovyjarjarpicocli.CommandLine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PullHtDataServiceImpl implements IPullHtDataService {

    @Resource
    private WorkBaseMapper workBaseMapper;

    @Resource
    private CCustomerMapper cCustomerMapper;


    @Resource
    private FormtableMain42Mapper formtableMain42Mapper;

    @Resource
    private IOaWorkflowService iOaWorkflowService;

    @Resource
    private IOaUserWorkflowService iOaUserWorkflowService;


    /**
     * 处理合同数据
     */
    @Override
    public void handleHtData() {
        log.info("全量处理合同数据开始");
        List<WorkCommonEntity> result = new ArrayList<>();
        int totalPage = 1;
        Page page = new Page(1, 1000);
        while (page.getPageNo() <= totalPage) {
            List<WorkCommonEntity> workCommonEntities = getHtList(page, null);
            if (CollectionUtils.isEmpty(workCommonEntities)) {
                break;
            }
            totalPage = page.getPages();
            page.setPageNo(page.getPageNo() + 1);
            result.addAll(workCommonEntities);
        }
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        realHandle(result);
        log.info("全量处理合同数据结束");
    }

    @Override
    public void handleHtData(String requestId) {
        if (StringUtils.isEmpty(requestId)) {
            return;
        }
        List<WorkCommonEntity> workCommonEntities = workBaseMapper.selectNeedHtList(requestId);
        realHandle(workCommonEntities);
    }

    void realHandle(List<WorkCommonEntity> result) {
        log.info("处理合同数据");
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        Map<String,HtCustomerWithBrand> customerWithBrandMap  = getCustomerWithBrandMap(result);

        List<OaWorkflow> oaWorkflows = new ArrayList<>();
        List<OaUserWorkflow> oaUserWorkflows = new ArrayList<>();
        for (int i = 0; i < result.size(); i++) {
            WorkCommonEntity workCommonEntityTemp = result.get(i);

            try {
                if(!customerWithBrandMap.containsKey(workCommonEntityTemp.getRequestId())){
                    log.info("不存在合同信息requestId:{}", workCommonEntityTemp.getRequestId());
                    continue;
                }

                HtCustomerWithBrand htCustomerWithBrand = customerWithBrandMap.get(workCommonEntityTemp.getRequestId());

                OaWorkflow oaWorkflow = new OaWorkflow();
                oaWorkflow.setId(workCommonEntityTemp.getRequestId());
                oaWorkflow.setRequestId(workCommonEntityTemp.getRequestId());
                oaWorkflow.setRequestName(workCommonEntityTemp.getRequestName());
                oaWorkflow.setCurrentNodeType(workCommonEntityTemp.getCurrentNodeType());
                oaWorkflow.setWorkFlowId(workCommonEntityTemp.getWorkFlowId());
                oaWorkflow.setWorkFlowName(workCommonEntityTemp.getWorkFlowName());
                oaWorkflow.setMainId(htCustomerWithBrand.getId());
                oaWorkflow.setSynTime(new Date());
                oaWorkflow.setCreateBy(workCommonEntityTemp.getCreateBy());
                oaWorkflow.setCreateId(workCommonEntityTemp.getCreateId());
                oaWorkflow.setCreateType(workCommonEntityTemp.getCreateType());
                DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyyy-MM-ddHH:mm:ss");
                DateTime dateTime = dateTimeFormatter.parseDateTime(workCommonEntityTemp.getCreateDate()+workCommonEntityTemp.getCreateTime());
                oaWorkflow.setCreateTime(dateTime.toDate());
                oaWorkflow.setMainId(htCustomerWithBrand.getId());
                oaWorkflow.setTabType(0L);
                oaWorkflows.add(oaWorkflow);
              //  iOaWorkflowService.saveOrUpdate(oaWorkflow);

                OaUserWorkflow oaUserWorkflow = new OaUserWorkflow();
                oaUserWorkflow.setRequestId(workCommonEntityTemp.getRequestId());
                oaUserWorkflow.setId(htCustomerWithBrand.getId());
                oaUserWorkflow.setMainId(htCustomerWithBrand.getId());
                oaUserWorkflow.setCustomerId(htCustomerWithBrand.getCustomerId());
                oaUserWorkflow.setHtStartTime(htCustomerWithBrand.getHtStartTime());
                oaUserWorkflow.setSynTime(new Date());
                oaUserWorkflow.setBrandId(htCustomerWithBrand.getArcBrandId());
                oaUserWorkflow.setCustomerName(htCustomerWithBrand.getCustomerName());
                oaUserWorkflows.add(oaUserWorkflow);
             //   iOaUserWorkflowService.saveOrUpdate(oaUserWorkflow);
            } catch (Exception e) {
                log.error("处理合同信息出现异常requestId:{} msg:{} e:{}", workCommonEntityTemp.getRequestId(), e.getMessage(), e);
            }
        }
        if(CollectionUtils.isEmpty(oaWorkflows) && CollectionUtils.isEmpty(oaUserWorkflows)){
            return;
        }
        List<List<OaWorkflow>> owPartList = Lists.partition(oaWorkflows, 500);
        owPartList.forEach(e->{
            if(CollectionUtils.isNotEmpty(e)){
                iOaWorkflowService.saveOrUpdateBatch(e);
            }

        });
        List<List<OaUserWorkflow>> ouwPratList = Lists.partition(oaUserWorkflows, 500);
        ouwPratList.forEach(e->{
            if(CollectionUtils.isNotEmpty(e)){
                iOaUserWorkflowService.saveOrUpdateBatch(e);

            }
        });
    }


    public List<WorkCommonEntity> getHtList(Page page, String id) {
        com.github.pagehelper.Page<WorkFlowEntity> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        workBaseMapper.selectNeedHtList(id);
        PageInfo<WorkCommonEntity> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();

    }

    Map<String, HtCustomerWithBrand> getCustomerWithBrandMap(List<WorkCommonEntity> result){
        List<List<WorkCommonEntity>> list = Lists.partition(result, 500);
        Map<String, HtCustomerWithBrand> htCustomerWithBrandMap = new HashMap<>();
        list.forEach(e -> {
            if (CollectionUtils.isNotEmpty(e)) {
                List<HtCustomerWithBrand> htCustomerWithBrands = formtableMain42Mapper.getHtCustomerWithBrand(e.stream().map(WorkCommonEntity::getRequestId).collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(htCustomerWithBrands)) {
                    htCustomerWithBrands.forEach(x -> {
                        htCustomerWithBrandMap.put(x.getRequestId(), x);
                    });
                }
            }
        });
        return htCustomerWithBrandMap;
    }
}
