package com.jnby.module.oa;

import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class ConvertTypeService {

    public String getType(String type) {
        if (type.equals("1")) {
            return "合同";
        } else if (type.equals("2")) {
            return "财务";
        } else if (type.equals("3")) {
            return "店仓";
        } else if (type.equals("4")) {
            return "装修";
        } else if (type.equals("5")) {
            return "解约";
        } else if (type.equals("6")) {
            return "销售";
        } else if (type.equals("7")) {
        }
        return null;
    }
}
