package com.jnby.module.oa;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import com.google.common.collect.Sets;
import com.jnby.common.Page;
import com.jnby.infrastructure.bojun.mapper.CCustomerMapper;
import com.jnby.infrastructure.box.model.OaUserWorkflow;
import com.jnby.infrastructure.box.model.OaWorkflow;
import com.jnby.infrastructure.oa.mapper.CwMapper;
import com.jnby.infrastructure.oa.mapper.DcMapper;
import com.jnby.infrastructure.oa.mapper.WorkBaseMapper;
import com.jnby.infrastructure.oa.model.*;
import com.jnby.module.crm.IOaUserWorkflowService;
import com.jnby.module.crm.IOaWorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 财务
 */
@Service
@Slf4j
public class PullCwDataServiceImpl implements IPullCwDataService{


    @Resource
    private WorkBaseMapper workBaseMapper;


    @Resource
    private CwMapper cwMapper;


    @Resource
    private IOaWorkflowService iOaWorkflowService;

    @Resource
    private IOaUserWorkflowService iOaUserWorkflowService;


    public static String type="CW";



    @Override
    public String getType() {
        return type;
    }

    @Override
    public void handleData() {
        log.info("全量处理财务数据开始");
        List<WorkCommonEntity> result = new ArrayList<>();
        int totalPage = 1;
        Page page = new Page(1, 1000);
        while (page.getPageNo() <= totalPage) {
            List<WorkCommonEntity> workCommonEntities = getCwList(page);
            if (CollectionUtils.isEmpty(workCommonEntities)) {
                break;
            }
            totalPage = page.getPages();
            page.setPageNo(page.getPageNo() + 1);
            result.addAll(workCommonEntities);
        }
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        realHandle(result);
        log.info("全量处理财务数据结束");
    }

    @Override
    public void handleData(String requestId) {
        if(StringUtils.isEmpty(requestId)){
            return;
        }
        List<WorkCommonEntity>  workCommonEntities =  workBaseMapper.selectNeedCwList(requestId);
        realHandle(workCommonEntities);
    }

    void realHandle(List<WorkCommonEntity> result) {
        log.info("处理财务数据");
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        Multimap<String, CustomerWithBrand>  customerWithBrandMapResult =  getCustomerWithBrandMap(result);

        List<OaWorkflow> oaWorkflows    = new ArrayList<>();
        List<OaUserWorkflow> oaUserWorkflows = new ArrayList<>();
        for (int i = 0; i < result.size(); i++) {
            WorkCommonEntity workCommonEntityTemp = result.get(i);
            if(!customerWithBrandMapResult.containsKey(workCommonEntityTemp.getRequestId())){
                continue;
            }
            List<CustomerWithBrand> customerWithBrandList = (List<CustomerWithBrand>)customerWithBrandMapResult.get(workCommonEntityTemp.getRequestId());
            try {
                OaWorkflow oaWorkflow = new OaWorkflow();
                oaWorkflow.setId(workCommonEntityTemp.getRequestId());
                oaWorkflow.setRequestId(workCommonEntityTemp.getRequestId());
                oaWorkflow.setRequestName(workCommonEntityTemp.getRequestName());
                oaWorkflow.setCurrentNodeType(workCommonEntityTemp.getCurrentNodeType());
                oaWorkflow.setWorkFlowId(workCommonEntityTemp.getWorkFlowId());
                oaWorkflow.setWorkFlowName(workCommonEntityTemp.getWorkFlowName());

                oaWorkflow.setSynTime(new Date());
                oaWorkflow.setCreateBy(workCommonEntityTemp.getCreateBy());
                oaWorkflow.setCreateId(workCommonEntityTemp.getCreateId());
                oaWorkflow.setCreateType(workCommonEntityTemp.getCreateType());
                DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyyy-MM-ddHH:mm:ss");
                DateTime dateTime = dateTimeFormatter.parseDateTime(workCommonEntityTemp.getCreateDate()+workCommonEntityTemp.getCreateTime());
                oaWorkflow.setCreateTime(dateTime.toDate());
                oaWorkflow.setTabType(1L);
                oaWorkflows.add(oaWorkflow);

                customerWithBrandList.forEach(customerWithBrandTemp -> {
                    OaUserWorkflow oaUserWorkflow = new OaUserWorkflow();
                    oaUserWorkflow.setBrandId(customerWithBrandTemp.getArcBrandId());
                    oaUserWorkflow.setCustomerName(customerWithBrandTemp.getCustomerName());
                    oaUserWorkflow.setId(customerWithBrandTemp.getId());
                    oaUserWorkflow.setMainId(customerWithBrandTemp.getId());
                    oaUserWorkflow.setCustomerId(customerWithBrandTemp.getCustomerId());

                    oaUserWorkflow.setRequestId(workCommonEntityTemp.getRequestId());
                    oaUserWorkflow.setSynTime(new Date());
                    oaUserWorkflows.add(oaUserWorkflow);
                });

            } catch (Exception e) {
                log.error("处理财务信息出现异常requestId:{} msg:{} e:{}", workCommonEntityTemp.getRequestId(), e.getMessage(), e);
            }
        }
        if(CollectionUtils.isEmpty(oaWorkflows) && CollectionUtils.isEmpty(oaUserWorkflows)){
            return;
        }

        List<List<OaWorkflow>> owPartList = Lists.partition(oaWorkflows, 500);
        owPartList.forEach(e->{
            if(CollectionUtils.isNotEmpty(e)){
                iOaWorkflowService.saveOrUpdateBatch(e);
            }

        });
        List<List<OaUserWorkflow>> ouwPratList = Lists.partition(oaUserWorkflows, 500);
        ouwPratList.forEach(e->{
            if(CollectionUtils.isNotEmpty(e)){
                iOaUserWorkflowService.saveOrUpdateBatch(e);

            }
        });
    }


    Multimap<String, CustomerWithBrand> getCustomerWithBrandMap(List<WorkCommonEntity> result) {
        List<List<WorkCommonEntity>> list = Lists.partition(result, 80);
        Multimap<String, CustomerWithBrand> customerWithBrandMap = ArrayListMultimap.create();
        list.forEach(e -> {
            if (CollectionUtils.isNotEmpty(e)) {
                List<CustomerWithBrand> customerWithBrands = cwMapper.getCwCustomerWithBrand(e.stream().map(WorkCommonEntity::getRequestId).collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(customerWithBrands)) {
                    customerWithBrands.forEach(x -> {
                        customerWithBrandMap.put(x.getRequestId(), x);
                    });
                }
            }
        });

        list.forEach(e -> {
            if (CollectionUtils.isNotEmpty(e)) {
                List<CustomerWithBrand> customerWithBrands25 = cwMapper.getCwCustomerWithBrand25(e.stream().map(WorkCommonEntity::getRequestId).collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(customerWithBrands25)) {
                    customerWithBrands25.forEach(x -> {
                        customerWithBrandMap.put(x.getRequestId(), x);
                    });
                }
            }
        });

        list.forEach(e -> {
            if (CollectionUtils.isNotEmpty(e)) {
                List<CustomerWithBrand> customerWithBrands471 = cwMapper.getCwCustomerWithBrand471(e.stream().map(WorkCommonEntity::getRequestId).collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(customerWithBrands471)) {
                    customerWithBrands471.forEach(x -> {
                        customerWithBrandMap.put(x.getRequestId(), x);
                    });
                }

            }
        });
        return customerWithBrandMap;
    }


    public List<WorkCommonEntity> getCwList(Page page) {
        com.github.pagehelper.Page<WorkFlowEntity> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        workBaseMapper.selectNeedCwList(null);
        PageInfo<WorkCommonEntity> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();

    }
}
