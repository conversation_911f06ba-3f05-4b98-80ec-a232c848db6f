package com.jnby.module.oa;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

/**
 * <AUTHOR>
 */
public class TypeConstant {
    /**
     * 合同
     * XMGL（HT）-02、合同审批申请流程（经销业务）   formtable_main_42         -42
     *
     */
    public static TypeWithFormId HT = new TypeWithFormId("合同", "HT"
            , Sets.newHashSet("-42")
            , Lists.newArrayList("-42"));

    /**
     * 财务
     * JXGL-03、（客户）经销商-装修支持申请流程            formtable_main_25                                  -25
     * JXGL-04、经销商-装修支持结算申请流程（第一次结算）    formtable_main_36                                  -36
     * JXGL-05、经销商-装修支持结算申请流程（第二次结算）    formtable_main_278                                 -278
     * JXGL-07、经销商-代销结账单申请流程                 formtable_main_756                                 -756
     * JXGL-11、经销商-扣/返款流程                       formtable_main_471(formtable_main_471_dt1)         -471
     *
     */
    public static TypeWithFormId CW = new TypeWithFormId("财务", "CW"
            , Sets.newHashSet("-25", "-36", "-278", "-756", "-471")
            , Lists.newArrayList("-25", "-36", "-278", "-756", "-471"));

    /**
     * 店仓
     * JXGL-01、经销商-开店及建档申请流程       formtable_main_53     -53
     * JXGL-06、（客户）经销商-撤店申请流程     formtable_main_261    -261
     */
    public static TypeWithFormId DC = new TypeWithFormId("店仓", "DC"
            , Sets.newHashSet("-53", "-261")
            , Lists.newArrayList("-53", "-261"));



    /**
     * 装修
     * JXGL-02、（客户）经销商-装修申请流程   formtable_main_26  -26
     */
    public static TypeWithFormId ZX = new TypeWithFormId("装修", "ZX"
            , Sets.newHashSet("-26")
            , Lists.newArrayList("-26"));


    /**
     * 销售
     * JXGL-10、（客户）经销商-信用申请流程流程表单-新     formtable_main_469                                                      -469
     * JXGL-18、（总部）经销商-退换货申请流程            formtable_main_457（formtable_main_457_dt1 formtable_main_457_dt2）      -457
     * JXGL-19、经销商-货品出库折扣申请流程             formtable_main_422（formtable_main_422_dt1）                              -422
     * JXGL-20、经销商-退货率设置申请流程                 formtable_main_423（formtable_main_423_dt1）                           -423
     * JXGL-23、经销商-期货转放量申请流程                formtable_main_443                                                     -443
     * ZYGL-04、直营/经销-内淘不发货设置申请流程            formtable_main_545（formtable_main_545_dt1）                          -545
     * ZYGL-09、直营/经销-（客户）销售及会员活动申请流程     formtable_main_790                                                    -790
     */
    public static TypeWithFormId XS = new TypeWithFormId("销售", "XS"
            , Sets.newHashSet("-469", "-457", "-422", "-423", "-443","-545", "-790")
            , Lists.newArrayList("-469", "-457", "-422", "-423", "-443","-545", "-790"));

    /**
     * 准入
     * OA-经销商准入及建档申请
     * formtable_main_853正式  -853
     * formtable_main_846测试  -846
     */
    public static TypeWithFormId ZR = new TypeWithFormId("准入", "ZR"
            , Sets.newHashSet("-853")
            , Lists.newArrayList("-853"));



    /**
     * 解约
     * XXZX-06、加密狗申请、退回流程           formtable_main_88  -48
     * JXGL-08、经销商-解约结算/对账申请流程    formtable_main_48  -88
     */
    public static TypeWithFormId JY = new TypeWithFormId("解约", "JY"
            , Sets.newHashSet("-48", "-88")
            , Lists.newArrayList("-48", "-88"));

}
