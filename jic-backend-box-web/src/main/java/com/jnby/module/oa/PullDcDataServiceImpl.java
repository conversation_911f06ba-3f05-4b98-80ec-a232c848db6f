package com.jnby.module.oa;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.jnby.common.Page;
import com.jnby.dto.bojun.CustomerDto;
import com.jnby.dto.oa.FormTableMain261Dto;
import com.jnby.dto.oa.FormTableMain53Dto;
import com.jnby.infrastructure.bojun.mapper.CCustomerMapper;
import com.jnby.infrastructure.box.model.OaUserWorkflow;
import com.jnby.infrastructure.box.model.OaWorkflow;
import com.jnby.infrastructure.oa.mapper.DcMapper;
import com.jnby.infrastructure.oa.mapper.WorkBaseMapper;
import com.jnby.infrastructure.oa.model.CustomerWithBrand;
import com.jnby.infrastructure.oa.model.WorkCommonEntity;
import com.jnby.infrastructure.oa.model.WorkFlowEntity;
import com.jnby.module.crm.IOaUserWorkflowService;
import com.jnby.module.crm.IOaWorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class PullDcDataServiceImpl  implements  IPullDcDataService{

    @Resource
    private WorkBaseMapper workBaseMapper;

    @Resource
    private DcMapper dcMapper;

    @Resource
    private IOaWorkflowService iOaWorkflowService;

    @Resource
    private IOaUserWorkflowService iOaUserWorkflowService;



    @Override
    public  void handleData() {
        log.info("全量处理店仓数据开始");
        List<WorkCommonEntity> result = new ArrayList<>();
        int totalPage = 1;
        Page page = new Page(1, 5000);
        while (page.getPageNo() <= totalPage) {
            List<WorkCommonEntity> workCommonEntities = getDcList(page);
            if (CollectionUtils.isEmpty(workCommonEntities)) {
                break;
            }
            totalPage = page.getPages();
            page.setPageNo(page.getPageNo() + 1);
            result.addAll(workCommonEntities);
        }
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        realHandle(result);
        log.info("全量处理店仓数据结束");

    }

    /**
     * 拉取指定requestId
     *
     * @param requestId
     */
    @Override
    public void handleData(String requestId) {
        if(StringUtils.isEmpty(requestId)){
            return;
        }
        List<WorkCommonEntity> workCommonEntities =  workBaseMapper.selectNeedDcList(requestId);
        realHandle(workCommonEntities);
    }


    void realHandle(List<WorkCommonEntity> result) {
        log.info("处理店铺数据");
        if (CollectionUtils.isEmpty(result)) {
            return;
        }

        Map<String, CustomerWithBrand>  customerWithBrandMapResult =  getCustomerWithBrandMap(result);
        List<OaWorkflow> oaWorkflows    = new ArrayList<>();
        List<OaUserWorkflow> oaUserWorkflows = new ArrayList<>();

        for (int i = 0; i < result.size(); i++) {
            WorkCommonEntity workCommonEntityTemp = result.get(i);
            CustomerWithBrand customerWithBrandTemp = customerWithBrandMapResult.get(workCommonEntityTemp.getRequestId());
            if (workCommonEntityTemp == null) {
                continue;
            }

            OaWorkflow oaWorkflow = new OaWorkflow();
            oaWorkflow.setId(workCommonEntityTemp.getRequestId());
            oaWorkflow.setRequestId(workCommonEntityTemp.getRequestId());
            oaWorkflow.setRequestName(workCommonEntityTemp.getRequestName());
            oaWorkflow.setCurrentNodeType(workCommonEntityTemp.getCurrentNodeType());
            oaWorkflow.setWorkFlowId(workCommonEntityTemp.getWorkFlowId());
            oaWorkflow.setWorkFlowName(workCommonEntityTemp.getWorkFlowName());
            oaWorkflow.setSynTime(new Date());
            oaWorkflow.setCreateBy(workCommonEntityTemp.getCreateBy());
            oaWorkflow.setCreateId(workCommonEntityTemp.getCreateId());
            oaWorkflow.setCreateType(workCommonEntityTemp.getCreateType());
            oaWorkflow.setTabType(3L);
            DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyyy-MM-ddHH:mm:ss");
            DateTime dateTime = dateTimeFormatter.parseDateTime(workCommonEntityTemp.getCreateDate()+workCommonEntityTemp.getCreateTime());
            oaWorkflow.setCreateTime(dateTime.toDate());

            OaUserWorkflow oaUserWorkflow = new OaUserWorkflow();
            oaUserWorkflow.setRequestId(workCommonEntityTemp.getRequestId());
            oaUserWorkflow.setSynTime(new Date());
            oaWorkflow.setMainId(customerWithBrandTemp.getId());
            oaUserWorkflow.setId(customerWithBrandTemp.getId());
            oaUserWorkflow.setMainId(customerWithBrandTemp.getId());
            oaUserWorkflow.setCustomerId(customerWithBrandTemp.getCustomerId());
            oaUserWorkflow.setBrandId(customerWithBrandTemp.getArcBrandId());
            oaUserWorkflow.setCustomerName(customerWithBrandTemp.getCustomerName());


            oaWorkflows.add(oaWorkflow);
            oaUserWorkflows.add(oaUserWorkflow);
        }
        if(CollectionUtils.isEmpty(oaWorkflows) && CollectionUtils.isEmpty(oaUserWorkflows)){
            return;
        }

        List<List<OaWorkflow>> owPartList = Lists.partition(oaWorkflows, 500);
        owPartList.forEach(e->{
            if(CollectionUtils.isNotEmpty(e)){
                iOaWorkflowService.saveOrUpdateBatch(e);
            }

        });
        List<List<OaUserWorkflow>> ouwPratList = Lists.partition(oaUserWorkflows, 500);
        ouwPratList.forEach(e->{
            if(CollectionUtils.isNotEmpty(e)){
                iOaUserWorkflowService.saveOrUpdateBatch(e);

            }
        });
    }


    Map<String, CustomerWithBrand> getCustomerWithBrandMap(List<WorkCommonEntity> result) {
        List<List<WorkCommonEntity>> list = Lists.partition(result, 800);
        Map<String, CustomerWithBrand> customerWithBrandMap = new HashMap<>();
        list.forEach(e -> {
            if (CollectionUtils.isNotEmpty(e)) {
                List<CustomerWithBrand> customerWithBrands = dcMapper.getDcCustomerWithBrand(e.stream().map(WorkCommonEntity::getRequestId).collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(customerWithBrands)) {
                    customerWithBrands.forEach(x -> {
                        customerWithBrandMap.put(x.getRequestId(), x);
                    });
                }
            }
        });
        return customerWithBrandMap;
    }


    public List<WorkCommonEntity> getDcList(Page page) {
        com.github.pagehelper.Page<WorkFlowEntity> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        workBaseMapper.selectNeedDcList(null);
        PageInfo<WorkCommonEntity> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();

    }

}
