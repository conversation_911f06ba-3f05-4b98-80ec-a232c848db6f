package com.jnby.module.oa;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jnby.common.Page;
import com.jnby.dto.bojun.CustomerDto;
import com.jnby.dto.oa.FormTableMain853Dto;
import com.jnby.infrastructure.bojun.mapper.CCustomerMapper;
import com.jnby.infrastructure.box.model.OaUserWorkflow;
import com.jnby.infrastructure.box.model.OaWorkflow;
import com.jnby.infrastructure.oa.mapper.FormTableMain853Mapper;
import com.jnby.infrastructure.oa.mapper.WorkBaseMapper;
import com.jnby.infrastructure.oa.mapper.ZrMapper;
import com.jnby.infrastructure.oa.model.CustomerWithBrand;
import com.jnby.infrastructure.oa.model.Fm853WithMain413;
import com.jnby.infrastructure.oa.model.WorkCommonEntity;
import com.jnby.infrastructure.oa.model.WorkFlowEntity;
import com.jnby.module.crm.CrmService;
import com.jnby.module.crm.IOaUserWorkflowService;
import com.jnby.module.crm.IOaWorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PullZrDataServiceImpl implements IPullZrDataService {

    @Resource
    private WorkBaseMapper workBaseMapper;

    @Resource
    private CCustomerMapper cCustomerMapper;


    @Resource
    private IOaWorkflowService iOaWorkflowService;

    @Resource
    private IOaUserWorkflowService iOaUserWorkflowService;


    @Resource
    private FormTableMain853Mapper formTableMain853Mapper;

    static String wfiDentKey = "151262";

    @Resource
    private ZrMapper zrMapper;

    @Resource
    private CrmService crmService;


    static Map<String,String> brandNameWithBrandId =  new HashMap<String, String>() {{
        put("JNBY", "2");
        put("速写", "3");
        put("jnby by JNBY", "4");
        put("LESS", "5");
        put("JNBYHOME", "17");
        put("Pomme de terre", "12");
        put("悖论集", "32");
        put("REVERB", "47");
        put("SAMO", "42");
        put("A PERSONAL NOTE 73", "57");
        put("RE；RE；RE；LAB", "77");
    }};


    /**
     * 70761  测试 准入
     */
    @Override
    public void handleData() {
        log.info("全量处理准入数据开始");
        List<WorkCommonEntity> result = new ArrayList<>();
        int totalPage = 1;
        Page page = new Page(1, 5000);
        while (page.getPageNo() <= totalPage) {
            List<WorkCommonEntity> workCommonEntities = getZrList(page, null);
            if (CollectionUtils.isEmpty(workCommonEntities)) {
                break;
            }
            totalPage = page.getPages();
            page.setPageNo(page.getPageNo() + 1);
            result.addAll(workCommonEntities);
        }
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        realHandle(result);
        log.info("全量处理准入数据结束");
    }


    /**
     * 拉取指定requestId
     *
     * @param requestId
     */
    @Override
    public void handleData(String requestId) {
        List<WorkCommonEntity> workCommonEntities = workBaseMapper.selectNeedZrList(requestId);
        realHandle(workCommonEntities);
    }


    void realHandle(List<WorkCommonEntity> result) {
        log.info("处理准入数据");
        if (CollectionUtils.isEmpty(result)) {
            return;
        }

        Map<String, CustomerWithBrand> customerWithBrandMapResult = getCustomerWithBrandMap(result);


        for (int i = 0; i < result.size(); i++) {
            WorkCommonEntity workCommonEntityTemp = result.get(i);

            try {
                CustomerWithBrand customerWithBrandTemp = customerWithBrandMapResult.get(workCommonEntityTemp.getRequestId());
                if (customerWithBrandTemp == null) {
                    log.info("不存在准入信息requestId:{}", workCommonEntityTemp.getRequestId());
                    continue;
                }

                OaWorkflow oaWorkflow = new OaWorkflow();
                oaWorkflow.setId(workCommonEntityTemp.getRequestId());
                oaWorkflow.setRequestId(workCommonEntityTemp.getRequestId());
                oaWorkflow.setRequestName(workCommonEntityTemp.getRequestName());
                oaWorkflow.setCurrentNodeType(workCommonEntityTemp.getCurrentNodeType());
                oaWorkflow.setWorkFlowId(workCommonEntityTemp.getWorkFlowId());
                oaWorkflow.setWorkFlowName(workCommonEntityTemp.getWorkFlowName());

                oaWorkflow.setSynTime(new Date());
                oaWorkflow.setCreateBy(workCommonEntityTemp.getCreateBy());
                oaWorkflow.setCreateId(workCommonEntityTemp.getCreateId());
                oaWorkflow.setCreateType(workCommonEntityTemp.getCreateType());
                DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyyy-MM-ddHH:mm:ss");
                DateTime dateTime = dateTimeFormatter.parseDateTime(workCommonEntityTemp.getCreateDate()+workCommonEntityTemp.getCreateTime());
                oaWorkflow.setCreateTime(dateTime.toDate());
                oaWorkflow.setTabType(6L);
                iOaWorkflowService.saveOrUpdate(oaWorkflow);

                OaUserWorkflow oaUserWorkflow = new OaUserWorkflow();
                oaUserWorkflow.setRequestId(workCommonEntityTemp.getRequestId());
                oaUserWorkflow.setId(customerWithBrandTemp.getId());
                oaUserWorkflow.setCustomerId(customerWithBrandTemp.getCustomerId());
                oaUserWorkflow.setCustomerName(customerWithBrandTemp.getCustomerName());
                oaUserWorkflow.setSynTime(new Date());
                oaUserWorkflow.setBrandId(customerWithBrandTemp.getArcBrandId());
                iOaUserWorkflowService.saveOrUpdate(oaUserWorkflow);
            } catch (Exception e) {
                log.error("处理合同信息出现异常requestId:{} msg:{} e:{}", workCommonEntityTemp.getRequestId(), e.getMessage(), e);
            }
        }
    }


    public List<WorkCommonEntity> getZrList(Page page, String id) {
        com.github.pagehelper.Page<WorkFlowEntity> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        workBaseMapper.selectNeedZrList(id);
        PageInfo<WorkCommonEntity> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();

    }


    Map<String, CustomerWithBrand> getCustomerWithBrandMap(List<WorkCommonEntity> result) {
        List<List<WorkCommonEntity>> list = Lists.partition(result, 800);
        Map<String, CustomerWithBrand> customerWithBrandMap = new HashMap<>();
        list.forEach(e -> {
            if (CollectionUtils.isNotEmpty(e)) {
                List<CustomerWithBrand> customerWithBrands = zrMapper.getZrCustomerWithBrand(e.stream().map(WorkCommonEntity::getRequestId).collect(Collectors.toList()));
                if (CollectionUtils.isNotEmpty(customerWithBrands)) {
                    // 筛选得到 品牌伯俊id为空的 再次查询 关联表进行补充
                    handleRequestIdWithFm853WithMain413(customerWithBrands);

                    customerWithBrands.forEach(x -> {
                        customerWithBrandMap.put(x.getRequestId(), x);
                    });
                }
            }
        });
        return customerWithBrandMap;
    }


    void handleRequestIdWithFm853WithMain413(List<CustomerWithBrand> customerWithBrands) {
        List<String> zrRequestIds = customerWithBrands.stream().filter(x -> StringUtils.isBlank(x.getArcBrandId())).map(CustomerWithBrand::getRequestId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(zrRequestIds)) {
            return;
        }
        Map<String, String> requestId413WithBrandName = new HashMap<>();
        List<Fm853WithMain413> fm853WithMain413s = zrMapper.getBrandWithRequestId(zrRequestIds);
        fm853WithMain413s.forEach(e -> {
            if (StringUtils.isNotBlank(e.getBrandName())) {
                requestId413WithBrandName.put(e.getRequestId(), e.getBrandName());
            }
        });


        customerWithBrands.forEach(e -> {
            if (StringUtils.isEmpty(e.getArcBrandId()) && requestId413WithBrandName.containsKey(e.getRequestId())) {
                String brandNameTemp = requestId413WithBrandName.get(e.getRequestId());
                // 获取转换关系
                if (brandNameWithBrandId.containsKey(brandNameTemp)) {
                    e.setArcBrandId(brandNameWithBrandId.get(brandNameTemp));
                    log.info("处理转换关系requestId:{} arcBrandId:{}", e.getRequestId(), e.getArcBrandId());
                }
            }
        });
    }
}
