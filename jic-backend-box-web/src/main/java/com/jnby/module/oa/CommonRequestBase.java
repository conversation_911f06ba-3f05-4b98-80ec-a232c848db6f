package com.jnby.module.oa;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jnby.common.Page;
import com.jnby.infrastructure.oa.mapper.WorkBaseMapper;
import com.jnby.infrastructure.oa.model.WorkBaseWithFormEntity;
import com.jnby.infrastructure.oa.model.WorkCommonEntity;
import com.jnby.infrastructure.oa.model.WorkFlowEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.joda.time.LocalDate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CommonRequestBase implements ICommonRequestBase {

    @Resource
    private WorkBaseMapper workBaseMapper;

    @Resource
    private IPullHtDataService iPullHtDataService;

    @Resource
    private IPullDcDataService iPullDcDataService;

    @Resource
    private IPullZxDataService iPullZxDataService;

    @Resource
    private IPullZrDataService iPullZrDataService;

    @Resource
    private IPullCwDataService iPullCwDataService;

    @Resource
    private IPullXsDataService iPullXsDataService;

    @Resource
    private IPullJyDataService iPullJyDataService;


    @Override
    public void handleRequestBase(String requestId) {
        log.info("处理oa数据requestId:{}", requestId);
        String formId = workBaseMapper.selectFromIdByRequestId(requestId);
        handleRequestBase(requestId, formId);
    }

    public void handleRequestBase(String requestId, String formId) {
        if (StringUtils.isEmpty(formId)) {
            return;
        }
        try {
            log.info("处理oa数据requestId:{} formId:{}", requestId, formId);
            // 合同
            if (TypeConstant.HT.getFromIdSet().contains(formId)) {
                log.info("处理合同oa数据requestId:{} formId:{}", requestId, formId);
                iPullHtDataService.handleHtData(requestId);
                return;
            }// 店铺
            if (TypeConstant.DC.getFromIdSet().contains(formId)) {
                log.info("处理店铺oa数据requestId:{} formId:{}", requestId, formId);
                iPullDcDataService.handleData(requestId);
                return;
            }// 准入
            if (TypeConstant.ZR.getFromIdSet().contains(formId)) {
                log.info("处理准入oa数据requestId:{} formId:{}", requestId, formId);
                iPullZrDataService.handleData(requestId);
                return;
            }
            // 装修
            if (TypeConstant.ZX.getFromIdSet().contains(formId)) {
                log.info("处理装修oa数据requestId:{} formId:{}", requestId, formId);
                iPullZxDataService.handleData(requestId);
                return;
            }// 销售
            if (TypeConstant.XS.getFromIdSet().contains(formId)) {
                log.info("处理销售oa数据requestId:{} formId:{}", requestId, formId);
                iPullXsDataService.handleData(requestId);
                return;
            }// 财务
            if (TypeConstant.CW.getFromIdSet().contains(formId)) {
                log.info("处理财务oa数据requestId:{} formId:{}", requestId, formId);
                iPullCwDataService.handleData(requestId);
                return;
            }
            // 解约
            if (TypeConstant.JY.getFromIdSet().contains(formId)) {
                log.info("处理解约oa数据requestId:{} formId:{}", requestId, formId);
                iPullJyDataService.handleData(requestId);
                return;
            }
            return;
        } catch (Exception e) {
            log.error("处理oa数据requestId:{} 异常msg:{} e:{}", requestId, e.getMessage(), e);
        }
    }

    @Override
    public void handle2Days() {
        // 当前时间 三天的时间 按照指定格式
        LocalDate today = new org.joda.time.LocalDate();
        LocalDate yesterday = new org.joda.time.LocalDate().plusDays(-1);

        List<String> days = new ArrayList<>();
        days.add(today.toString());
        days.add(yesterday.toString());

        // 获取数据 更新时间为近2天数
        List<WorkBaseWithFormEntity> result = new ArrayList<>();
        getPageNeedForm(result, TypeConstant.HT.getFromIdList(), days);
        getPageNeedForm(result, TypeConstant.DC.getFromIdList(), days);
        getPageNeedForm(result, TypeConstant.ZR.getFromIdList(), days);
        getPageNeedForm(result, TypeConstant.ZX.getFromIdList(), days);
        getPageNeedForm(result, TypeConstant.XS.getFromIdList(), days);
        getPageNeedForm(result, TypeConstant.CW.getFromIdList(), days);
        getPageNeedForm(result, TypeConstant.JY.getFromIdList(), days);

        if (CollectionUtils.isEmpty(result)) {
            log.info("本次无数据");
            return;
        }
        result.forEach(e -> {
            handleRequestBase(e.getRequestId(), e.getFormId());
        });
    }

    void getPageNeedForm(List<WorkBaseWithFormEntity> result, List<String> formIds, List<String> days) {
        int totalPage = 1;
        Page page = new Page(1, 200);
        while (page.getPageNo() <= totalPage) {
            List<WorkBaseWithFormEntity> workBaseWithFormEntities = getNeedFormList(page, formIds, days);

            if (CollectionUtils.isEmpty(workBaseWithFormEntities)) {
                break;
            }
            totalPage = page.getPages();
            page.setPageNo(page.getPageNo() + 1);
            result.addAll(workBaseWithFormEntities);
        }
    }


    public List<WorkBaseWithFormEntity> getNeedFormList(Page page, List<String> formIds, List<String> days) {
        com.github.pagehelper.Page<WorkBaseWithFormEntity> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        workBaseMapper.selectNeedFormList(formIds, days);
        PageInfo<WorkBaseWithFormEntity> pageInfo = new PageInfo(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return pageInfo.getList();

    }

}
