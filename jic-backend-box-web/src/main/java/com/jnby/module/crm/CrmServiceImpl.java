package com.jnby.module.crm;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jnby.authority.api.ISysBaseAPI;
import com.jnby.authority.api.dto.Result;
import com.jnby.authority.common.system.vo.SysPermissionDataRuleModel;
import com.jnby.common.Page;
import com.jnby.common.enums.CrmStatus;
import com.jnby.common.enums.IdConstant;
import com.jnby.common.util.*;
import com.jnby.common.util.excel.BaseNoModelDataAbstractListener;
import com.jnby.common.util.excel.EasyExcelUtil;
import com.jnby.dto.*;
import com.jnby.dto.bojun.CstoreSumDto;
import com.jnby.dto.bojun.CustomerDto;
import com.jnby.dto.oa.DingMsgParam;
import com.jnby.dto.oa.FormattableMain42Dto;
import com.jnby.dto.oa.FormattableMain48Dto;
import com.jnby.entity.CreateUserParam;
import com.jnby.infrastructure.bojun.mapper.*;
import com.jnby.infrastructure.bojun.model.*;
import com.jnby.infrastructure.box.mapper.*;
import com.jnby.infrastructure.box.model.*;
import com.jnby.infrastructure.oa.mapper.*;
import com.jnby.infrastructure.oa.model.DingtalkSendMsgVo;
import com.jnby.infrastructure.oa.model.Hrmresource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springcenter.product.api.enums.IsDeleteEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

import static com.jnby.dto.CStoreConstant.OA_BRAND_NAME_BOJUN;

@Service
@Slf4j
@RefreshScope
public class CrmServiceImpl implements CrmService{

    // 经销商货品 关联NC_ACCOUNT_ID
    public static final Integer NC_DOC_ACCOUNT_ID = 4;

    @Autowired
    private CrmCustomerMainMapper crmCustomerMainMapper;

    @Autowired
    private CRegionsMapper cRegionsMapper;

    @Autowired
    private CrmGenCodeMapper crmGenCodeMapper;

    @Autowired
    private CrmCustomerRelationMapper crmCustomerRelationMapper;

    @Autowired
    private CrmCustomerLabelMapper crmCustomerLabelMapper;

    @Autowired
    private CCustomerMapper cCustomerMapper;
    
    @Autowired
    private FormtableMain48Mapper formtableMain48Mapper;

    @Autowired
    private CStoreMapper cStoreMapper;

    @Autowired
    private CArcbrandMapper cArcbrandMapper;

    @Autowired
    private BrandRuleContrastMapper brandRuleContrastMapper;

    @Autowired
    private ISysBaseAPI sysBaseAPI;

//    @Autowired
//    private FormtableMain53Mapper formtableMain53Mapper;

    private final Lock lock = new ReentrantLock();


    /**
     * 定义初始化变量
     */
    private final AtomicInteger num = new AtomicInteger(0);


    @Autowired
    private CAreaMapper cAreaMapper;

    @Autowired
    private CArea1Mapper cArea1Mapper;

    @Autowired
    private CCusdisdefMapper cCusdisdefMapper;

    @Autowired
    private UfKhidMapper ufKhidMapper;

    @Autowired
    private FormtableMain42Mapper formtableMain42Mapper;


    public static final String STR = "D";

    @Autowired
    private NcCustomerMapper ncCustomerMapper;

    @Autowired
    private NcCustomerCompareMapper ncCustomerCompareMapper;

    @Autowired
    private NcCustomerCompareZgMapper ncCustomerCompareZgMapper;

    @Autowired
    private FormTableMain853Mapper formTableMain853Mapper;

    @Autowired
    private HrEmployeeMapper hrEmployeeMapper;

    @Autowired
    private HrmresourceMapper hrmresourceMapper;

    @Autowired
    private CrmOrderGoodsMapper crmOrderGoodsMapper;

    @Value("${crm.oa.secrit}")
    private String crmSecrit;

    @Value("${crm.oa.spk}")
    private String crmSpk;

    @Value("${crm.oa.appid}")
    private String crmAppid;

    @Value("${crm.oa.url}")
    private String crmUrl;

    @Value("${crm.oa.appkey}")
    private String crmAppkey;


    private CStoreattribvalueMapper cStoreattribvalueMapper;

    @Resource
    private UsersMapper usersMapper;

    @Resource
    private BusinessControlMapper businessControlMapper;


    @Resource
    private DingtalkSendMsgMapper dingtalkSendMsgMapper;


    @Override
    public void createOrUpdate(CrmCreateDto requestData) {
        CrmCustomerMain crmCustomerMain = new CrmCustomerMain();
        BeanUtils.copyProperties(requestData,crmCustomerMain);
        if(StringUtils.isBlank(requestData.getId())){
            crmCustomerMain.setId(IdLeaf.getId(IdConstant.CRM_CUSTOMER_MAIN));
            crmCustomerMain.setStatus(CrmStatus.INTERVIEW_STATUS);
            // 生成客户编号
            crmCustomerMain.setCrmId(genCrmId(crmCustomerMain));
            crmCustomerMain.setCreateTime(new Date());
            crmCustomerMain.setUpdateTime(new Date());
            crmCustomerMainMapper.insert(crmCustomerMain);
            // 将数据存储 OA的  uf_khid 表
            UfKhid ufKhid = new UfKhid();
            ufKhid.setKhid(crmCustomerMain.getCrmId());
            ufKhid.setName(crmCustomerMain.getCompanyName());
            ufKhidMapper.insertSelective(ufKhid);
        }else{
            crmCustomerMain.setUpdateTime(new Date());
            crmCustomerMainMapper.updateById(crmCustomerMain);
        }
    }

    private String createOaFlow(CrmCustomerMain crmCustomerMain1, CreateOaFlowDto createOaFlowDto) {
        if(StringUtils.isBlank(createOaFlowDto.getLoginId())){
            log.info("loginId为空，不进行下一步处理");
            throw new RuntimeException("loginId为空，不进行下一步处理");
        }
        // 获取userId
        String userId = getUserId(createOaFlowDto.getLoginId());
        if(StringUtils.isBlank(userId)){
            log.info("未能获取userId，不进行下一步处理");
            throw new RuntimeException("通过OA未能获取userId，不进行下一步处理");
        }

        // 获取oa token
        String oaToken = getOaToken();
        Map<String,String> headerMap = new HashMap<>();
        //设置参数
        headerMap.put("token",oaToken);
        headerMap.put("appid",crmAppid);
        // 通过SPK对oaid进行加密操作
        RSA rsa = new RSA(null,crmSpk);
        String encryptOaId = rsa.encryptBase64(userId, CharsetUtil.CHARSET_UTF_8, KeyType.PublicKey);
        headerMap.put("userid",encryptOaId);
        headerMap.put("Content-Type","application/x-www-form-urlencoded; charset=utf-8");
        headerMap.put("Content-Length","0");
        okhttp3.Headers headers = Headers.of(headerMap);
        //请求头
        Map<String,String> params = new HashMap<>();
        // 封装参数
        params.put("detailData",JSONObject.toJSONString(new ArrayList<>()));
        List<Map<String,String>> mainDataList = new ArrayList<>();
        // 设置主表参数
        //kcqjnid   江南ID
        //qylxrdhsj 企业联系人手机号
        //qyzylxr   企业联系人
        //sqr       申请人
        //lclx      默认 0 新建准入主体及建档

        Map<String,String> kcqjnid = new HashMap<>();
        kcqjnid.put("fieldName","kcqjnid");
        kcqjnid.put("fieldValue",crmCustomerMain1.getCrmId());
        mainDataList.add(kcqjnid);

        Map<String,String> qylxrdhsj = new HashMap<>();
        qylxrdhsj.put("fieldName","qylxrdhsj");
        qylxrdhsj.put("fieldValue",crmCustomerMain1.getConnectPhone());
        mainDataList.add(qylxrdhsj);

        Map<String,String> qyzylxr = new HashMap<>();
        qyzylxr.put("fieldName","qyzylxr");
        qyzylxr.put("fieldValue",crmCustomerMain1.getConnectName());
        mainDataList.add(qyzylxr);

        Map<String,String> sqr = new HashMap<>();
        sqr.put("fieldName","sqr");
        sqr.put("fieldValue",userId);
        mainDataList.add(sqr);

        Map<String,String> lclx = new HashMap<>();
        lclx.put("fieldName","lclx");
        lclx.put("fieldValue",createOaFlowDto.getLclx());
        mainDataList.add(lclx);


        Map<String,String> ly = new HashMap<>();
        ly.put("fieldName","ly");
        ly.put("fieldValue","0");
        mainDataList.add(ly);

        params.put("mainData",JSONObject.toJSONString(mainDataList));
        Map<String,String> otherParams = new HashMap<>();
        otherParams.put("isnextflow","0");
        params.put("otherParams",JSONObject.toJSONString(otherParams));
        if(createOaFlowDto.getLclx().equals("0")){
            createOaFlowDto.setLclxStr("新建准入主体及建档");
        }else if(createOaFlowDto.getLclx().equals("1")){
            createOaFlowDto.setLclxStr("已有主体仅建档");
        }else{
            createOaFlowDto.setLclxStr("现有经销商主体变更");
        }

        params.put("requestName",(crmCustomerMain1.getCustomerName() == null ? "":crmCustomerMain1.getCustomerName())  +createOaFlowDto.getLclxStr());
        // 默认建档流程id 145761 测试环境  正式环境  151262
        params.put("workflowId","151262");

        log.info("请求创建OA流程 入参 = {}  header = {}",JSONObject.toJSONString(params),JSONObject.toJSONString(headerMap));
        // 请求参数
        String resultJson = HttpUtils.postForm(crmUrl + "/api/workflow/paService/doCreateRequest", params, headers);
        if(StringUtils.isNotBlank(resultJson)){
            log.info("请求创建OA流程 返回参数 = {}",resultJson);
            Map map = JSONObject.parseObject(resultJson, Map.class);
            if(String.valueOf(map.get("code")).equals("SUCCESS")){
                Object o = map.get("data");
                String jsonString = JSONObject.toJSONString(o);
                Map map1 = JSONObject.parseObject(jsonString, Map.class);
                Object o1 = map1.get("requestid");
                return o1+"";
            }else{
                throw new RuntimeException("暂无OA准入权限，请联系OA管理员");
            }
        }
        return null;
    }

    private String getUserId(String loginId) {
        //http://192.168.0.16:9999/api/esb/execute
        //header里传
        //appkey     64caed2d-ab47-4116-b1be-6caec02a2fa1
        //eventkey     queryuserid
        //
        //请求参数
        //{
        //    "loginid": "qiudanfang18067"
        //}
        Map<String,String> headerMap = new HashMap<>();
        //设置参数
        headerMap.put("appkey",crmAppkey);
        headerMap.put("eventkey","queryuserid");
        okhttp3.Headers headers = Headers.of(headerMap);
        Map<String,Object> paramsPost = new HashMap<>();
        paramsPost.put("loginid",loginId);
        log.info("请求参数 /api/esb/execute params = {} headerMap = {}",JSONObject.toJSONString(paramsPost),JSONObject.toJSONString(headerMap));
        String post = HttpUtils.post(crmUrl + "/api/esb/execute", paramsPost, headers);
        log.info("返回参数 /api/esb/execute result = {}",post);
        if(StringUtils.isNotBlank(post)){
            JSONObject jsonObject = JSONObject.parseObject(post);
            Object o = jsonObject.get("data");
            if(o != null){
                String jsonString = JSONObject.toJSONString(o);
                JSONObject datas = JSONObject.parseObject(jsonString);
                Object o1 = datas.get("datas");
                if(o1 != null){
                    String jsonString1 = JSONObject.toJSONString(o1);
                    List<Map> maps = JSONObject.parseArray(jsonString1, Map.class);
                    if(CollectionUtils.isNotEmpty(maps)){
                        Map map = maps.get(0);
                        Object o2 = map.get("user");
                        String jsonString2 = JSONObject.toJSONString(o2);
                        JSONObject jsonObject1 = JSONObject.parseObject(jsonString2);
                        String string = jsonObject1.get("id").toString();
                        return string;
                    }
                }
            }
        }
        return null;
    }

    @Override
    public void changeCrmCustomerMain(ChangeCrmCustomerMainDto requestData) {
        // 变更档案信息
        List<CrmCustomerRelation> list = crmCustomerRelationMapper.selectByCrmCustomerMainId(requestData.getOldCrmCustomerMainId());
        if(CollectionUtils.isEmpty(list)){
            return ;
        }
        //变更信息  并且之前的信息  置为不可用
        crmCustomerRelationMapper.updateByCrmCustomerMainId(requestData.getOldCrmCustomerMainId(),requestData.getNewCrmCustomerMainId());
        // 置为不可用
        CrmCustomerMain crmCustomerMain = new CrmCustomerMain();
        crmCustomerMain.setStatus(-1);
        crmCustomerMain.setUpdateTime(new Date());
        crmCustomerMain.setId(requestData.getOldCrmCustomerMainId());
        crmCustomerMainMapper.updateById(crmCustomerMain);
    }

    @Override
    public void changeCrmRelation(ChangeCrmRelationDto requestData) {
        List<CrmCustomerRelation> list = crmCustomerRelationMapper.selectByCrmCustomerMainId(requestData.getOldCrmCustomerMainId());
        if(CollectionUtils.isEmpty(list)){
            return ;
        }
        List<CrmCustomerRelation> collect = list.stream().filter(r -> r.getCustomerCode().equals(requestData.getCustomerCode())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(collect)){
            return ;
        }
        CrmCustomerRelation crmCustomerRelation = collect.get(0);
        crmCustomerRelation.setCrmCustomerMainId(requestData.getNewCrmCustomerMainId());
        crmCustomerRelation.setUpdateTime(new Date());
        crmCustomerRelationMapper.updateById(crmCustomerRelation);
    }

    @Override
    public List<CrmRelationDto> getCrmRelation(String crmCustomerMainId) {
        List<CrmCustomerRelation> list = crmCustomerRelationMapper.selectByCrmCustomerMainId(crmCustomerMainId);
        String jsonString = JSONObject.toJSONString(list);
        return JSONObject.parseArray(jsonString,CrmRelationDto.class);
    }

    @Override
    public List<CrmCreateDto> getCanBindingCustomerMain() {
        List<CrmCustomerMain> list = crmCustomerMainMapper.selectCanBindingCustomerMain();
        String jsonString = JSONObject.toJSONString(list);
        return JSONObject.parseArray(jsonString,CrmCreateDto.class);
    }


    @Override
    public ListCountStatusDto listCountStatus(ListCountReq requestData, String component, String userId) {
        ListCountStatusDto listCountStatusDto = new ListCountStatusDto();

        requestData.setStatus(CrmStatus.INTERVIEW_STATUS);
        Integer i = crmCustomerMainMapper.selectCountByParams(requestData);
        listCountStatusDto.setInterviewStatusNum(i);

        requestData.setStatus(CrmStatus.INVESTIGATE_STATUS);
        Integer invest = crmCustomerMainMapper.selectCountByParams(requestData);
        listCountStatusDto.setInvestigateStatusNum(invest);


        requestData.setStatus(CrmStatus.ACCESS_STATUS);
        Integer access = crmCustomerMainMapper.selectCountByParams(requestData);
        listCountStatusDto.setAccessStatusNum(access);

        requestData.setStatus(CrmStatus.OUT_STATUS);
        Integer out = crmCustomerMainMapper.selectCountByParams(requestData);
        listCountStatusDto.setOutStatusNum(out);


        // 数据权限控制   到人
        List<String> arraySplit = new ArrayList<>();
        // 判断数据
        List<SysPermissionDataRuleModel> sysPermissionDataRuleModels = sysBaseAPI.queryPermissionDataRule("", component, userId);
        for (SysPermissionDataRuleModel sysPermissionDataRuleModel : sysPermissionDataRuleModels) {
            boolean equals = sysPermissionDataRuleModel.getRuleColumn().equals("sys_dealing");
            if(equals){
                String ruleValue = sysPermissionDataRuleModel.getRuleValue();
                arraySplit.addAll(Arrays.asList(ruleValue.split(",")));
            }
        }
        // 获取id
        if(CollectionUtils.isNotEmpty(arraySplit)){
            // 仅查询这些门店的
            requestData.setCustomerIds(arraySplit);
            requestData.setInterviewBrandIds(null);
            requestData.setStatus(CrmStatus.COOPREATE_STATUS);
            Integer coop = crmCustomerMainMapper.selectCountByParams(requestData);
            listCountStatusDto.setCoopreateStatusNum(coop);

            requestData.setInterviewBrandIds(null);
            requestData.setStatus(CrmStatus.TERMINATE_STATUS);
            Integer terminate = crmCustomerMainMapper.selectCountByParams(requestData);
            listCountStatusDto.setTerminateStatusNum(terminate);
        }else{
            listCountStatusDto.setCoopreateStatusNum(0);
            listCountStatusDto.setTerminateStatusNum(0);
        }



        return listCountStatusDto;
    }

    @Override
    public List<CArcbrand> getBrandInfo() {
        QueryWrapper<CArcbrand> queryWrapper = new QueryWrapper<>();
        CArcbrand cArcbrand = new CArcbrand();
        cArcbrand.setIsactive("Y");
        queryWrapper.setEntity(cArcbrand);
        queryWrapper.isNotNull("BRANDNAME");
        List<CArcbrand> cArcbrands = cArcbrandMapper.selectList(queryWrapper);
        return cArcbrands;
    }

    @Override
    public List<CAreaVo1> getArea1() {
        QueryWrapper<CAreaVo1> queryWrapper = new QueryWrapper<>();
        CAreaVo1 cAreaVo = new CAreaVo1();
        cAreaVo.setIsactive("Y");
        queryWrapper.setEntity(cAreaVo);
        List<CAreaVo1> cAreaVos = cArea1Mapper.selectList(queryWrapper);
        return cAreaVos;
    }

    @Override
    public List<StoreCustomerResp> getStoreList(StoreCustomerReq requestData, Page page) {
        com.github.pagehelper.Page<StoreCustomerResp> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        cStoreMapper.selectByParams(requestData);
        PageInfo<StoreCustomerResp> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        List<StoreCustomerResp> list = pageInfo.getList();
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<CrmCreateDto> list(CrmCustomerListReq requestData, Page page, String component, String userId) {
        // 数据权限控制   到人
        if(requestData.getStatus().equals(3) || requestData.getStatus().equals(4)){
            requestData.setInterviewBrandIds(null);
            List<String> arraySplit = new ArrayList<>();
            // 判断数据
            List<SysPermissionDataRuleModel> sysPermissionDataRuleModels = sysBaseAPI.queryPermissionDataRule("", component, userId);
            for (SysPermissionDataRuleModel sysPermissionDataRuleModel : sysPermissionDataRuleModels) {
                boolean equals = sysPermissionDataRuleModel.getRuleColumn().equals("sys_dealing");
                if(equals){
                    String ruleValue = sysPermissionDataRuleModel.getRuleValue();
                    arraySplit.addAll(Arrays.asList(ruleValue.split(",")));
                }
            }
            // 获取id
            if(CollectionUtils.isNotEmpty(arraySplit)){
                // 仅查询这些门店的
                requestData.setCustomerIds(arraySplit);
            }else{
                return new ArrayList<>();
            }
        }

        // 查询数据
        com.github.pagehelper.Page<String> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        crmCustomerMainMapper.selectByParams(requestData);
        PageInfo<String> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        List<String> list = pageInfo.getList();
        if(CollectionUtils.isEmpty(list)){
            return new ArrayList<>();
        }
        // 获取主数据  获取标签数据  获取品牌数据
        QueryWrapper<CrmCustomerMain> crmCustomerMainQueryWrapper = new QueryWrapper<>();
        crmCustomerMainQueryWrapper.in("ID",list);
        crmCustomerMainQueryWrapper.eq("is_del",0);
        crmCustomerMainQueryWrapper.orderByDesc("update_time");
        List<CrmCustomerMain> crmCustomerMains = crmCustomerMainMapper.selectList(crmCustomerMainQueryWrapper);
        String jsonString = JSONObject.toJSONString(crmCustomerMains);
        List<CrmCreateDto> crmCreateDtos = JSONObject.parseArray(jsonString, CrmCreateDto.class);

        // 获取品牌数据
        Map<String,List<CrmRelationDto>> map = new HashMap<>();
        List<CrmCustomerRelation> crmCustomerRelations = crmCustomerRelationMapper.selectByCrmCustomerMainIds(list);
        if(CollectionUtils.isNotEmpty(crmCustomerRelations)){
            String jsonString2 = JSONObject.toJSONString(crmCustomerRelations);
            List<CrmRelationDto> crmRelationDtos = JSONObject.parseArray(jsonString2, CrmRelationDto.class);
            map = crmRelationDtos.stream().collect(Collectors.groupingBy(r -> r.getCrmCustomerMainId()));
        }
        // 获取标签数据
        Map<String,List<CrmCustomerLabelDto>> map2 = new HashMap<>();
        List<CrmCustomerLabel> crmCustomerLabels = crmCustomerLabelMapper.selectByCrmCustomerMainIds(list);
        if(CollectionUtils.isNotEmpty(crmCustomerLabels)){
            String jsonString3 = JSONObject.toJSONString(crmCustomerLabels);
            List<CrmCustomerLabelDto> crmCustomerLabelDtos = JSONObject.parseArray(jsonString3, CrmCustomerLabelDto.class);
            map2 = crmCustomerLabelDtos.stream().collect(Collectors.groupingBy(r->r.getCrmCustomerMainId()));
        }


        for (CrmCreateDto crmCreateDto : crmCreateDtos) {
            if(StringUtils.isNotBlank(crmCreateDto.getFirstAreaManager())){
                // 通过hrmresource进行查询根据id
                Hrmresource hrmresource = hrmresourceMapper.selectByPrimarykey(crmCreateDto.getFirstAreaManager());
                if(hrmresource != null){
                    crmCreateDto.setFirstAreaManagerName(hrmresource.getLastname());
                }
//                String s = hrEmployeeMapper.selectById(crmCreateDto.getFirstAreaManager());
            }

            // 设置下属经销商
            crmCreateDto.setCrmRelationDtoList(map.get(crmCreateDto.getId()));
            // 设置当前客户标签
            crmCreateDto.setCrmCustomerLabelDtos(map2.get(crmCreateDto.getId()));
        }
        if(CollectionUtils.isNotEmpty(crmCustomerRelations)){
            List<String> collect = crmCustomerRelations.stream().map(r -> r.getCustomerCode()).collect(Collectors.toList());
            List<CustomerDto> customerDtos = cCustomerMapper.selectByCodes(collect);
            if(CollectionUtils.isNotEmpty(customerDtos)){
                List<String> collectx = customerDtos.stream().filter(r->r.getId() != null).map(r -> r.getId()+"").collect(Collectors.toList());
                //Custyomer
                Map<Long, List<CustomerDto>> groupById = customerDtos.stream().collect(Collectors.groupingBy(r -> r.getId()));

                List<FormattableMain48Dto> formattableMain48Dtos = formtableMain48Mapper.selectByBjjxsmcs(collectx);
                //分组
                Map<String, List<FormattableMain48Dto>> collect1 = formattableMain48Dtos.stream().collect(Collectors.groupingBy(r -> r.getBjjxsmc() +""));

                for (CrmCreateDto crmCreateDto : crmCreateDtos) {
                    // 获取首个合作品牌
                    // 查询大区经理名称
                    List<CrmRelationDto> crmRelationDtoList = crmCreateDto.getCrmRelationDtoList();
                    if(CollectionUtils.isNotEmpty(crmRelationDtoList)){
                        for (CrmRelationDto crmRelationDto : crmRelationDtoList) {
                            //  经销区域信息
                            List<CustomerDto> customerDtos1 = groupById.get(Long.parseLong(crmRelationDto.getCustomerId()));
                            if(CollectionUtils.isNotEmpty(customerDtos1)){
                                String clopStore = customerDtos1.get(0).getClopStore();
                                crmCreateDto.setSellArea(clopStore);
                            }

                            List<FormattableMain48Dto> formattableMain48Dtos1 = collect1.get(crmRelationDto.getCustomerId());
                            // 看看是否有
                            if(CollectionUtils.isNotEmpty(formattableMain48Dtos1)){
                                crmRelationDto.setJysj2(formattableMain48Dtos1.get(0).getJysj2());
                            }
                        }
                    }
                }
            }
        }


        // 过滤数据
        for (CrmCreateDto crmCreateDto : crmCreateDtos) {
            Set<String> brands = new HashSet<>();
            List<CrmRelationDto> crmRelationDtoList = crmCreateDto.getCrmRelationDtoList();
            if(CollectionUtils.isNotEmpty(crmRelationDtoList)){
                Map<String, List<CrmRelationDto>> collectCrmCustomerDto = crmRelationDtoList.stream()
                        .filter(r->StringUtils.isNotBlank(r.getcArcbrandId())).collect(Collectors.groupingBy(r -> r.getcArcbrandId()));
                for (String s : collectCrmCustomerDto.keySet()) {
                    List<CrmRelationDto> crmRelationDtos = collectCrmCustomerDto.get(s);
                    if(CollectionUtils.isNotEmpty(crmRelationDtos)){
                        List<CrmRelationDto> collect1 = crmRelationDtos.stream().filter(r -> StringUtils.isNotBlank(r.getJysj2())).collect(Collectors.toList());
                        if(collect1.size() != crmRelationDtos.size()){
                            // 不相等  则是没解约的
                            brands.add(s);
                        }
                    }
                }
            }
            crmCreateDto.setArcBrandIds(brands);
        }

        return crmCreateDtos;
    }

    @Override
    public List<FindCustomerListDto> findCustomerByCodes(List<String> requestData,String path,String username) {
        List<String> requestData2 = new ArrayList<>();

        List<String> arraySplit = new ArrayList<>();
        // 判断数据
        List<SysPermissionDataRuleModel> sysPermissionDataRuleModels = sysBaseAPI.queryPermissionDataRule("", path, username);
        for (SysPermissionDataRuleModel sysPermissionDataRuleModel : sysPermissionDataRuleModels) {
            boolean equals = sysPermissionDataRuleModel.getRuleColumn().equals("sys_dealing");
            if(equals){
                String ruleValue = sysPermissionDataRuleModel.getRuleValue();
                arraySplit.addAll(Arrays.asList(ruleValue.split(",")));
            }
        }

        // 获取id

        if(CollectionUtils.isNotEmpty(arraySplit)){
            List<CustomerDto> customerDtos = cCustomerMapper.selectByCodes(requestData);
            for (CustomerDto customerDto : customerDtos) {
                for (String code : arraySplit) {
                    if(customerDto.getId().toString().equals(code)){
                        requestData2.add(customerDto.getCode());
                    }
                }
            }
        }

        requestData = requestData2;



        List<FindCustomerListDto> responseList = new ArrayList<>();
        // 根据编码查询数据
        if(CollectionUtils.isEmpty(requestData)){
            return responseList;
        }

        List<CustomerDto> customerDtos = cCustomerMapper.selectByCodes(requestData);
        if(CollectionUtils.isNotEmpty(customerDtos)){

            List<String> collectx = customerDtos.stream().filter(r->r.getId() != null).map(r -> r.getId()+"").collect(Collectors.toList());
            List<Long> collect = customerDtos.stream().filter(r->r.getId() != null).map(r -> r.getId()).collect(Collectors.toList());
            List<FormattableMain48Dto> formattableMain48Dtos = formtableMain48Mapper.selectByBjjxsmcs(collectx);
            Map<Long, List<FormattableMain48Dto>> collect1 = formattableMain48Dtos.stream().collect(Collectors.groupingBy(r -> r.getBjjxsmc()));


            // 获取店铺情况  统计经销商下店铺情况
            List<CstoreSumDto> cstoreSumDtos = cStoreMapper.selectCountStoreByCustomerIds(collect);
            Map<Long, List<CstoreSumDto>> collect2 = cstoreSumDtos.stream().collect(Collectors.groupingBy(r -> r.getCustomerId()));

            for (CustomerDto customerDto : customerDtos) {
                // 查询 基础信息
                QueryWrapper<CrmCustomerRelation> queryWrapper2 = new QueryWrapper<>();
                queryWrapper2.eq("CUSTOMER_CODE",customerDto.getCode());
                queryWrapper2.eq("IS_DEL",0);
                List<CrmCustomerRelation> crmCustomerRelations = crmCustomerRelationMapper.selectList(queryWrapper2);
                if(CollectionUtils.isNotEmpty(crmCustomerRelations)){
                    CrmCustomerRelation crmCustomerRelation = crmCustomerRelations.get(0);
                    // 设置信息
                    customerDto.setCustomerLevel(crmCustomerRelation.getCustomerLevel());
                    customerDto.setCrmRelationId(crmCustomerRelation.getId());
                    customerDto.setCustomerEvaluate(crmCustomerRelation.getCustomerEvaluate());
                    // 去除
//                    customerDto.setLegalPersonName(crmCustomerRelation.getLegalPerson());
//                    customerDto.setLegalPersonPhone(crmCustomerRelation.getLegalPersonPhone());
                }

                List<String> customerIds = new ArrayList<>();
                Long id = customerDto.getId();
                customerIds.add(id.toString());
                // 查询最新的一条合同
                List<FormattableMain42Dto> formattableMain42Dtos = formtableMain42Mapper.selectByCustomerIds(customerIds);
                if(CollectionUtils.isNotEmpty(formattableMain42Dtos)){
                    FormattableMain42Dto formattableMain42Dto = formattableMain42Dtos.get(0);
                    customerDto.setConnectName(formattableMain42Dto.getDflxr());
                    customerDto.setConnectPhone(formattableMain42Dto.getDflxrdh());
                    //  公司名称 和 公司地址信息
                    if(StringUtils.isNotBlank(formattableMain42Dto.getHzdwmode())){
                        // 查询数据
                        String s = formtableMain42Mapper.selectUfCustomerNameById(formattableMain42Dto.getHzdwmode());
                        customerDto.setCompanyName(s);
                    }
                    customerDto.setCompanyAddress(formattableMain42Dto.getDfdz());
                }


                // 取伯俊的大区经理信息
                if(customerDto.getAreamngId() != null){
                    String name = hrEmployeeMapper.selectById(customerDto.getAreamngId() + "");
                    customerDto.setAreamngName(name);
                }


                // 设置销售区域  和  大区经理信息 按照经销商id去查询
                List<FlowDataDTO> flowDataDTOS = formTableMain853Mapper.selectByBjjxsid(customerDto.getId());
                if(CollectionUtils.isNotEmpty(flowDataDTOS)){
//                    if(StringUtils.isNotBlank(flowDataDTOS.get(0).getNewAreaId())){
//                        // 销售区域id
//                        customerDto.setcAreaId(Long.parseLong(flowDataDTOS.get(0).getNewAreaId()));
//                    }
//                    if(StringUtils.isNotBlank(flowDataDTOS.get(0).getDqjl())){
//                        Hrmresource hrmresource = hrmresourceMapper.selectByPrimarykey(flowDataDTOS.get(0).getDqjl());
//                        //大区经理名称
//                        if(hrmresource != null){
//                            customerDto.setAreamngName(hrmresource.getLastname());
//                        }
//                    }

                    // 获取经销省市区详细地址
                    Long cProvinceId = flowDataDTOS.get(0).getCProvinceId();
                    Long cCityId = flowDataDTOS.get(0).getCCityId();
                    Long cDistrictId = flowDataDTOS.get(0).getCDistrictId();
                    String zrztxxdz = flowDataDTOS.get(0).getZrztxxdz();
                    // 赋值
                    if(cProvinceId != null){
                        String s = formTableMain853Mapper.selectNameByProviceId(cProvinceId);
                        customerDto.setProvince(s);
                    }
                    if(cCityId != null){
                        String s = formTableMain853Mapper.selectNameByCityId(cCityId);
                        customerDto.setCity(s);
                    }
                    if(cDistrictId != null){
                        String s = formTableMain853Mapper.selectNameByReigonID(cDistrictId);
                        customerDto.setRegion(s);
                    }
                    customerDto.setAddressInfo(zrztxxdz);
                    customerDto.setIsRegCompany(flowDataDTOS.get(0).getSfwgs()+"");
                }

                // 获取是否转直营
                List<FormattableMain48Dto> formattableMain48Dtos2 = formtableMain48Mapper.selectByBjjxsId(customerDto.getId()+"");
                if(CollectionUtils.isNotEmpty(formattableMain48Dtos2)){
                    // 设置是否转直营
                    customerDto.setSfzzy(formattableMain48Dtos2.get(0).getSfzzy());
                    customerDto.setJyyy(formattableMain48Dtos2.get(0).getJyyy());
                }

                // 获取订货信息
                QueryWrapper<CrmOrderGoods> crmOrderGoodsQueryWrapper = new QueryWrapper<>();
                crmOrderGoodsQueryWrapper.eq("CUSTOMER_ID",customerDto.getId());
                crmOrderGoodsQueryWrapper.orderByDesc("SEASON");
                List<CrmOrderGoods> crmOrderGoods = crmOrderGoodsMapper.selectList(crmOrderGoodsQueryWrapper);
                // 转一下信息 数量和金额 转成千分位
                for (CrmOrderGoods crmOrderGood : crmOrderGoods) {
                    String orderGoodsAmt = crmOrderGood.getOrderGoodsAmt();
                    String orderGoodsNum = crmOrderGood.getOrderGoodsNum();
                    //转成千分位
                    NumberFormat numberFormat = NumberFormat.getNumberInstance(Locale.SIMPLIFIED_CHINESE); // 或者使用其他Locale，例如Locale.GERMANY等
                    numberFormat.setGroupingUsed(true); // 启用千分位分隔符

                    if(StringUtils.isNotBlank(orderGoodsAmt)){
                        boolean matches = orderGoodsAmt.matches("\\d+");
                        if(matches){
                            BigDecimal number = new BigDecimal(orderGoodsAmt);
                            String formattedNumber = numberFormat.format(number);
                            crmOrderGood.setOrderGoodsAmt(formattedNumber);
                        }

                        String pattern = "^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$";
                        if(orderGoodsAmt.matches(pattern)){
                            BigDecimal number = new BigDecimal(orderGoodsAmt);
                            String formattedNumber = numberFormat.format(number);
                            crmOrderGood.setOrderGoodsAmt(formattedNumber);
                        }
                    }

                    if(StringUtils.isNotBlank(orderGoodsNum)){
                        boolean matches = orderGoodsNum.matches("\\d+");
                        if(matches){
                            BigDecimal number = new BigDecimal(orderGoodsNum);
                            String formattedNumber = numberFormat.format(number);
                            crmOrderGood.setOrderGoodsNum(formattedNumber);
                        }
                    }
                }


                customerDto.setCrmOrderGoodsList(crmOrderGoods);

                List<FormattableMain48Dto> formattableMain48Dtos1 = collect1.get(customerDto.getId());
                if(CollectionUtils.isNotEmpty(formattableMain48Dtos1)){
                    customerDto.setJysj2(formattableMain48Dtos1.get(0).getJysj2());
                }
                List<CstoreSumDto> cstoreSumDtos1 = collect2.get(customerDto.getId());
                if(CollectionUtils.isNotEmpty(cstoreSumDtos1)){
                    customerDto.setHaveStoreNum(cstoreSumDtos1.get(0).getSumNum());
                }
            }
        }

        Map<Long, List<CustomerDto>> collect = customerDtos.stream().collect(Collectors.groupingBy(r -> r.getcArcbrandId()));
        // 调整顺序  JNBY-速写-LESS-童装-蓬马--HOME  2-3-5-4-12-17
        List<Long> arrayList = new ArrayList<>();
        arrayList.add(2L);
        arrayList.add(3L);
        arrayList.add(5L);
        arrayList.add(4L);
        arrayList.add(12L);
        arrayList.add(17L);

        for (Long arcBrandId : arrayList) {
            List<CustomerDto> customerDtos1 = collect.get(arcBrandId);
            if(CollectionUtils.isNotEmpty(customerDtos1)){
                FindCustomerListDto findCustomerListDto = new FindCustomerListDto();
                findCustomerListDto.setArcBrandId(arcBrandId.toString());
                findCustomerListDto.setList(collect.get(arcBrandId));
                responseList.add(findCustomerListDto);
            }
        }
//        for (Long arcBrandId : collect.keySet()) {
//            FindCustomerListDto findCustomerListDto = new FindCustomerListDto();
//            findCustomerListDto.setArcBrandId(arcBrandId.toString());
//            findCustomerListDto.setList(collect.get(arcBrandId));
//            responseList.add(findCustomerListDto);
//        }

        return responseList;
    }

    @Override
    public List<CAreaVo> getArea() {
        QueryWrapper<CAreaVo> queryWrapper = new QueryWrapper<>();
        CAreaVo cAreaVo = new CAreaVo();
        cAreaVo.setIsactive("Y");
        queryWrapper.setEntity(cAreaVo);
        queryWrapper.ne("pra_name","道具");
        List<CAreaVo> cAreaVos = cAreaMapper.selectList(queryWrapper);
        return cAreaVos;
    }

    @Override
    public List<FormattableMain42Dto> findHtByCustomerId(List<String> customerIds) {
        List<FormattableMain42Dto> formattableMain42resp = new ArrayList<>();
        Set<String> set = new HashSet<>();
        if(CollectionUtils.isEmpty(customerIds)){
            return new ArrayList<>();
        }
        // 查询最新的一条合同
        List<FormattableMain42Dto> formattableMain42Dtos = formtableMain42Mapper.selectByCustomerIds(customerIds);
        if(CollectionUtils.isNotEmpty(formattableMain42Dtos)){
            // 取每一条第一个 取所有
            for (FormattableMain42Dto formattableMain42Dto : formattableMain42Dtos) {
//                if(set.contains(formattableMain42Dto.getJsxbjjd())){
//                    continue;
//                }
                if(StringUtils.isNotBlank(formattableMain42Dto.getHtkrrq())){
                    //
                    String yyyyMMdd = formattableMain42Dto.getHtkrrq().replaceAll("-", "");
                    String substring = yyyyMMdd.substring(4, 6);
                    if(Integer.parseInt(substring) >= 7){
                        formattableMain42Dto.setFy("FY"+(Integer.parseInt(yyyyMMdd.substring(2,4)) + 1));
                    }else{
                        formattableMain42Dto.setFy("FY"+(Integer.parseInt(yyyyMMdd.substring(2,4))));
                    }
                }
                //转成千分位
                NumberFormat numberFormat = NumberFormat.getNumberInstance(Locale.SIMPLIFIED_CHINESE); // 或者使用其他Locale，例如Locale.GERMANY等
                numberFormat.setGroupingUsed(true); // 启用千分位分隔符

                if(StringUtils.isNotBlank(formattableMain42Dto.getLybzj())){
                    // 判断是否为数字
                    boolean matches = formattableMain42Dto.getLybzj().matches("\\d+");
                    if(matches){
                        BigDecimal number = new BigDecimal(formattableMain42Dto.getLybzj());
                        String formattedNumber = numberFormat.format(number);
                        formattableMain42Dto.setLybzj(formattedNumber);
                    }
                }

                if(StringUtils.isNotBlank(formattableMain42Dto.getHtqjhzb())){
                    boolean matches = formattableMain42Dto.getHtqjhzb().matches("\\d+");
                    if(matches){
                        BigDecimal number = new BigDecimal(formattableMain42Dto.getHtqjhzb());
                        String formattedNumber = numberFormat.format(number);
                        formattableMain42Dto.setHtqjhzb(formattedNumber);
                    }

                }

                if(StringUtils.isNotBlank(formattableMain42Dto.getXtsyf())){
                    boolean matches = formattableMain42Dto.getXtsyf().matches("\\d+");
                    if(matches){
                        BigDecimal number = new BigDecimal(formattableMain42Dto.getXtsyf());
                        String formattedNumber = numberFormat.format(number);
                        formattableMain42Dto.setXtsyf(formattedNumber);
                    }
                }

                formattableMain42resp.add(formattableMain42Dto);
                set.add(formattableMain42Dto.getJsxbjjd());
            }
            return formattableMain42resp;
        }
        return null;
    }

    @Override
    public List<FormattableMain42Dto> findConsSalesByCustomerIds(List<String> customerIds) {
        if(CollectionUtils.isEmpty(customerIds)){
            return new ArrayList<>();
        }
        List<FormattableMain42Dto> formattableMain42resp = new ArrayList<>();
        Set<String> set = new HashSet<>();
        // 查询最新的一条代销合同
        List<FormattableMain42Dto> formattableMain42Dtos = formtableMain42Mapper.selectByConsSalesCustomerIds(customerIds);
        if(CollectionUtils.isNotEmpty(formattableMain42Dtos)){
            // 查询最新的一条代销合同
            for (FormattableMain42Dto formattableMain42Dto : formattableMain42Dtos) {
                if(set.contains(formattableMain42Dto.getJsxbjjd())){
                    continue;
                }
                //转成千分位
                NumberFormat numberFormat = NumberFormat.getNumberInstance(Locale.SIMPLIFIED_CHINESE); // 或者使用其他Locale，例如Locale.GERMANY等
                numberFormat.setGroupingUsed(true); // 启用千分位分隔符

                if(StringUtils.isNotBlank(formattableMain42Dto.getLybzj())){

                    boolean matches = formattableMain42Dto.getLybzj().matches("\\d+");
                    if(matches){
                        BigDecimal number = new BigDecimal(formattableMain42Dto.getLybzj());
                        String formattedNumber = numberFormat.format(number);
                        formattableMain42Dto.setLybzj(formattedNumber);
                    }
                }

                if(StringUtils.isNotBlank(formattableMain42Dto.getHtqjhzb())){
                    boolean matches = formattableMain42Dto.getHtqjhzb().matches("\\d+");
                    if(matches){
                        BigDecimal number = new BigDecimal(formattableMain42Dto.getHtqjhzb());
                        String formattedNumber = numberFormat.format(number);
                        formattableMain42Dto.setHtqjhzb(formattedNumber);
                    }


                }

                if(StringUtils.isNotBlank(formattableMain42Dto.getXtsyf())){
                    boolean matches = formattableMain42Dto.getXtsyf().matches("\\d+");
                    if(matches){
                        BigDecimal number = new BigDecimal(formattableMain42Dto.getXtsyf());
                        String formattedNumber = numberFormat.format(number);
                        formattableMain42Dto.setXtsyf(formattedNumber);
                    }

                }

                formattableMain42resp.add(formattableMain42Dto);
                set.add(formattableMain42Dto.getJsxbjjd());
            }
            return formattableMain42resp;
        }
        return null;
    }

    @Override
    public void importOldData(String url) {
        // 导入旧的数据
        List<Map<String,String>> importData  = new ArrayList<>();
        //返回数据
        String filePath = FileParseUtil.downLoadExcel(url);
        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {

            @Override
            protected void saveData() {
                List<Map<Integer, String>> cachedDataList1 = this.cachedDataList;
                for (Map<Integer, String> integerStringMap : cachedDataList1) {
                    Map<String,String> result = new HashMap<>();
                    result.put("brandName",integerStringMap.get(0));          //  品牌名称
                    result.put("companyName",integerStringMap.get(1));  //  公司名称  主经销商
                    result.put("customers",integerStringMap.get(2));        //多个经销商   格式  [(5KA046)太原(JNBY),(9KB053)运城(速写),(2KA053)运城(LESS)]
                    result.put("province",integerStringMap.get(3));     // 省
                    result.put("city",integerStringMap.get(4));           // 市
                    result.put("district",integerStringMap.get(5)); // 区    区有可能为空 是直接代理到市的
                    result.put("crmId",integerStringMap.get(6));  // crmId 缺了年份
                    result.put("coopYear",integerStringMap.get(7));  //开始合作时间
                    result.put("mainCustomer",integerStringMap.get(8));  //主经销商编码
                    importData.add(result);
                }
            }
        });
        // 封装并且插入数据
        buildAndInsertData(importData);
        try {
            Files.deleteIfExists(Paths.get(filePath));
        }catch (Exception e){
            log.error("e= ",e);
        }
    }

    private void buildAndInsertData(List<Map<String, String>> importData) {
        Map<String,Integer> soutMap = new HashMap<>();
        soutMap.put("JNBY",1);
        soutMap.put("CROQUIS",2);
        soutMap.put("速写",3);
        soutMap.put("LESS",4);
        soutMap.put("jnby by JNBY",5);
        soutMap.put("Pomme de terre",6);
        soutMap.put("JNBYHOME",7);
        soutMap.put(null,8);
        soutMap.put("",9);


        Map<String, List<Map<String, String>>> crmId1 = importData.stream().collect(Collectors.groupingBy(r -> r.get("crmId")));
        for (String crmFinalId : crmId1.keySet()) {
            List<Map<String, String>> maps = crmId1.get(crmFinalId);
            // 排序 按照 1 JNBY  2 CROQUIS  3 速写   4 LESS  5 jnby by JNBY   6 Pomme de terre   7   JNBYHOME
            List<Map<String, String>> collect = maps.stream().sorted(new Comparator<Map<String, String>>() {
                @Override
                public int compare(Map<String, String> o1, Map<String, String> o2) {
                    return soutMap.get(o1.get("brandName").toString()) - soutMap.get(o2.get("brandName").toString());
                }
            }).collect(Collectors.toList());
            // 封装数据
            for (Map<String, String> importDatum : collect) {
                //开始处理数据
                log.info("开始处理数据  = {}",JSONObject.toJSONString(importDatum));
                // 获取数据
                String brandName = importDatum.get("brandName");//  品牌名称
                String companyName = importDatum.get("companyName");//  公司名称  主经销商
                String customers = importDatum.get("customers");//多个经销商   格式  [(5KA046)太原(JNBY),(9KB053)运城(速写),(2KA053)运城(LESS)]
                String province = importDatum.get("province");// 省
                String city = importDatum.get("city");// 市
                String district = importDatum.get("district");// 区    区有可能为空 是直接代理到市的
                String crmId = importDatum.get("crmId");// crmId 缺了年份
                String coopYear = importDatum.get("coopYear");//开始合作时间
                String mainCustomer = importDatum.get("mainCustomer");//主经销商编码
                // 设置数据
                CrmCustomerMain insertData = new CrmCustomerMain();
                String id = IdLeaf.getId(IdConstant.CRM_CUSTOMER_MAIN);
                insertData.setId(id);
                // 设置crmId
                if(StringUtils.isBlank(coopYear)){
                    coopYear = DateUtils.format(new Date(),"yyyy/MM/dd");
                }
                String finalId = crmId + coopYear.substring(0, coopYear.indexOf("/"));
                insertData.setCrmId(finalId);

                insertData.setCompanyFlag("0");
                insertData.setCustomerName(companyName);
                insertData.setErpSystem("系统导入-ERP系统");
                insertData.setCreateTime(new Date());
                insertData.setUpdateTime(new Date());
                insertData.setStatus(CrmStatus.COOPREATE_STATUS);
                insertData.setIsDel(IsDeleteEnum.NORMAL.getCode());

                if(StringUtils.isNotBlank(province)){
                    insertData.setCompanyProvince(province);
                }
                if(StringUtils.isNotBlank(city)){
                    insertData.setCompanyCity(city);
                }
                if(StringUtils.isNotBlank(district)){
                    insertData.setCompanyDistrict(district);
                }
                // 查询主经销商信息 (5KA046)太原(JNBY)   5KA046  主Code
//                String customerMainCode = companyName.substring(1, 7);
//                // 根据code查询
                List<String> codes = new ArrayList<>();
                codes.add(mainCustomer);
                List<CustomerDto> customerDtos = cCustomerMapper.selectByCodesAll(codes);
                // 查询出来主的
                if(CollectionUtils.isNotEmpty(customerDtos)){
                    CustomerDto customerDto = customerDtos.get(0);
                    insertData.setConnectEmail(customerDto.getEmail());
                    insertData.setConnectName(customerDto.getContacter());
                    insertData.setConnectPhone(customerDto.getPhone());
                    insertData.setCompanyName(customerDto.getAddress());
                    insertData.setFirstAreaManager(customerDto.getAreamngId() == null ? "":customerDto.getAreamngId().toString());
                    insertData.setSellArea(customerDto.getClopStore() == null ? "" : customerDto.getClopStore());
                    insertData.setFirstFranchiseBrand(customerDto.getcArcbrandId() == null ? "" : customerDto.getcArcbrandId().toString());
                }
                if(StringUtils.isNotBlank(coopYear)){
                    insertData.setFirstFranchiseTime(coopYear);
                }
                // 处理 详情数据  多个经销商    customers  格式  [(5KA046)太原(JNBY),(9KB053)运城(速写),(2KA053)运城(LESS)]
                String cutCustomerData = customers.trim().substring(1, customers.trim().length() - 1);
                String[] split = cutCustomerData.split(",");

                //封装二层数据
                List<CrmCustomerRelation> crmCustomerRelations = new ArrayList<>();
                List<String> allCodes = new ArrayList<>();
                for (String customerData : split) {
                    List<String> cutCodes = new ArrayList<>();
                    CrmCustomerRelation crmCustomerRelation = new CrmCustomerRelation();
                    crmCustomerRelation.setId(IdLeaf.getId(IdConstant.CRM_CUSTOMER_RELATION));
                    String substring = customerData.trim().substring(1, 7);
                    crmCustomerRelation.setCustomerCode(substring);
                    crmCustomerRelation.setCrmCustomerMainId(id);
                    crmCustomerRelation.setCreateTime(new Date());
                    crmCustomerRelation.setUpdateTime(new Date());
                    crmCustomerRelation.setIsDel(IsDeleteEnum.NORMAL.getCode());
                    // 查询数据
                    cutCodes.add(substring);
                    allCodes.add(substring);
                    List<CustomerDto> customerDtoData= cCustomerMapper.selectByCodesAll(cutCodes);
                    if(CollectionUtils.isNotEmpty(customerDtoData)){
                        CustomerDto customerDto = customerDtoData.get(0);
                        crmCustomerRelation.setcArcbrandId(customerDto.getcArcbrandId() == null ? "" : customerDto.getcArcbrandId().toString());
                        crmCustomerRelation.setCustomerId(customerDto.getId()+"");
                    }
                    crmCustomerRelations.add(crmCustomerRelation);
                }

                // 获取解约时间   根据当前时间
//                List<FindCustomerListDto> customerByCodes = findCustomerByCodes(allCodes);
                // 需要排序的list
                List<String> shouldSortList = new ArrayList<>();
                // 获取开始合作时间  yyyy/MM/dd
                String firstFranchiseTime = insertData.getFirstFranchiseTime();
                long l = new Date().getTime() - DateUtil.parseDate(firstFranchiseTime, "yyyy/MM/dd").getTime();
                long days = l / (24 * 60 * 60 * 1000);
                Long year = days / 360;

                if(year < 3){
                    year = null;
                }else if(year >= 3 && year < 5){
                    year = 3L;
                }else{
                    if(year % 5 != 0){
                        long l1 = year / 5;
                        year = l1 * 5;
                    }
                }

                // 增加标签  老客标签可以拿出来的
                if(year != null){
                    CrmCustomerLabel crmCustomerLabel = new CrmCustomerLabel();
                    crmCustomerLabel.setId(insertData.getId() +"-" + IdLeaf.getId(IdConstant.CRM_CUSTOMER_MAIN));
                    crmCustomerLabel.setCrmCustomerMainId(insertData.getId());
                    crmCustomerLabel.setIsDel(IsDeleteEnum.NORMAL.getCode());
                    crmCustomerLabel.setLabelName(year+"年老客");
                    crmCustomerLabelMapper.insert(crmCustomerLabel);
                }


//                if(CollectionUtils.isNotEmpty(customerByCodes)){
//                    List<CustomerDto> finalList = new ArrayList<>();
//                    for (FindCustomerListDto customerByCode : customerByCodes) {
//                        finalList.addAll(customerByCode.getList());
//                    }
//                    // 需要先排序
//                    // 查询加盟时间
//                    for (CustomerDto customerDto : finalList) {
//                        // 20230202
//                        Long enterdate = customerDto.getEnterdate();
//
//                        // 解约时间  2022-02-02
//                        String jysj2 = customerDto.getJysj2();
//                        if(StringUtils.isBlank(jysj2)){
//                            jysj2 = DateUtils.format(new Date(),"yyyyMMdd");
//                        }else{
//                            jysj2 = jysj2.replaceAll("-","");
//                        }
//                        shouldSortList.add(enterdate+"-头");
//                        shouldSortList.add(jysj2 +"-尾");
//                    }
//
//                    // 排序
//                    List<String> sortedList = shouldSortList.stream().sorted(new Comparator<String>() {
//                        @Override
//                        public int compare(String o1, String o2) {
//                            return Integer.parseInt(o1.split("-")[0]) - Integer.parseInt(o2.split("-")[0]);
//                        }
//                    }).collect(Collectors.toList());
//                    // 开始查找  头 碰 头  直接不管    头碰尾  合并   尾碰头  这块空档期   尾碰尾  后尾覆盖前尾
//                    List<String> finalListResult = new ArrayList<>();
//                    int i = 0;
//                    for (String date : sortedList) {
//                        // date 有可能是尾 也有可能是头 第一个肯定是头
//                        if(i == 0){
//                            finalListResult.add(date);
//                        }else{
//                            // 获取最后一位
//                            String lastHave = finalListResult.get(finalListResult.size() - 1);
//                            String lastOrFirst = lastHave.split("-")[1];
//                            String shouldCheckLastOrFisrt = date.split("-")[1];
//                            if(lastOrFirst.equals("头")){
//                                if(lastOrFirst.equals(shouldCheckLastOrFisrt)){
//                                    // 头碰头
//                                    continue;
//                                }else{
//                                    // 头碰尾
//                                    finalListResult.add(date);
//                                }
//                            }else{
//                                if(!lastOrFirst.equals(shouldCheckLastOrFisrt)){
//                                    // 尾碰头
//                                    finalListResult.add(date);
//                                }else{
//                                    // 尾碰尾
//                                    finalListResult.remove(finalListResult.size() - 1);
//                                    finalListResult.add(date);
//                                }
//                            }
//                        }
//                        i++;
//                    }
//
//                    // 获取分段时间
//                    if(CollectionUtils.isNotEmpty(finalListResult)){
//                        // 获取天数
//                        int j = 0;
//                        String firstTime = null;
//                        String endTime = null;
//                        Long day = 0L;
//                        for (String date : finalListResult) {
//                            if(j% 2 == 0){
//                                // 第一位
//                                firstTime = date.split("-")[0];
//                            }else{
//                                // 第二位
//                                endTime = date.split("-")[0];
//                                long l = DateUtil.parseDate(endTime, "yyyyMMdd").getTime() - DateUtil.parseDate(firstTime, "yyyyMMdd").getTime();
//                                long days = l / (24 * 60 * 60 * 1000);
//                                day+= days;
//                            }
//                            j++;
//                        }
//                        log.info("day = {}",day);
//                        // 创建标签
//                        if(day > 0){
//                            long l = day / 360;
//                            if(l > 0){
//                                // 增加标签  老客标签可以拿出来的
//                                CrmCustomerLabel crmCustomerLabel = new CrmCustomerLabel();
//                                crmCustomerLabel.setId(insertData.getId() +"-" + IdLeaf.getId(IdConstant.CRM_CUSTOMER_MAIN));
//                                crmCustomerLabel.setCrmCustomerMainId(insertData.getId());
//                                crmCustomerLabel.setIsDel(IsDeleteEnum.NORMAL.getCode());
//                                crmCustomerLabel.setLabelName(l+"年老客");
//                                crmCustomerLabelMapper.insert(crmCustomerLabel);
//                            }
//                        }
//                    }
//                }


                // 插入数据
                insertData.setBojunCw(1);
                insertData.setBojunOver(1);
                insertData.setNcCw(1);
                insertData.setOaCustomerOver(1);
                crmCustomerMainMapper.insert(insertData);
                if(CollectionUtils.isNotEmpty(crmCustomerRelations)){
                    for (CrmCustomerRelation crmCustomerRelation : crmCustomerRelations) {
                        crmCustomerRelationMapper.insert(crmCustomerRelation);
                    }
                }
                break;
            }
        }


    }

    private String genCrmId(CrmCustomerMain crmCustomerMain) {
        // 查询生成用户编号
        List<CrmGenCode> list = crmGenCodeMapper.selectByProviceCityDistrictName(crmCustomerMain.getCompanyProvince(),crmCustomerMain.getCompanyCity(),crmCustomerMain.getCompanyDistrict());
        if(CollectionUtils.isEmpty(list)){
            throw  new RuntimeException("地址信息不存在，不可生成编号，请检查地址后进行重试");
        }
        CrmGenCode crmGenCode = list.get(0);
        // 更新
        Integer idLeaf = crmGenCode.getIdLeaf();

        CrmGenCode updateCrm = new CrmGenCode();
        updateCrm.setId(crmGenCode.getId());
        updateCrm.setIdLeaf(idLeaf + 1);
        crmGenCodeMapper.updateById(updateCrm);

        String idFinal = "";
        if(Long.valueOf(idLeaf) < 10L){
            idFinal = "00"+ Long.valueOf(idLeaf);
        }else if(Long.valueOf(idLeaf) < 100L){
            idFinal = "0"+Long.valueOf(idLeaf);
        }
        // 今年
        String nowYears = DateUtils.format(new Date(), "yyyy");
        String crmId =crmGenCode.getCode() + idFinal +  nowYears;
        // 去除 -
        crmId = crmId.replaceAll("-", "");
        return crmId;
    }

    private void createAccount(){
        /**
         * 开通门户，分2步
         *
         * 第一步：设置密码
         *
         * java调用方法：
         * com.engine.crm.service.CustomerService#doApply
         * 这个java方法有2个参数。一个是map，一个是request对象。java调用的话，需要自行够建下参数
         *
         * map参数：{customerId: 500028,method: updatePassword,passwordnew: 123456}
         *
         * 前端rest请求：
         * /api/crm/customer/apply?customerId=500028&method=updatePassword&passwordnew=123456
         *
         * 第二步：开通门户，分2步
         * java调用方法：
         * com.engine.crm.service.CustomerService#doApply
         * 这个java方法有2个参数。一个是map，一个是request对象。java调用的话，需要自行够建下参数
         * map参数：{isfromtab: true,customerId: 500028,approvedesc: 导入级别:门户申请,method: portal,PortalStatus: 2}
         * 或
         * /api/crm/customer/apply?isfromtab=true&customerId=500028&approvedesc=%E5%AF%BC%E5%85%A5%E7%BA%A7%E5%88%AB%3A%E9%97%A8%E6%88%B7%E7%94%B3%E8%AF%B7&method=portal&PortalStatus=2
         */
    }



    /**
     * @param crmId
     * @param brand    首次合作品牌
     * @param time     首次合作时间
     * @param manger   首次合作经理
     * @param sellArea 首次合作区域
     * @param
     */
    private void updateCrmInfo(String crmId, String brand, String time,
                               String manger , String sellArea, String code, Long customerId){
        // crmId  根据crmId进行变更数据 变更为合作期  如果已经是合作期 则不变更
        QueryWrapper<CrmCustomerMain> queryWrapper = new QueryWrapper<>();
        CrmCustomerMain crmCustomerMain = new CrmCustomerMain();
        crmCustomerMain.setCrmId(crmId);
        crmCustomerMain.setIsDel(IsDeleteEnum.NORMAL.getCode());
        queryWrapper.setEntity(crmCustomerMain);
        List<CrmCustomerMain> crmCustomerMains = crmCustomerMainMapper.selectList(queryWrapper);
        if(CollectionUtils.isNotEmpty(crmCustomerMains)){
            Integer status = crmCustomerMains.get(0).getStatus();
            if(status.equals(CrmStatus.ACCESS_STATUS)){
                // 更改为合作期
                CrmCustomerMain up = new CrmCustomerMain();
                up.setId(crmCustomerMains.get(0).getId());
                up.setStatus(CrmStatus.COOPREATE_STATUS);
                up.setUpdateTime(new Date());
                up.setNcCw(1);
                up.setBojunCw(1);
                up.setOaCustomerOver(1);
                up.setBojunOver(1);
                // 更新首次合作品牌
                up.setFirstFranchiseBrand(brand);
                up.setFirstFranchiseTime(time);
                up.setFirstAreaManager(manger);
                up.setSellArea(sellArea);
                crmCustomerMainMapper.updateById(up);
            }
            // 插入数据
            if(StringUtils.isNotBlank(code)){
                CrmCustomerRelation crmCustomerRelation = new CrmCustomerRelation();
                crmCustomerRelation.setId(IdLeaf.getId(IdConstant.CRM_CUSTOMER_RELATION));
                crmCustomerRelation.setUpdateTime(new Date());
                crmCustomerRelation.setCreateTime(new Date());
                crmCustomerRelation.setcArcbrandId(brand);
                crmCustomerRelation.setCrmCustomerMainId(crmCustomerMains.get(0).getId());
                crmCustomerRelation.setIsDel(IsDeleteEnum.NORMAL.getCode());
                crmCustomerRelation.setCustomerCode(code);
                crmCustomerRelation.setCustomerId(String.valueOf(customerId));
                crmCustomerRelationMapper.insert(crmCustomerRelation);
            }
        }
    }

    public Long createCustomerBojun(String requestId){
        //  接收 准入完成请求

        final List<DingMsgParam> dingMsgParamList = new ArrayList<>();

        // 获取oa数据
        FlowDataParam flowDataParam = new FlowDataParam(null, Arrays.asList(Long.parseLong(requestId)));
        List<FlowDataDTO> oaBatchDataUseFlowList = formTableMain853Mapper.getOaBatchDataUseFlow(flowDataParam);
        List<FlowDataDTO> customerAndStoreList = new ArrayList<>();

        for (FlowDataDTO flowDataDTO : oaBatchDataUseFlowList) {
            /* 0 仅新建店仓  1 既新建经销商又新建店仓*/
             if ("1".equals(flowDataDTO.getSystemCreateType())) {
                customerAndStoreList.add(flowDataDTO);
            }
        }

        if(CollectionUtils.isNotEmpty(customerAndStoreList)){
            for (FlowDataDTO flowDataDTO : customerAndStoreList) {
                try {
                    if (org.apache.commons.lang3.StringUtils.isBlank(flowDataDTO.getGeneratorCode())) {
                        log.error("=== 经销》既新建经销商又新建店仓，generatorCode不能为空直接退出,flowDataDTO:{} ===", flowDataDTO);
                        continue;
                    }

                    // 品牌（OA获取为汉字）
                    String cArcBrandName = flowDataDTO.getCArcbrandId();
                    if (org.apache.commons.lang3.StringUtils.isBlank(cArcBrandName)) {
                        log.error("=== 该流程没有品牌架构名称，OA流程RequestId：{} 直接退出===", flowDataDTO.getRequestId());
                        continue;
                    }
                    if (OA_BRAND_NAME_BOJUN.get(cArcBrandName.trim()) != null) {
                        cArcBrandName = OA_BRAND_NAME_BOJUN.get(cArcBrandName.trim());
                    }
                    /* 获取品牌架构 */
                    QueryWrapper<CArcbrand> arcBrandWrapper = new QueryWrapper<>();
                    arcBrandWrapper.eq("NAME", cArcBrandName.trim());
                    CArcbrand cArcbrandVo = cArcbrandMapper.selectOne(arcBrandWrapper);
                    if (cArcbrandVo == null) {
                        log.error("===无法获取品牌架构cArcBrandName：{} 直接退出===", cArcBrandName);
                        continue;
                    }
                    Long cArcBrandId = cArcbrandVo.getId();
                    /* 根据品牌获取对应规则集合 */
                    QueryWrapper<BrandRuleContrast> ruleWrapper = new QueryWrapper<>();
                    ruleWrapper.eq("BRAND", cArcBrandName.trim());
                    final List<BrandRuleContrast> ruleContrastList = brandRuleContrastMapper.selectList(ruleWrapper);
                    if (org.springframework.util.CollectionUtils.isEmpty(ruleContrastList)) {
                        log.error("===无法根据品牌BRAND_RULE_CONTRAST数据：{} 直接退出===", cArcBrandName);
                        continue;
                    }
                    final Map<String, BrandRuleContrast> brandRuleMap = new HashMap<>(16);
                    for (BrandRuleContrast brandRuleContrastVo : ruleContrastList) {
                        brandRuleMap.put(brandRuleContrastVo.getType(), brandRuleContrastVo);
                    }

                    CustomerFinanceDingMsgParam customerFinanceDingMsgParam = new CustomerFinanceDingMsgParam();
                    customerFinanceDingMsgParam.setFlowDataDTO(flowDataDTO);

                    log.info("=============== 标题：经销》既新建经销商又新建店仓(共6步) ===========");
                    log.info("=============  第一步：先创建经销商 ======================");
                    /* 第一步：先创建经销商 */
                    Pair<CCustomerVo, CCustomerVo> customerVoPair = createCustomerDoc(flowDataDTO, brandRuleMap, cArcBrandId);
                    // 左边是 经销商id  右边是 道具仓id
                    CCustomerVo customerDoc = customerVoPair.getLeft();
                    CCustomerVo customerProp = customerVoPair.getRight();
                    if (customerDoc == null || customerProp == null) {
                        log.error("=== error：创建经销商/经销商道具失败，直接退出请排查异常 =====");
                        continue;
                    }


                    log.info("=============  第二步：创建店仓总仓 ======================");
                    /* 第二步：创建店仓总仓 */
                    CStoreVo storeAllDoc = createStoreDoc(flowDataDTO, brandRuleMap, customerDoc, cArcBrandId, "1", false, null);
                    if (storeAllDoc == null) {
                        log.error("=== 创建店仓总仓发生异常flowDataDTO：{} ===", flowDataDTO);
                        continue;
                    }


                    // 创建NC 财务信息
                    // 根据经销商名称查询经销商信息
                    NcCustomer docNcCustomer = ncCustomerMapper.selectLatestByCustomerName(flowDataDTO.getJxsssgsqc(), NC_DOC_ACCOUNT_ID);
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(flowDataDTO.getJxsssgsqc())) {
                        // 创建经销商财务档案(货品)
                        customerFinanceDingMsgParam.setDocNcCustomer(docNcCustomer);
                        customerFinanceDingMsgParam.setDocCCustomer(customerDoc);
                        if (docNcCustomer != null) {
                            NcCustomerCompare ncCustomerCompare = buildNcCustomerCompare(docNcCustomer, NC_DOC_ACCOUNT_ID, customerDoc);
                            ncCustomerCompareMapper.insert(ncCustomerCompare);
                            customerFinanceDingMsgParam.setDocNcCustomerCompare(ncCustomerCompare);

                            NcCustomerCompareZg ncCustomerCompareZg = buildNcCustomerCompareZg(docNcCustomer, NC_DOC_ACCOUNT_ID, customerDoc);
                            ncCustomerCompareZgMapper.insert(ncCustomerCompareZg);
                            customerFinanceDingMsgParam.setDocNcCustomerCompareZg(ncCustomerCompareZg);
                        }
                    }


                    log.info("=============  第四步：再创建店仓道具总仓 ======================");
                    /* 第四步：再创建店仓道具总仓 */
                    CStoreVo cStoreAllProp = createStoreDoc(flowDataDTO, brandRuleMap, customerProp, cArcBrandId, "3", false, storeAllDoc);

                    dingMsgParamList.add(new DingMsgParam(flowDataDTO, customerDoc, customerProp, null, null, "既新建经销商又新建店仓", "1", null));

                    if(customerDoc!= null){
                        // 转化得到crmId 根据iD查询
//                        Long id = flowDataDTO.getKhid();
                        // 大区经理 销售区域
                        String dqjl = flowDataDTO.getDqjl();
                        String newAreaId = flowDataDTO.getNewAreaId();

//                        UfKhid ufKhid = ufKhidMapper.selectById(id);
                        // 拿到信息
                        Long cArcbrandId = customerDoc.getCArcbrandId();
                        String clopStore = customerDoc.getClopStore();
                        String time = DateUtil.formatToStr(new Date(), "yyyy/MM/dd");
//                        Long areamngId = customerDoc.getBigareamngId();
                        String code = customerDoc.getCode();
                        //更新数据
                        updateCrmInfo(flowDataDTO.getKhid(),cArcbrandId+"",
                                time,StringUtils.isBlank(dqjl) ? null : dqjl,
                                StringUtils.isBlank(clopStore)  ? null : clopStore,code,customerDoc.getId());
                    }
                    Long id = customerDoc.getId();
                    return id;
                }catch (Exception e ){
                    log.error("=== 经销==》既新建经销商又新建店仓：执行出现异常time:{} ===",new Date().getTime(), e);
                }
            }


            if (!CollectionUtils.isEmpty(dingMsgParamList)) {
                sendDingDingMessage(dingMsgParamList);
            }

        }
        return null;
        // 新增 首次合作时间  首次合作品牌   大区经理  经销区域
    }


    public void sendDingDingMessage(List<DingMsgParam> dingMsgParamList) {
        final QueryWrapper<BusinessControlVo> wrapper = new QueryWrapper<>();
        wrapper.eq("BUSINESS_NAME", "OA到伯俊流程创建钉钉通知");
        wrapper.eq("BUSINESS_TYPE", "3");
        wrapper.eq("STATUS", "1");

        List<BusinessControlVo> controlVoList = businessControlMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(controlVoList)) {
            log.error("=== 钉钉消息没有发送的人员,直接退出 ===");
            return;
        }

        List<Boolean> flagList = new ArrayList<>();
        for (DingMsgParam dingMsgParam : dingMsgParamList) {

            try {
                // 回写OA流程表数据
                writeBackOaFlowData(dingMsgParam);
            } catch (Exception e) {
                log.error("=== 回写OA流程档案数据出现异常 ===", e);
            }

            try {
                // 推送钉钉消息
                getSendDingDingMsgPerson(controlVoList, flagList, dingMsgParam);
            } catch (Exception e) {
                log.error("=== 推送钉钉消息插入数据出现异常 ===", e);
            }
        }
        final boolean res = flagList.stream().allMatch(e -> e.equals(true));
        log.info("====== 钉钉推送数据入库结果: {} ======", res);
    }


    private void writeBackOaFlowData(DingMsgParam dingMsgParam) {

        final FlowDataDTO flowDataDTO = dingMsgParam.getFlowDataDTO();
        final CCustomerVo cCustomerVo = dingMsgParam.getCCustomerVo();
//        final CStoreVo storeDoc = dingMsgParam.getStoreDoc();
//        final CStoreVo cStoreProp = dingMsgParam.getCStoreProp();

        // 账号转换成小写
//        String code = storeDoc.getCode().toLowerCase(Locale.ROOT);
//        String codeProp = cStoreProp.getCode().toLowerCase(Locale.ROOT);

//        final StringBuilder stringBuilder1 = new StringBuilder();
//        stringBuilder1
//                .append("店仓：").append(code.toUpperCase(Locale.ROOT)).append(" ").append(storeDoc.getName()).append(" ")
//                .append("用户账号：").append(" ").append(code).append("@jnby.com").append(" ").append("\r\n")
//                .append("道具仓：").append(codeProp.toUpperCase(Locale.ROOT)).append(" ").append(cStoreProp.getName()).append(" ")
//                .append("用户账号：").append(" ").append(codeProp).append("@jnby.com").append(" ").append("\r\n");

        // 直营店类型
//        String directSaleType = "2";
//        if (directSaleType.equals(dingMsgParam.getType())) {
//            FormtableMain54Vo formtableMain54Vo = new FormtableMain54Vo();
//            //  新建店仓名称及用户名、密码
//            formtableMain54Vo.setXjdcmcyhmmm(stringBuilder1.toString());
//
//            final QueryWrapper<FormtableMain54Vo> wrapper = new QueryWrapper<>();
//            wrapper.eq("REQUESTID", flowDataDTO.getRequestId());
//            final int effRow = formTableMainDirectSaleMapper.update(formtableMain54Vo, wrapper);
//            log.info("=== 更新直营店OA流程数据结果flag:{} ===", effRow > 0);
//            return;
//        }

//        final FormtableMain53Vo main53Vo = new FormtableMain53Vo();
//        // 新建伯俊经销商名称示例： (AKA366)青岛(APN)
//        main53Vo.setXjbjjxs(cCustomerVo.getName());
//        // 新建店仓名称及用户名、密码
//        main53Vo.setXjdcmc(stringBuilder1.toString());
//
//        final QueryWrapper<FormtableMain53Vo> wrapper = new QueryWrapper<>();
//        wrapper.eq("REQUESTID", flowDataDTO.getRequestId());
//        final int effRow = formTableMainDealerMapper.update(main53Vo, wrapper);
//        log.info("=== 更新经销店OA流程数据结果flag:{} ===", effRow > 0);
    }



    private void getSendDingDingMsgPerson(List<BusinessControlVo> controlVoList, List<Boolean> flagList, DingMsgParam dingMsgParam) {
        DingtalkSendMsgVo dingtalkSendMsgVo;
        for (BusinessControlVo businessControlVo : controlVoList) {
            dingtalkSendMsgVo = new DingtalkSendMsgVo();
            if (businessControlVo.getBusinessId() == null) {
                continue;
            }

            Long id = dingtalkSendMsgMapper.getIdSequences("DINGTALK_SEND_MSG");
            dingtalkSendMsgVo.setId(id);
            dingtalkSendMsgVo.setLinkId(businessControlVo.getBusinessId().toString());
            dingtalkSendMsgVo.setMsgTitle("OA流程创建档案提醒(" + dingMsgParam.getStr() + ")");


            CCustomerVo cCustomerVo = dingMsgParam.getCCustomerVo();
            CCustomerVo customerProp = dingMsgParam.getCustomerProp();
            CStoreVo storeDoc = dingMsgParam.getStoreDoc();
            CStoreVo cStoreProp = dingMsgParam.getCStoreProp();


            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(dingMsgParam.getStr());
            if (cCustomerVo != null) {
                stringBuilder.append("【经销商ID：").append(cCustomerVo.getId()).append(" Code：").append(cCustomerVo.getCode());
                stringBuilder.append(" Name：").append(cCustomerVo.getName()).append("】/");
            }
            if (customerProp != null) {
                stringBuilder.append("【经销道具ID：").append(customerProp.getId()).append(" Code：").append(customerProp.getCode());
                stringBuilder.append(" Name：").append(customerProp.getName()).append("】/");
            }
            if (storeDoc != null) {
                stringBuilder.append("【店仓ID：").append(storeDoc.getId()).append(" Code：").append(storeDoc.getCode());
                stringBuilder.append(" Name：").append(storeDoc.getName()).append("】");
            }
            if (cStoreProp != null) {
                stringBuilder.append("/【店仓道具ID：").append(cStoreProp.getId()).append(" Code：").append(cStoreProp.getCode());
                stringBuilder.append(" Name：").append(cStoreProp.getName()).append("】");
            } else {
                stringBuilder.append("店仓道具: ").append("不需要创建店仓道具");
            }

            final CStoreVo specialSaleStoreDoc = dingMsgParam.getRight();
            if (specialSaleStoreDoc != null) {
                stringBuilder
                        .append("/【特卖仓ID：").append(specialSaleStoreDoc.getId())
                        .append(" Code：").append(specialSaleStoreDoc.getCode())
                        .append(" Name:").append(specialSaleStoreDoc.getName()).append("】");
            }

            stringBuilder.append("  ");
            try {
                stringBuilder.append("创建时间：").append(DateUtil.formatToStr(new Date(),"yyyy-MM-dd HH:mm:ss"));
            } catch (Exception e) {
                e.printStackTrace();
            }
            dingtalkSendMsgVo.setMsgContent(stringBuilder.toString());
            dingtalkSendMsgVo.setFlag(0L);
            dingtalkSendMsgVo.setSendDate(new Date());
            dingtalkSendMsgVo.setMsgType("TEXT");
            dingtalkSendMsgVo.setCreateDate(new Date());
            dingtalkSendMsgVo.setUpdateDate(new Date());
            final int effRow = dingtalkSendMsgMapper.insert(dingtalkSendMsgVo);
            flagList.add(effRow > 0);

        }
    }



    /**
     * 创建店仓档案
     *
     * @param flag  true 表示直营店仓  false表示经销
     * @param store 店仓档案生成道具时, 此参数为对应店仓的对象,如果创建店仓则传递NULL
     * @return 结果集对象
     */
    public CStoreVo createStoreDoc(FlowDataDTO flowDataDTO, Map<String, BrandRuleContrast> brandRuleMap, CCustomerVo customerDoc,
                                   Long cArcBrandId, String ruleIndex, Boolean flag, CStoreVo store) {
        final CStoreVo cStoreVo = new CStoreVo();
        /* 属性参数赋值 */
        fieldSetParam(flowDataDTO, cStoreVo, brandRuleMap, ruleIndex, store);
        if (cStoreVo.getId() == null) {
            log.error("====== 创建店仓属性参数赋值失败，无法创建，直接退出 ======");
            return null;
        }
        // 设置品牌架构
        cStoreVo.setCArcbrandId(cArcBrandId);
        // 设置店仓所属经销商
        cStoreVo.setCCustomerId(customerDoc.getId());

        // 判定有几条记录店仓道具记录
        Integer count = 0;
        // 获取上级经销的编码Code
        String codeRes;
        if (flag) {
            // 直营店特定参数赋值
            directStoreFieldDifficult(flowDataDTO, cStoreVo, store);
            // 直营店的所属经销商编码=5DA+城市编码
            codeRes = brandRuleMap.get(ruleIndex).getCode() + flowDataDTO.getGeneratorCode();
            count = getStoreCountDataByCode(codeRes);
        } else {
            // 经销数据处理 codeRes=2KA065
            codeRes = customerDoc.getCode();
            log.info("----- codeRes:{} ------", codeRes);
            if (store == null) {
                count = getStoreCountDataByCustomerId(customerDoc.getId(), flowDataDTO.getGeneratorCode(), codeRes);
            }
        }
        // 大区经理
        cStoreVo.setBigareamngNew(flowDataDTO.getDqjlName());
        // 区域/城市经理
        cStoreVo.setAreamngNew(flowDataDTO.getQycsjlName());
        // 区域/销售主管
        cStoreVo.setRemark1(flowDataDTO.getQyxszgName());
        /* 经销商 5KA699 ->5KA ->A  / 道具 5K10699 ->5k10 ->10 注意：直营店返回null */
        String newNextCode = generatorCodeIndex(codeRes, flowDataDTO.getGeneratorCode(), brandRuleMap.get(ruleIndex).getCode(), flag);

        boolean flagNew = false;
        String originalName = null;
        String codePropNum = null;
        /* 历史数据没按照规则来, Code需要替换，然后自动填充 */
        if (store != null && org.apache.commons.lang3.StringUtils.isNotBlank(newNextCode) && (newNextCode.length() == codeRes.length() || newNextCode.length() > GlobalCommonVariable.NUM)) {
            String newCode = store.getCode();
            originalName = codeRes;
            StringBuilder stringBuilder = new StringBuilder(newCode);
            codePropNum = generatorPropNum(newCode.substring(2, 3));
            stringBuilder.replace(2, 3, codePropNum);
            codeRes = stringBuilder.toString();
            log.info("==== 注意：当前历史店仓数据未按照规则,含有特殊字符串启动自动更换方案 newNextCode：{} 原经销商code:{} 新生成的Code:{} ====", newNextCode, originalName, codeRes);
            flagNew = true;
        }
        //  (5DD263)杭州天目里女(道具) 根据城市+商场名来的
        String realName = flowDataDTO.getName().toUpperCase();
        String showName = realName.substring(0, realName.indexOf("（"));

        BrandRuleContrast brandRuleContrast = brandRuleMap.get(ruleIndex);

        String newCode;
        if (count == null || count <= 0) {
            // 0 代表前面补充0   2代表长度为2  d 代表参数为正数型
            newCode = String.format("%02d", 1);
        } else {
            newCode = String.format("%02d", count + 1);
        }

        // 店仓道具序号和店仓对应一致
        if (store == null) {
            cStoreVo.setCountCode(newCode);
        } else {
            newCode = store.getCountCode();
            // 道具仓赋值所属货品仓【REMARK7】
            cStoreVo.setRemark7(store.getId());
        }

        // 动态替换生成创建店仓名称
        String newName = generatorName(brandRuleContrast, newNextCode, flowDataDTO.getGeneratorCode(),
                null, showName, null);

        if (flagNew) {
            cStoreVo.setCode(codeRes);
            newName = newName.replace(originalName, codePropNum);
            log.info("--- 注意：生成店仓数据的Code历史数据未按照规则转换后结果 cStoreVo.setCode(codeRes):{} newName:{} codePropNum:{} ---", codeRes, newName, codePropNum);
        } else {
            cStoreVo.setCode(codeRes + newCode);
        }
        // 店仓名称Name字段判重
        newName = storeByName(newName, showName);

        cStoreVo.setName(newName);
        cStoreVo.setYStore(newName);

        /* 如果是总仓: 生成的名称和经销商名称一致 */
        if (GlobalCommonVariable.ALL_STORE_TYPE_SET.contains(ruleIndex)) {
            cStoreVo.setName(customerDoc.getName().toUpperCase());
            cStoreVo.setYStore(customerDoc.getName().toUpperCase());
        }

        /* 生成对应描述名称 */
        final String nameDesc = generatorNameDesc(cStoreVo.getName(), flowDataDTO.getGeneratorCode());
        cStoreVo.setDescription(nameDesc);
        /* 新需求：Name和Desc需要设置成和名称一致 */
        cStoreVo.setDescription(cStoreVo.getName());

        log.info("===== 执行插入店仓CODE:{} NAME:{} ===", cStoreVo.getCode(), cStoreVo.getName());
        int effRow = 0;
        try {
            effRow = cStoreMapper.insert(cStoreVo);
            log.info("===== 执行插入店仓 SUCCESS ****************** 成功 =====");
        } catch (Exception e) {
            dingDingMsgRemind(null, "插入店仓档案异常 执行插入店仓CODE:" + cStoreVo.getCode() + " NAME:" + cStoreVo.getName());
            log.error("===== 执行插入店仓 fail ****************** 失败 =====");
            log.error("===== 执行插入店仓CODE 发生异常e ===== ", e);
        }

        /* 调用存储过程,填充子表字段数据 */
        final Long storeId = cStoreVo.getId();
        Map<String, Object> map = new HashMap<>(1);
        map.put("storeId", storeId);
        cCustomerMapper.insertIntoStoreChildTableAc(map);
        cCustomerMapper.insertIntoStoreChildTableAm(map);

        log.info("=== 新建店仓档案结果：{} ruleIndex:{} ===", effRow > 0, ruleIndex);

        // 货品仓关联道具仓的ID
        if (store != null) {
            CStoreVo updateCStore = new CStoreVo();
            updateCStore.setId(store.getId());
            updateCStore.setRemark7(cStoreVo.getId());
            cStoreMapper.updateById(updateCStore);
        }

        /* 动态创建档案用户登录账号数据 */
        int status = 3;
        // 店仓分仓
        BrandRuleContrast brandRuleContrastVo = brandRuleMap.get("10");

        /* 如果是总仓: 生成的名称和经销商名称一致 */
        if (GlobalCommonVariable.ALL_STORE_TYPE_SET.contains(ruleIndex)) {
            status = 1;
            // 店仓总仓
            brandRuleContrastVo = brandRuleMap.get("9");
        }
        // 如果不等于null表明是道具
        if (store != null) {
            status = 2;
            // 店仓道具分仓
            brandRuleContrastVo = brandRuleMap.get("12");
        }

        // 既是总仓也是道具不创建账号
        if (store != null && GlobalCommonVariable.ALL_STORE_TYPE_SET.contains(ruleIndex)) {
            log.warn(" ==== 既是总仓也是道具不创建账号==== ");
            log.warn(" ==== 新需求：既是总仓也是道具创建账号 ========== ");
        }

        // 如果是直营店
        int type = 1;
        if (flag) {
            type = 2;
            // 道具仓
            if (store != null) {
                brandRuleContrastVo = brandRuleMap.get("14");
            } else {
                brandRuleContrastVo = brandRuleMap.get("13");
            }
        }
        CreateUserParam createUserParam = new CreateUserParam(flowDataDTO, customerDoc, cStoreVo, type, brandRuleContrastVo, status);
        UsersVo docUser = createDocUser(createUserParam);
        log.info("=== 创建用户信息 id:{} NAME:{} TrueName:{} ===", docUser.getId(), docUser.getName(), docUser.getTruename());

        return cStoreVo;
    }

    public UsersVo createDocUser(CreateUserParam createUserParam) {
        final UsersVo usersVo = new UsersVo();
        // 通过参考对象完成部分字段数据值
        List<UsersVo> usersList = getUsersByName(createUserParam);
        if (CollectionUtils.isEmpty(usersList)) {
            log.error("=== 无法获取到用户信息,无法完成参数赋值,直接退出 ===");
            return null;
        }
        UsersVo users = usersList.get(0);
        // 对象属性赋值
        BeanUtils.copyProperties(users, usersVo);

        final CStoreVo cStoreVo = createUserParam.getCStoreVo();
        if (cStoreVo == null) {
            log.error("=== 没有店仓数据对象，无法完成赋值，直接退出 ===");
            return null;
        }

        // 特定参数对象参数属性赋值
        storeUserCreateRule(createUserParam, usersVo);
        try {
            // 执行插入
            final int effRow = usersMapper.insert(usersVo);
            if (effRow > 0) {
                log.info("=== 执行插入用户成功： id:{} NAME:{} TrueName:{} ===", usersVo.getId(), usersVo.getName(), usersVo.getTruename());
                // 创建成功，执行存储过程
                executeDbSqlUsers(usersVo);
            }
            return usersVo;
        } catch (Exception e) {
            log.error("=== 店仓插入用户失败 ===", e);
        }
        return null;
    }

    public void executeDbSqlUsers(UsersVo usersVo) {
        final Long usersId = usersVo.getId();
        Map<String, Object> map = new HashMap<>(1);
        map.put("usersId", usersId);
        try {
            usersMapper.insertIntoUsersTableAc(map);
            usersMapper.insertIntoUsersTableAm(map);
        } catch (Exception e) {
            dingDingMsgRemind(usersVo, "用户创建执行保存程序异常" + (e.getMessage() == null ? "" : ":" + e.getMessage()));
            log.error(" === 执行创建用户的数据库的存储过程Exception: {} usersId： {} 需要执行call USERS_BD(usersId) 清洗数据====", e.getMessage(), usersVo.getId(), e);
        }
    }



    /**
     * 消息提醒调用
     *
     * @param paramData 参数
     */

    public void dingDingMsgRemind(Object paramData, String msg) {
        if (msg == null || "".equals(msg)) {
            return;
        }
        try {
            /* 获取需要发送推送消息的人群 */
            QueryWrapper<BusinessControlVo> wrapper = new QueryWrapper<>();
            wrapper.eq("BUSINESS_NAME", "OA到伯俊流程创建错误提醒");
            wrapper.eq("BUSINESS_TYPE", "7");
            wrapper.eq("STATUS", "1");

            List<BusinessControlVo> controlVoList = businessControlMapper.selectList(wrapper);
            if (CollectionUtils.isEmpty(controlVoList)) {
                log.error("=== 钉钉消息没有发送的人员,直接退出 ===");
                return;
            }

            DingtalkSendMsgVo dingtalkSendMsgVo;
            for (BusinessControlVo businessControlVo : controlVoList) {
                dingtalkSendMsgVo = new DingtalkSendMsgVo();
                Long id = dingtalkSendMsgMapper.getIdSequences("DINGTALK_SEND_MSG");
                dingtalkSendMsgVo.setId(id);
                dingtalkSendMsgVo.setLinkId(businessControlVo.getBusinessId().toString());
                dingtalkSendMsgVo.setMsgTitle("OA流程创建错误提醒");
                dingtalkSendMsgVo.setMsgContent("OA流程创建错误，请检查【(" + msg + ")" + (paramData == null ? "" : paramData.toString()) + "】");
                dingtalkSendMsgVo.setFlag(0L);
                dingtalkSendMsgVo.setSendDate(new Date());
                dingtalkSendMsgVo.setMsgType("TEXT");
                dingtalkSendMsgVo.setCreateDate(new Date());
                dingtalkSendMsgVo.setUpdateDate(new Date());
                dingtalkSendMsgMapper.insert(dingtalkSendMsgVo);
            }
        } catch (Exception e) {
            log.error("=== 调用钉钉发送消息错误,可忽略 ===");
        }
    }



    /**
     * 门店规则数据赋值用户创建
     */
    public void storeUserCreateRule(CreateUserParam createUserParam, UsersVo usersVo) {
        // 通过序列获取自增ID
        Long usersId = cCustomerMapper.getSequencesByTableName("USERS");
        usersVo.setId(usersId);

        final CStoreVo cStoreVo = createUserParam.getCStoreVo();
        //  所属店仓 C_STORE_ID 该账号当前所属门店店仓档案的id
        usersVo.setCStoreId(cStoreVo.getId());
        String code = cStoreVo.getCode().toLowerCase(Locale.ROOT);
        // 名称：门店的店仓编号改为小写，如店仓编号为1DA26301，则名称为1da26301；1DD26301，名称为1dd26301
        usersVo.setName(code);
        // 真实姓名：等同门店的店仓名称
        usersVo.setTruename(cStoreVo.getName());
        usersVo.setIsUpuserpassword("Y");
        // 是否允许分配权限 1：是，2：否，默认2
        usersVo.setIsadmin(2);
        // IS_CLOSE 10：是，20：否，默认10
        usersVo.setIsClose("Y");
        // 初始密码Jnby1994
        usersVo.setPasswordhash(GlobalCommonVariable.DEFAULT_PASSWORD);
        // 字段访问级别
        usersVo.setSgrade(15);
        // 用户登录账号 登录账号，规则为名称<EMAIL>,若名称为1ka26301，则邮件为*****************
        usersVo.setEmail(code + "@jnby.com");
        // 缺省语言
        usersVo.setLanguage("zh_CN");
        usersVo.setCreationdate(new Date());
        usersVo.setOwnerid(893L);
        usersVo.setModifierid(893L);

        BrandRuleContrast ruleContrastVo = createUserParam.getRuleContrastVo();
        String resourceCode = ruleContrastVo.getResourceCode();

        // 零售最低折扣 经销 0.1，直营0.9
        if (createUserParam.getType() == 1) {
            // 安全组来源账户:权限参考人
            usersVo.setOldName(resourceCode);
            // 子系统过滤 总仓账号：赋值为空 1 道具账号：赋值为空 2  分仓账号，赋值为：分销管理,数据查询,在线店务,内淘宝,零售 管理 3
            if (createUserParam.getStatus() == 3) {
                usersVo.setSubsystems("分销管理,数据查询,在线店务,内淘宝,零售 管理");
            }

            // 总仓账号：赋值为：总仓帐号
            if (createUserParam.getStatus() == 1) {
                usersVo.setDescription("总仓帐号");
            }
            // C_CUSTOMER_ID经销商 该账号所属经销商的id
            CCustomerVo cCustomerVo = createUserParam.getCCustomerVo();
            usersVo.setCCustomerId(cCustomerVo.getId());
            usersVo.setDiscountlimitNew(BigDecimal.valueOf(0.1));
        } else {
            usersVo.setDiscountlimitNew(BigDecimal.valueOf(0.9));
            // 安全组来源账户:权限参考人
            usersVo.setOldName(resourceCode);
            // 子系统过滤: 赋值为：分销管理,数据查询,在线店务,内淘宝,零售 管理
            usersVo.setSubsystems("分销管理,数据查询,在线店务,内淘宝,零售 管理");
            // 总仓账号：赋值为：总仓帐号
            if (createUserParam.getStatus() == 1) {
                usersVo.setDescription("总仓帐号");
            }
            // C_CUSTOMER_ID经销商 该账号所属经销商的id
            usersVo.setCCustomerId(176L);
        }
    }


    public List<UsersVo> getUsersByName(CreateUserParam createUserParam) {
        BrandRuleContrast ruleContrastVo = createUserParam.getRuleContrastVo();

        if (org.apache.commons.lang3.StringUtils.isBlank(ruleContrastVo.getResourceCode())) {
            log.error("===  传递的查询到ruleContrastVo.getResourceCode()为空，直接退出===");
            return null;
        }

        // 注意： USERS表中存储的账号为小写，需要转成小写
        QueryWrapper<UsersVo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("NAME", ruleContrastVo.getResourceCode().trim().toLowerCase(Locale.ROOT));
        return usersMapper.selectList(queryWrapper);
    }


    public String generatorNameDesc(String name, String code) {
        if (org.apache.commons.lang3.StringUtils.isBlank(name)) {
            throw new RuntimeException("throw ===>生成对应的名称,传递名称不能为空");
        }
        StringBuilder stringBuilder = new StringBuilder(name);
        int first = name.indexOf("(");
        int first2 = name.indexOf(")");
        if (first == -1 || first2 == -1) {
            throw new RuntimeException("throw ===>生成对应的名称,无法替换");
        }
        stringBuilder.replace(first + 1, first2, code);
        return stringBuilder.toString();
    }



    public String storeByName(String storeName, String showName) {
        final QueryWrapper<CStoreVo> wrapper = new QueryWrapper<>();
        wrapper.eq("NAME", storeName);
        Integer count = cStoreMapper.selectCount(wrapper);
        if (count > 0) {
            log.info("=== 店仓名称重复:{} 替换后名称: {} ===", storeName, storeName.replace(showName, showName + "店"));
            return storeName.replace(showName, showName + "店");
        }
        return storeName;
    }


    public Integer getStoreCountDataByCustomerId(Long customerId, String generatorCode, String resCode) {
        if (customerId == null) {
            log.error("===查询店仓，没有经销商ID === ");
            return null;
        }

        final QueryWrapper<CStoreVo> wrapper = new QueryWrapper<>();
        wrapper.select("CODE");
        wrapper.orderByDesc("ID");

        // 所属经销商ID和编码
        wrapper.eq("C_CUSTOMER_ID", customerId);
        wrapper.likeRight("CODE", resCode);

        final List<CStoreVo> cStoreVoList = cStoreMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(cStoreVoList)) {
            log.info("=== 当前经销商没有已经创建的店仓,返回0个生成经销店仓序号001 customerId:{} ====", customerId);
            return 0;
        }

        // 当前店仓最大值CODE
        final String code = cStoreVoList.get(0).getCode();
        if (org.apache.commons.lang3.StringUtils.isBlank(code)) {
            throw new RuntimeException("=== 查询店仓数据记录数据量,code不能为空 ===");
        }

        final String newNum = code.replace(resCode, "");

        log.info("--- 生成经销店仓序号newNum:{} 所属经销商resCode:{} ---", newNum, resCode);

        if (org.apache.commons.lang3.StringUtils.isNotBlank(newNum) && org.apache.commons.lang3.StringUtils.isNumeric(newNum)) {
            return Integer.parseInt(newNum);
        }
        log.error("=== 生成经销店仓序号失败,code转换失败或者是num不是数字newNum：{}  ===", newNum);
        throw new RuntimeException("=== 生成经销店仓序号失败 ===");
    }


    private Integer getStoreCountDataByCode(String code) {
        if (code == null) {
            log.error("===查询店仓，没有经销商code参数 === ");
            return null;
        }

        // 直营店查询所有店仓
        final QueryWrapper<CStoreVo> wrapper = new QueryWrapper<>();
        wrapper.select("CODE");
        wrapper.orderByDesc("ID");

        // 模糊查询匹配第一位 [值%]
        wrapper.likeRight("CODE", code);

        final List<CStoreVo> cStoreVos = cStoreMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(cStoreVos)) {
            log.info("=== 直营店创建店仓=》 查询店仓数据记录数据量为空Code:{} ，默认创建01", code);
            return 0;
        }

        // 数据库中最大Code值
        final String finalCode = cStoreVos.get(0).getCode();
        if (org.apache.commons.lang3.StringUtils.isBlank(finalCode)) {
            throw new RuntimeException("=== 直营店创建==》数据库中最大Code值为空");
        }
        final String codeNum = finalCode.replace(code, "");
        log.info("--- 生成直营店店仓序号codeNum:{} 所属经销商Code:{} ---", codeNum, code);

        if (org.apache.commons.lang3.StringUtils.isNumeric(codeNum)) {
            return Integer.parseInt(codeNum);
        }

        log.error("--- 直营店创建==》 生成对应的CODE失败codeNum：{} ---", codeNum);
        throw new RuntimeException("=== 直营店创建==》 生成对应的CODE失败codeNum：" + codeNum);

    }


    /**
     * 直营店仓新增字段赋值
     */
    public void directStoreFieldDifficult(FlowDataDTO flowDataDTO, CStoreVo cStoreVo, CStoreVo store) {
        try {
            // 建筑面积/实用面积
            if (org.apache.commons.lang3.StringUtils.isNotBlank(flowDataDTO.getJzmj())) {
                cStoreVo.setProportion(flowDataDTO.getJzmj());
                cStoreVo.setMonthfee(new BigDecimal(flowDataDTO.getJzmj()));
            }
        } catch (Exception e) {
            log.error("--- 直营店仓建筑面积/实用面积赋值异常 ---", e);
        }
        // 建筑面积
        cStoreVo.setProportion(flowDataDTO.getJzmj());
        // 实际面积
        if (org.apache.commons.lang3.StringUtils.isNotBlank(flowDataDTO.getMjlc())) {
            cStoreVo.setMonthfee(new BigDecimal(flowDataDTO.getMjlc().trim()));
        }
        // 人员编制人
        cStoreVo.setEmpcnt(flowDataDTO.getRybz());

        // 租赁合同开始日
        if (org.apache.commons.lang3.StringUtils.isNotBlank(flowDataDTO.getZlhtksr())) {
            String date1 = flowDataDTO.getZlhtksr();
            cStoreVo.setRentbegin(date1.replace("-", ""));
        }

        // 租赁合同到期日
        cStoreVo.setRentend(flowDataDTO.getZlhtdqr());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(flowDataDTO.getZlhtdqr())) {
            String date2 = flowDataDTO.getZlhtdqr();
            cStoreVo.setRentend(date2.replace("-", ""));
        }

        // 租赁合同到期限
        if (org.apache.commons.lang3.StringUtils.isNotBlank(flowDataDTO.getZlhtqx())) {
            try {
                String date3 = flowDataDTO.getZlhtqx();
                cStoreVo.setLeaseperiod(date3.replace("-", ""));
            } catch (Exception e) {
                log.error("--- 租赁期限填写错误Zlhtqx：{} ---", flowDataDTO.getZlhtqx(), e);
            }
        }

        // 装修费支出标准金额
        cStoreVo.setRencost(flowDataDTO.getZxfzcje());

        // 装修完工后开始使用月份
        cStoreVo.setUsemonth(flowDataDTO.getZxwghkssyyf());

        // 提前解约是否有赔偿要求   SELECT * FROM C_STOREATTRIBVALUE WHERE ID=898 OR ID=897
        if (org.apache.commons.lang3.StringUtils.isNotBlank(flowDataDTO.getSelectname())) {
            if (GlobalCommonVariable.YES.equals(flowDataDTO.getSelectname())) {
                cStoreVo.setCStoreattrib18Id(897L);
            } else {
                cStoreVo.setCStoreattrib18Id(898L);
            }
        }
        // 店铺所属公司
        if (org.apache.commons.lang3.StringUtils.isNotBlank(flowDataDTO.getYwstmc())) {
            List<CStoreattribvalueVo> storeAttributeVoData = getStoreAttributeVoData(flowDataDTO.getYwstmc().trim());
            if (!CollectionUtils.isEmpty(storeAttributeVoData)) {
                List<CStoreattribvalueVo> collect = storeAttributeVoData.stream()
                        .sorted(Comparator.comparing(CStoreattribvalueVo::getId))
                        .collect(Collectors.toList());

                cStoreVo.setCStoreattrib6Id(collect.get(0).getId());
            }

        }
        // 新开店仓放量区域
        if (org.apache.commons.lang3.StringUtils.isNotBlank(flowDataDTO.getXkdcflqyx())) {
            cStoreVo.setCQtyaddareaId(Long.parseLong(flowDataDTO.getXkdcflqyx()));
        }
        cStoreVo.setIsUnique("Y");

        // 是否奥莱 0
        if ("0".equals(flowDataDTO.getSfal())) {
            cStoreVo.setDefault05("是");
        }

        // 新开店仓销售区域
        if (org.apache.commons.lang3.StringUtils.isNotBlank(flowDataDTO.getXkdcxsqyx())) {
            final String xkdcxsqyxId = flowDataDTO.getXkdcxsqyx();
            if (!org.apache.commons.lang3.StringUtils.isNumeric(xkdcxsqyxId)) {
                log.error("=== 直营店OA传递的销售区域ID错误 :{} ==== ", xkdcxsqyxId);
                return;
            }
            if (store == null) {
                cStoreVo.setCAreaId(Long.parseLong(xkdcxsqyxId));
                return;
            }
            final List<CAreaVo> areaById = getAreaById(xkdcxsqyxId);
            if (CollectionUtils.isEmpty(areaById)) {
                return;
            }
            String name = areaById.get(0).getName() + "-道具";
            List<CAreaVo> cAreaVoList = getArea(name);
            if (CollectionUtils.isEmpty(cAreaVoList)) {
                return;
            }
            cStoreVo.setCAreaId(cAreaVoList.get(0).getId());
        }
    }


    /**
     * 经销店仓属性赋值 是否启用流水码 下拉框(是、否，10：Y，20：N)，默认选：是
     *
     * @param flowDataDTO 参数
     * @param cStore      店仓
     * @param index       类型 1 经销总仓 2 经销店仓 3 道具总仓 4道具店仓 5经销商档案
     */
    public void fieldSetParam(FlowDataDTO flowDataDTO, CStoreVo cStore, Map<String, BrandRuleContrast> brandRuleMap, String index, CStoreVo store) {

        /* 抽取指定Code记录进行复制 */
        final BrandRuleContrast ruleContrastVo = brandRuleMap.get(index);
        final String resourceCode = ruleContrastVo.getResourceCode();
        if (org.apache.commons.lang3.StringUtils.isBlank(resourceCode)) {
            log.error("--- BRAND_RULE_CONTRAST表的resourceCode字段不能为空 ruleContrastVo:{}---", ruleContrastVo);
            throw new RuntimeException("resourceCode不能为空");
        }

        final QueryWrapper<CStoreVo> cStoreWrapper = new QueryWrapper<>();
        cStoreWrapper.eq("CODE", resourceCode);
        final CStoreVo storeData = cStoreMapper.selectOne(cStoreWrapper);
        if (storeData == null) {
            log.error(" === 经销店仓属性赋值无法查询到示例记录无法创建，直接退出，请检查 resourceCode:{} ===", resourceCode);
            return;
        }
        // 属性对象赋值
        BeanUtils.copyProperties(storeData, cStore);

        // 经纬度
        final String jwd = flowDataDTO.getJwd();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(jwd)) {
            try {
                String[] split = jwd.replaceAll(" ", "").split(",");
                if (split.length > 0) {
                    cStore.setLongitude(new BigDecimal(split[0].trim()));
                    cStore.setLatitude(new BigDecimal(split[1].trim()));
                }
            } catch (Exception e) {
                log.error("=== 经纬度转换的异常,未按照指定格式填写 =={}===", jwd, e);
            }
        }

        // 销售区域 C_AREA表中ID
        String newAreaId = flowDataDTO.getNewAreaId();
        if (org.apache.commons.lang3.StringUtils.isBlank(newAreaId)) {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(flowDataDTO.getCAreaId())) {
                // 销售区域赋值
                List<CAreaVo> cAreaList;
                if (store == null) {
                    cAreaList = getArea(flowDataDTO.getCAreaId());
                } else {
                    cAreaList = getArea(flowDataDTO.getCAreaId() + "-道具");
                }
                if (!CollectionUtils.isEmpty(cAreaList)) {
                    cStore.setCAreaId(cAreaList.get(0).getId());
                }
            }
        } else {
            if (org.apache.commons.lang3.StringUtils.isNumeric(newAreaId)) {
                if (store == null) {
                    cStore.setCAreaId(Long.parseLong(newAreaId));
                } else {

                    // 根据销售区域ID获取销售区域名称
                    final List<CAreaVo> areaByIdList = getAreaById(newAreaId);

                    if (CollectionUtils.isEmpty(areaByIdList)) {
                        log.error("=== OA流程传递的AreaId销售区域错误: {} 无法查询到对应的销售区域Name ====", newAreaId);
                    } else {
                        String areaName = areaByIdList.get(0).getName();
                        List<CAreaVo> areaNewList = getArea(areaName + "-道具");
                        if (!CollectionUtils.isEmpty(areaNewList)) {
                            cStore.setCAreaId(areaNewList.get(0).getId());
                        }
                    }

                }
            } else {
                log.error("=== OA流程传递的AreaId销售区域错误: {} ====", newAreaId);
            }
        }
        cStore.setCProvinceId(flowDataDTO.getCProvinceId());
        cStore.setCCityId(flowDataDTO.getCCityId());
        cStore.setCDistrictId(flowDataDTO.getCDistrictId());

        // 默认是Y
        cStore.setIsUnique("Y");
        cStore.setAddress(flowDataDTO.getAddress());
        cStore.setProportion(flowDataDTO.getProportion());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(flowDataDTO.getMonthfee())) {
            try {
                cStore.setMonthfee(new BigDecimal(flowDataDTO.getMonthfee()));
            } catch (NumberFormatException e) {
                log.error("=== setMonthfee店仓字段赋值异常：{} ===", flowDataDTO.getMonthfee());
                e.printStackTrace();
            }
        }

        /* 店铺性质 / 店铺分类 / 店铺管理模式 / 楼层 从OA获取的市汉字，需要获取数据库中的ID编号*/
        try {
            List<CStoreattribvalueVo> store1 = getStoreAttributeVoData(flowDataDTO.getCStoreattrib7Id(), "DIM7");
            if (!CollectionUtils.isEmpty(store1)) {
                cStore.setCStoreattrib7Id(store1.get(0).getId());
            }
            List<CStoreattribvalueVo> store2 = getStoreAttributeVoData(flowDataDTO.getCStoreattrib8Id());
            if (!CollectionUtils.isEmpty(store2)) {
                cStore.setCStoreattrib8Id(store2.get(0).getId());
            }
            List<CStoreattribvalueVo> store3 = getStoreAttributeVoData(flowDataDTO.getCStoreattrib9Id());
            if (!CollectionUtils.isEmpty(store3)) {
                cStore.setCStoreattrib9Id(store3.get(0).getId());
            }
            List<CStoreattribvalueVo> store4 = getStoreAttributeVoData(flowDataDTO.getCStoreattrib15Id());
            if (!CollectionUtils.isEmpty(store4)) {
                cStore.setCStoreattrib15Id(store4.get(0).getId());
            }
        } catch (Exception e) {
            log.error("=== 店铺性质 / 店铺分类 / 店铺管理模式 / 赋值有异常 ===", e);
        }

        // 设置电话号码
        log.info("=== 设置电话号码:{} ===", flowDataDTO.getPhone());
        cStore.setPhone(flowDataDTO.getPhone().trim());

        // 预估开业时间
        String preOpenDate = flowDataDTO.getPreopendate();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(preOpenDate)) {
            try {
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                Date parse = format.parse(preOpenDate);
                SimpleDateFormat simpleDate = new SimpleDateFormat("yyyyMMdd");
                cStore.setPreopendate(Integer.parseInt(simpleDate.format(parse)));
            } catch (ParseException e) {
                log.error(" === 预估开业时间转换出现异常===", e);
            }
        }
        cStore.setIsactive("Y");
        cStore.setCreationdate(new Date());
        cStore.setModifieddate(new Date());
        // 初始信用额度
        cStore.setCreditlimit(0L);
        // 创建人 修改人 首张单据日期
        cStore.setOwnerid(893L);
        cStore.setModifierid(893L);
        cStore.setBilldateFrist(null);

        /* 设置甲方体系字段: 经销/直营共用一个字段jftx */
        cStore.setDefault01(flowDataDTO.getJftx());
        try {
            // 建筑面积/实用面积 （经销使用这个字段）
            if (org.apache.commons.lang3.StringUtils.isNotBlank(flowDataDTO.getDpmj())) {
                cStore.setProportion(flowDataDTO.getDpmj());
                cStore.setMonthfee(new BigDecimal(flowDataDTO.getDpmj()));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 增加细化城市/细化区县/商场名字段数据
        cStore.setDescription1(flowDataDTO.getXhcs());
        cStore.setDefault04(flowDataDTO.getXhq());
        cStore.setMarketName(flowDataDTO.getScm());

        // 城市级别
        final String bojuncsjb = flowDataDTO.getBojuncsjb();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(bojuncsjb) && org.apache.commons.lang3.StringUtils.isNumeric(bojuncsjb)) {
            cStore.setCStoreattrib1Id(Long.parseLong(bojuncsjb));
        }

        //  是否发券 0 是 1 否
        //  这个字段是伯俊的外键 SELECT	* FROM  C_STOREATTRIBVALUE WHERE id= 2322
        //  SELECT	* FROM  C_STOREATTRIBDEF WHERE id= 12 / SELECT	* FROM  C_STOREATTRIBVALUE WHERE C_STOREATTRIBDEF_id= 12
        final String isTicket = flowDataDTO.getSffq();
        if (org.apache.commons.lang3.StringUtils.isBlank(isTicket) || Objects.equals("1", isTicket.trim())) {
            cStore.setCStoreattrib12Id(2324L);
        } else {
            cStore.setCStoreattrib12Id(2322L);
        }

        // 地理区域设置
        if (org.apache.commons.lang3.StringUtils.isNotBlank(flowDataDTO.getDlqy1())) {
            cStore.setClopStore(flowDataDTO.getDlqy1());
        }

        // 复制ID重新设置
        Long seqId = cCustomerMapper.getSequencesByTableName("C_STORE");

        lock.lock();
        try {
            seqId = generatorId(seqId, 2);
        } catch (Exception exception) {
            exception.printStackTrace();
        } finally {
            num.set(0);
            lock.unlock();
        }
        cStore.setId(seqId);
    }


    public List<CStoreattribvalueVo> getStoreAttributeVoData(String param, String dimFlag) {
        if (org.apache.commons.lang3.StringUtils.isBlank(param)) {
            return null;
        }
        QueryWrapper<CStoreattribvalueVo> wrapper = new QueryWrapper<>();
        wrapper.eq("NAME", param);
        wrapper.eq("DIMFLAG", dimFlag);
        return cStoreattribvalueMapper.selectList(wrapper);
    }

    public List<CStoreattribvalueVo> getStoreAttributeVoData(String param) {
        if (org.apache.commons.lang3.StringUtils.isBlank(param)) {
            return null;
        }
        QueryWrapper<CStoreattribvalueVo> wrapper = new QueryWrapper<>();
        wrapper.eq("NAME", param);
        return cStoreattribvalueMapper.selectList(wrapper);
    }



    @Override
    public void syncStopStatus() {
        QueryWrapper<CrmCustomerMain> queryWrapper = new QueryWrapper();
        CrmCustomerMain crmCustomerMain = new CrmCustomerMain();
        crmCustomerMain.setIsDel(IsDeleteEnum.NORMAL.getCode());
        crmCustomerMain.setStatus(CrmStatus.COOPREATE_STATUS);
        queryWrapper.setEntity(crmCustomerMain);
        List<CrmCustomerMain> list = crmCustomerMainMapper.selectList(queryWrapper);
        // 查询子集
        for (CrmCustomerMain customerMain : list) {
            try {
                List<CrmCustomerRelation> crmCustomerRelations = crmCustomerRelationMapper.selectByCrmCustomerMainId(customerMain.getId());
                if(CollectionUtils.isNotEmpty(crmCustomerRelations)){
                    // code编码
                    List<String> collect = crmCustomerRelations.stream().map(r -> r.getCustomerCode()).collect(Collectors.toList());
                    List<CustomerDto> customerDtos = cCustomerMapper.selectByCodesAll(collect);
                    //
                    if(CollectionUtils.isNotEmpty(customerDtos)){
                        List<CustomerDto> customerDtosFilter = customerDtos.stream().filter(r -> r.getIsactive().equals("N")).collect(Collectors.toList());
                        if(customerDtosFilter.size() == customerDtos.size()){
                            // 更新当前这个状态为 已解约
                            CrmCustomerMain update = new CrmCustomerMain();
                            update.setId(customerMain.getId());
                            update.setUpdateTime(new Date());
                            update.setUpdateBy("系统定时更新解约状态");
                            update.setStatus(CrmStatus.TERMINATE_STATUS);
                            crmCustomerMainMapper.updateById(update);
                        }
                    }
                }
            }catch (Exception e){
                log.info("syncStopStatus 同步状态报错 = {}",JSONObject.toJSONString(customerMain),e);
            }

        }
    }

    @Override
    public void terminateContract(String requestId) {
        log.info("terminateContract req = {}",requestId);
        // 查询到当前的这个用户
        FormattableMain48Dto formattableMain48Dto = formtableMain48Mapper.selectById(requestId);
        if(formattableMain48Dto == null || formattableMain48Dto.getBjjxsmc() == null){
            log.info("terminateContract 未查询到数据或者伯俊经销商id为空 = {}",requestId);
            return ;
        }

        Long customerId = formattableMain48Dto.getBjjxsmc();
        // 根据customer_id 查询当前这个客户
        QueryWrapper<CrmCustomerRelation> queryWrapper = new QueryWrapper<>();
        CrmCustomerRelation crmCustomerRelation = new CrmCustomerRelation();
        crmCustomerRelation.setIsDel(IsDeleteEnum.NORMAL.getCode());
        crmCustomerRelation.setIsStop("0");
        crmCustomerRelation.setCustomerId(customerId+"");
        queryWrapper.setEntity(crmCustomerRelation);
        List<CrmCustomerRelation> crmCustomerRelations = crmCustomerRelationMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(crmCustomerRelations)){
            log.info("未查询到任何crm 未解约的数据");
            return ;
        }
        for (CrmCustomerRelation customerRelation : crmCustomerRelations) {
            // 处理数据
            CrmCustomerRelation update = new CrmCustomerRelation();
            update.setId(customerRelation.getId());
            update.setUpdateTime(new Date());
            update.setIsStop("1");
            crmCustomerRelationMapper.updateById(update);
            // 查询当前客户是否底下的经销商都已经解约了

            List<CrmCustomerRelation> allCrmCsutomer = crmCustomerRelationMapper.selectByCrmCustomerMainId(customerRelation.getCrmCustomerMainId());
            // 筛选数量
            long count = allCrmCsutomer.stream().filter(r->r.getIsStop().equals("0")).count();
            if(count == 0L){
                // 全部已经解约
                CrmCustomerMain updateCrmCustomerMain = new CrmCustomerMain();
                updateCrmCustomerMain.setId(customerRelation.getCrmCustomerMainId());
                updateCrmCustomerMain.setUpdateTime(new Date());
                updateCrmCustomerMain.setUpdateBy("系统定时更新解约状态");
                updateCrmCustomerMain.setStatus(CrmStatus.TERMINATE_STATUS);
                crmCustomerMainMapper.updateById(updateCrmCustomerMain);
            }
        }
    }

    @Override
    public CrmCreateDto findByUnionid(String unionid) {

        QueryWrapper<CrmCustomerMain> queryWrapper = new QueryWrapper<>();
        CrmCustomerMain crmCustomerMain = new CrmCustomerMain();
        crmCustomerMain.setUnionid(unionid);
        queryWrapper.setEntity(crmCustomerMain);
        List<CrmCustomerMain> crmCustomerMains = crmCustomerMainMapper.selectList(queryWrapper);
        if(CollectionUtils.isNotEmpty(crmCustomerMains)){
            CrmCreateDto crmCreateDto = new CrmCreateDto();
            CrmCustomerMain crmCustomerMain1 = crmCustomerMains.get(0);
            BeanUtils.copyProperties(crmCustomerMain1,crmCreateDto);
            return crmCreateDto;
        }
        return null;
    }

    @Override
    public void flushYearsLabel() {
        // 查询到合作时间不为空的
        com.github.pagehelper.Page<CrmCustomerMain> hPage = PageHelper.startPage(1,10000);
        QueryWrapper<CrmCustomerMain> queryWrapper = new QueryWrapper();
        queryWrapper.isNotNull("FIRST_FRANCHISE_TIME");
        queryWrapper.eq("IS_DEL",0);
        List<CrmCustomerMain> crmCustomerMains = crmCustomerMainMapper.selectList(queryWrapper);
        PageInfo<CrmCustomerMain> pageInfo = new PageInfo<>(hPage);
        List<CrmCustomerMain> list = pageInfo.getList();
        for (CrmCustomerMain crmCustomerMain : list) {
            String firstFranchiseTime = crmCustomerMain.getFirstFranchiseTime();
            // 获取开始合作时间  yyyy/MM/dd
            long l = new Date().getTime() - DateUtil.parseDate(firstFranchiseTime, "yyyy/MM/dd").getTime();
            long days = l / (24 * 60 * 60 * 1000);
            Long year = days / 360;

            if(year < 3){
                year = null;
            }else if(year >= 3 && year < 5){
                year = 3L;
            }else{
                if(year % 5 != 0){
                    long l1 = year / 5;
                    year = l1 * 5;
                }
            }

            // 增加标签  老客标签可以拿出来的
            if(year != null){
                // 查询是否有这样的数据
                QueryWrapper<CrmCustomerLabel> queryWrapper2 = new QueryWrapper();
                queryWrapper2.likeRight("ID",crmCustomerMain.getId()+"-");
                queryWrapper2.eq("CRM_CUSTOMER_MAIN_ID",crmCustomerMain.getId());
                queryWrapper2.eq("IS_DEL",0);
                List<CrmCustomerLabel> crmCustomerLabels = crmCustomerLabelMapper.selectList(queryWrapper2);
                if(CollectionUtils.isNotEmpty(crmCustomerLabels)){
                    for (CrmCustomerLabel crmCustomerLabel : crmCustomerLabels) {
                        CrmCustomerLabel update = new CrmCustomerLabel();
                        update.setId(crmCustomerLabel.getId());
                        update.setIsDel(IsDeleteEnum.IS_DELETED.getCode());
                        update.setUpdateTime(new Date());
                        crmCustomerLabelMapper.updateById(update);
                    }
                }

                CrmCustomerLabel crmCustomerLabel = new CrmCustomerLabel();
                crmCustomerLabel.setId(crmCustomerMain.getId() +"-" + IdLeaf.getId(IdConstant.CRM_CUSTOMER_MAIN));
                crmCustomerLabel.setCrmCustomerMainId(crmCustomerMain.getId());
                crmCustomerLabel.setIsDel(IsDeleteEnum.NORMAL.getCode());
                crmCustomerLabel.setLabelName(year+"年老客");
                crmCustomerLabelMapper.insert(crmCustomerLabel);
            }
        }
    }

    @Override
    public void importCrmFileChangeData(String url) {
        // 导入excel
        // 导入旧的数据
        List<Map<String,String>> importData  = new ArrayList<>();
        //返回数据
        String filePath = FileParseUtil.downLoadExcel(url);
        EasyExcelUtil.read(filePath, new BaseNoModelDataAbstractListener() {

            @Override
            protected void saveData() {
                List<Map<Integer, String>> cachedDataList1 = this.cachedDataList;
                for (Map<Integer, String> integerStringMap : cachedDataList1) {
                    Map<String,String> result = new HashMap<>();
                    result.put("personName",integerStringMap.get(5));          //  法人姓名
                    result.put("phone",integerStringMap.get(6));               // 电话  法人联系电话：
                    result.put("customerControlCitys",integerStringMap.get(20));        //客户操作的城市
                    result.put("customerOperateBrands",integerStringMap.get(21));     // 客户经营品牌
                    result.put("operateStore",integerStringMap.get(22));           // 客户经销店铺数
                    result.put("yearScale",integerStringMap.get(23)); // 区    区有可能为空 是直接代理到市的
                    result.put("clothOperateYears",integerStringMap.get(26));  // crmId 缺了年份
                    result.put("erpSystem",integerStringMap.get(28));  //erp 系统
                    result.put("customerLevel",integerStringMap.get(34));  //品牌客户评级
                    result.put("mainCustomerLevel",integerStringMap.get(35));  // 主客户等级
                    result.put("customerEvaluate",integerStringMap.get(36));  // 客户评价
                    result.put("customerCode",integerStringMap.get(32));  // customercode 5AK01
                    result.put("crmId",integerStringMap.get(39));  // crmId
                    result.put("jnbyAccountFor",integerStringMap.get(40));  // 江南占比
                    importData.add(result);
                }
            }
        });
        // 封装并且更新数据  仅更新
        buildAndUpdateData(importData);
        try {
            Files.deleteIfExists(Paths.get(filePath));
        }catch (Exception e){
            log.error("e= ",e);
        }
    }

    @Override
    public String getOaToken() {
        // 从系统缓存或者数据库中获取ECOLOGY系统公钥和Secret信息
        String secret = crmSecrit;
        String spk = crmSpk;

        // 公钥加密,所以RSA对象私钥为null
        RSA rsa = new RSA(null,spk);
        //对秘钥进行加密传输，防止篡改数据
        String encryptSecret = rsa.encryptBase64(secret, CharsetUtil.CHARSET_UTF_8, KeyType.PublicKey);

        Map<String,String> mapHeader = new HashMap<>();
        mapHeader.put("APPID",crmAppid);
        mapHeader.put("secret",encryptSecret);

        okhttp3.Headers headers = Headers.of(mapHeader);
        String post = HttpUtils.post(crmUrl +"/api/ec/dev/auth/applytoken", new HashMap<>(), headers);
        Map<String,Object> datas = JSONObject.parseObject(post,Map.class);
        //ECOLOGY返回的token
        // TODO 为Token缓存设置过期时间
        String token = (String) datas.get("token");
        return token;
    }

    @Override
    public String createOaFlow(CreateOaFlowDto requestData) {
        // 如果状态是准入 那么就是提交了准入  需要返回数据
        CrmCustomerMain crmCustomerMain1 = crmCustomerMainMapper.selectById(requestData.getId());
        String oaFlow = createOaFlow(crmCustomerMain1, requestData);
        if(StringUtils.isNotBlank(oaFlow)){
            // 调用小忠接口 返回一个 http链接
            Result<?> result = sysBaseAPI.applyPermission(oaFlow);
            log.info("createOaFlow 获取url = {}",JSONObject.toJSONString(result));
            if(result == null){
                throw new RuntimeException("暂无OA准入权限，请联系OA管理员");
            }
            if(result.getData() == null){
                throw new RuntimeException("暂无OA准入权限，请联系OA管理员");
            }
            Object data = result.getData();
            return data+"";
        }
        return null;
    }

    @Override
    public void updateRelation(List<UpdateCrmRelationDto> requestData) {
        for (UpdateCrmRelationDto requestDatum : requestData) {
            if(StringUtils.isNotBlank(requestDatum.getId())){
                CrmCustomerRelation crmCustomerRelation = new CrmCustomerRelation();
                crmCustomerRelation.setUpdateTime(new Date());
                crmCustomerRelation.setId(requestDatum.getId());
                crmCustomerRelation.setCustomerLevel(requestDatum.getCustomerLevel());
                crmCustomerRelation.setCustomerEvaluate(requestDatum.getCustomerEvaluate());
                crmCustomerRelationMapper.updateById(crmCustomerRelation);
            }
        }
    }

    private void buildAndUpdateData(List<Map<String, String>> importData) {
        //开始处理数据
        for (Map<String, String> importDatum : importData) {
            try {
                String crmId = importDatum.get("crmId");
                String customerCode = importDatum.get("customerCode");
                String customerEvaluate = importDatum.get("customerEvaluate");
                String customerLevel = importDatum.get("customerLevel");
                String erpSystem = importDatum.get("erpSystem");
                String clothOperateYears = importDatum.get("clothOperateYears");
                String yearScale = importDatum.get("yearScale");
                String operateStore = importDatum.get("operateStore");
                String customerOperateBrands = importDatum.get("customerOperateBrands");
                String customerControlCitys = importDatum.get("customerControlCitys");
                String phone = importDatum.get("phone");
                String personName = importDatum.get("personName");
                String mainCustomerLevel = importDatum.get("mainCustomerLevel");
                String jnbyAccountFor = importDatum.get("jnbyAccountFor");
                // 查询数据
                if(StringUtils.isBlank(crmId)){
                    continue;
                }
                // 查询数据处理
                QueryWrapper<CrmCustomerMain> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("crm_id",crmId);
                queryWrapper.eq("IS_DEL",0);
                List<CrmCustomerMain> crmCustomerMains = crmCustomerMainMapper.selectList(queryWrapper);
                if(CollectionUtils.isEmpty(crmCustomerMains)){
                    log.info("根据crmId = {}未查询到数据",crmId);
                    continue;
                }
                // 处理数据
                String crmCustomerMainId = crmCustomerMains.get(0).getId();
                CrmCustomerMain updateCrmCustomerMain = new CrmCustomerMain();
                updateCrmCustomerMain.setId(crmCustomerMainId);
                updateCrmCustomerMain.setUpdateTime(new Date());
                updateCrmCustomerMain.setMainCustomerLevel(mainCustomerLevel);
                if(StringUtils.isNotBlank(erpSystem)){
                    updateCrmCustomerMain.setErpSystem(erpSystem);
                }
                if(StringUtils.isNotBlank(customerControlCitys)){
                    updateCrmCustomerMain.setCustomerControlCitys(customerControlCitys);
                }
                if(StringUtils.isNotBlank(customerOperateBrands)){
                    updateCrmCustomerMain.setCustomerOperateBrands(customerOperateBrands);
                }
                if(StringUtils.isNotBlank(operateStore)){
                    updateCrmCustomerMain.setOperateStore(operateStore);
                }
                if(StringUtils.isNotBlank(yearScale)){
                    updateCrmCustomerMain.setYearScale(yearScale);
                }
                if(StringUtils.isNotBlank(clothOperateYears)){
                    updateCrmCustomerMain.setClothOperateYears(clothOperateYears);
                }
                if(StringUtils.isNotBlank(personName)){
                    updateCrmCustomerMain.setCompanyLegalPerson(personName);
                }
                if(StringUtils.isNotBlank(phone)){
                    updateCrmCustomerMain.setCompanyPhone(phone);
                }
                if(StringUtils.isNotBlank(jnbyAccountFor)){
                    updateCrmCustomerMain.setJnbyAccountFor(jnbyAccountFor);
                }

                crmCustomerMainMapper.updateById(updateCrmCustomerMain);
                // 处理详情
                if(StringUtils.isBlank(customerCode)){
                    log.info("没有传递customerCode");
                    continue;
                }
                QueryWrapper<CrmCustomerRelation> queryWrapper2 = new QueryWrapper<>();
                queryWrapper2.eq("CRM_CUSTOMER_MAIN_ID",updateCrmCustomerMain.getId());
                queryWrapper2.eq("CUSTOMER_CODE",customerCode);
                queryWrapper2.eq("IS_DEL",0);
                List<CrmCustomerRelation> crmCustomerRelations = crmCustomerRelationMapper.selectList(queryWrapper2);
                if(CollectionUtils.isNotEmpty(crmCustomerRelations)){
                    String crmCustomerRelationId = crmCustomerRelations.get(0).getId();
                    CrmCustomerRelation updateCrmCustomerRelation = new CrmCustomerRelation();
                    updateCrmCustomerRelation.setId(crmCustomerRelationId);
                    updateCrmCustomerRelation.setUpdateTime(new Date());
                    if(StringUtils.isNotBlank(customerEvaluate)){
                        updateCrmCustomerRelation.setCustomerEvaluate(customerEvaluate);
                    }
                    if(StringUtils.isNotBlank(customerLevel)){
                        updateCrmCustomerRelation.setCustomerLevel(customerLevel);
                    }
//                    if(StringUtils.isNotBlank(personName)){
//                        updateCrmCustomerRelation.setLegalPerson(personName);
//                    }
//                    if(StringUtils.isNotBlank(phone)){
//                        updateCrmCustomerRelation.setLegalPersonPhone(phone);
//                    }
                    crmCustomerRelationMapper.updateById(updateCrmCustomerRelation);
                }
            }catch (Exception e ){
                log.info("更新数据出现错误  = {}",JSONObject.toJSONString(importDatum));
                continue;
            }

        }
    }


    private NcCustomerCompare buildNcCustomerCompare(NcCustomer ncCustomer, Integer ncAccountId, CCustomerVo cCustomerVo) {
        NcCustomerCompare ncCustomerCompare = new NcCustomerCompare();
        ncCustomerCompare.setNcAccountId(ncAccountId);
        ncCustomerCompare.setNcCustomerId(ncCustomer.getId());
        ncCustomerCompare.setCCustomerId(cCustomerVo.getId().intValue());
        ncCustomerCompare.setAdClientId(cCustomerVo.getAdClientId().intValue());
        ncCustomerCompare.setAdOrgId(cCustomerVo.getAdOrgId().intValue());
        ncCustomerCompare.setOwnerid(893);
        ncCustomerCompare.setModifierid(893);
        ncCustomerCompare.setCreationdate(new Date());
        ncCustomerCompare.setModifieddate(new Date());
        ncCustomerCompare.setIsactive("Y");
        ncCustomerCompare.setCInvocegroupmodeId(2);
        ncCustomerCompare.setKs(ncCustomer.getCustname());
        return ncCustomerCompare;
    }

    private NcCustomerCompareZg buildNcCustomerCompareZg(NcCustomer ncCustomer, Integer ncAccountId, CCustomerVo cCustomerVo) {
        NcCustomerCompareZg ncCustomerCompareZg = new NcCustomerCompareZg();
        ncCustomerCompareZg.setNcAccountId(ncAccountId);
        ncCustomerCompareZg.setNcCustomerId(ncCustomer.getId());
        ncCustomerCompareZg.setCCustomerId(cCustomerVo.getId().intValue());
        ncCustomerCompareZg.setAdClientId(cCustomerVo.getAdClientId().intValue());
        ncCustomerCompareZg.setAdOrgId(cCustomerVo.getAdOrgId().intValue());
        ncCustomerCompareZg.setOwnerid(893);
        ncCustomerCompareZg.setModifierid(893);
        ncCustomerCompareZg.setCreationdate(new Date());
        ncCustomerCompareZg.setModifieddate(new Date());
        ncCustomerCompareZg.setIsactive("Y");
        return ncCustomerCompareZg;
    }


    public Pair<CCustomerVo, CCustomerVo> createCustomerDoc(FlowDataDTO flowDataDTO, Map<String, BrandRuleContrast> brandRuleMap, Long cArcBrandId) {
        final CCustomerVo cCustomerVo = new CCustomerVo();
        BrandRuleContrast brandRuleContrast = brandRuleMap.get("5");

        /* 查询该区域是否已经有经销商或第几个经销商 定义Code  5k+%+城市编码,然后模糊查询 示例：5K_001 */
        QueryWrapper<CCustomerVo> customerWrapper = new QueryWrapper<>();
        customerWrapper.like("CODE", brandRuleContrast.getCode() + "_" + flowDataDTO.getGeneratorCode());
        List<CCustomerVo> cCustomerVoList = cCustomerMapper.selectList(customerWrapper);

        /* 查询是否已经有经销商，没有就默认生成第一个有就默认递增 */
        String newNextCode;
        if (!org.springframework.util.CollectionUtils.isEmpty(cCustomerVoList)) {

            List<CCustomerVo> collectCustomer = cCustomerVoList.stream()
                    .filter(e -> !e.getName().contains("道具") && org.apache.commons.lang3.StringUtils.isNotBlank(e.getCode()))
                    .sorted(Comparator.comparing(CCustomerVo::getCode).reversed())
                    .collect(Collectors.toList());

            if (org.springframework.util.CollectionUtils.isEmpty(collectCustomer)) {
                log.error("=== 查询经销商数据异常请排查,实际应当一定有经销商主体信息: flowDataDTO {} ===", flowDataDTO);
                return null;
            }

            CCustomerVo cCustomerRes = collectCustomer.get(0);
            String code = cCustomerRes.getCode();

            // 这里截取的编码是0-3 比如5KA007--->调用createNext会生成5KB
            newNextCode = createNext(code.substring(0, 3).toUpperCase(Locale.ROOT)) + flowDataDTO.getGeneratorCode();
            // 如果是D需要越过D,再递增一位
            if (newNextCode.contains(STR)) {
                newNextCode = createNext(newNextCode.substring(0, 3).toUpperCase(Locale.ROOT)) + flowDataDTO.getGeneratorCode();
            }

        } else {
            newNextCode = brandRuleContrast.getCode() + "A" + flowDataDTO.getGeneratorCode();
        }

        //  创建经销商===================================================================================================
        customerFieldDocParamSet(flowDataDTO, cCustomerVo, brandRuleMap, "5");
//        if (flowDataDTO.getCCityId() == null) {
//            log.error("=== 流程创建的ProvinceId或CCityId为空，无法创建创建经销商数据,记录 RequestId：{}===", flowDataDTO.getRequestId());
//            return null;
//        }

        if (org.apache.commons.lang3.StringUtils.isBlank(flowDataDTO.getGeneratorCodeName())) {
            log.error("=== 传递的经销商城市generatorCodeName为空 ,无法创建经销商,直接退出 === ");
            return null;
        }
        String cityName = flowDataDTO.getGeneratorCodeName();

        // 动态替换生成经销商名称
        String newName = generatorName(brandRuleContrast, newNextCode.substring(2, 3), flowDataDTO.getGeneratorCode(),
                null, cityName, null);

        // 经销商名称判重
        newName = customerCountByName(newName, cityName);

        log.info("=== 动态替换生成经销商名称：{} 动态生成经销商CODE:{} newName:{} ===", newName, newNextCode, newName);
        cCustomerVo.setCode(newNextCode);
        cCustomerVo.setName(newName);
        cCustomerVo.setDescription(newName);
        cCustomerVo.setYuanCustomer(newName);

        /* 插入数据库/ 调用存储过程，向经销商档案子表添加数据 */
        int effRow = 0;
        try {
            effRow = cCustomerMapper.insert(cCustomerVo);
            log.info("==== 执行插入经销商 SUCCESS 成功 ====");
        } catch (Exception e) {
            log.error("===== 执行插入经销商道具 fail 失败 =====");
            log.error("===== 执行经销商档案发生异常cCustomerVo：{} ====== ", cCustomerVo, e);
            return null;
        }

        // 调用存储过程
        executeDbSqlCustomer(cCustomerVo);
        // 折扣表数据清洗重填
        dealerDiscountUpdate(cCustomerVo, brandRuleContrast);

        //  创建经销商道具================================================================================================
        BrandRuleContrast brandRuleContrastProp = brandRuleMap.get("6");
        CCustomerVo cCustomerProp = new CCustomerVo();
        /* 经销商道具属性赋值 */
        customerFieldDocParamSet(flowDataDTO, cCustomerProp, brandRuleMap, "6");

        /* 生成对应的编号 示例：A ---> D / B ---> 1 / C ---> 2 */
        final String codeChar = cCustomerVo.getCode().substring(2, 3);
        String propNum = generatorPropNum(codeChar);
        String codeProp = brandRuleContrastProp.getCode() + propNum + flowDataDTO.getGeneratorCode();

        cCustomerProp.setCode(codeProp);
        String numCode = generatorCodeIndex(codeProp, flowDataDTO.getGeneratorCode(), brandRuleContrastProp.getCode(), null);

        // 动态替换生成经销商道具名称
        String newPropName = generatorName(brandRuleContrastProp, numCode, flowDataDTO.getGeneratorCode(),
                null, cityName, null);

        // 经销商名称判重
        newPropName = customerCountByName(newPropName, cityName);

        cCustomerProp.setName(newPropName);
        cCustomerProp.setDescription(newPropName);
        cCustomerProp.setYuanCustomer(cCustomerVo.getName());

        /* 创建经销商道具/调用存储过程，向经销商档案子表添加数据 */
        log.info("=== 动态替换生成经销商道具名称：{} 动态生成经销商CODE:{} newPropName：{}===", newName, newNextCode, newPropName);
        int effRowProp = 0;
        try {
            effRowProp = cCustomerMapper.insert(cCustomerProp);
            log.info("==== 执行插入经销商 SUCCESS 成功====");
        } catch (Exception e) {
            log.error("===== 执行插入经销商道具 fail 失败 =====");
            log.error("===== 执行经销商道具发生异常cCustomerVo：{} ====== ", cCustomerVo, e);
            return null;
        }

        // 调用存储过程
        executeDbSqlCustomer(cCustomerProp);

        if (effRow > 0 && effRowProp > 0) {
            log.info("=== 创建经销商和经销商道具成功 ID：{} CODE:{} 道具ID：{} CODE:{} SUCCESS ===", cCustomerVo.getId(), cCustomerVo.getCode(), cCustomerProp.getId(), cCustomerProp.getCode());
            return ImmutablePair.of(cCustomerVo, cCustomerProp);
        }
        return null;
    }

    public void customerFieldDocParamSet(FlowDataDTO flowDataDTO, CCustomerVo cCustomerVo,
                                         Map<String, BrandRuleContrast> brandRuleMap, String index) {
        /* 抽取相同记录进行复制 */
        final String resourceCode = brandRuleMap.get(index).getResourceCode();

        QueryWrapper<CCustomerVo> cCustomerVoQueryWrapper = new QueryWrapper<>();
        cCustomerVoQueryWrapper.eq("CODE", resourceCode);
        final CCustomerVo customerData = cCustomerMapper.selectOne(cCustomerVoQueryWrapper);

        if (customerData != null) {
            BeanUtils.copyProperties(customerData, cCustomerVo);
        }
        // 名称描述
        cCustomerVo.setDescription(flowDataDTO.getDescription());
        /* 获取品牌架构 */
        String brandName = flowDataDTO.getCArcbrandId();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(brandName)) {
            if (OA_BRAND_NAME_BOJUN.get(brandName.trim()) != null) {
                brandName = OA_BRAND_NAME_BOJUN.get(brandName.trim());
            }
            CArcbrand cArcbrandVo = getBrandFrameworkName(brandName);
            if (cArcbrandVo != null) {
                cCustomerVo.setCArcbrandId(cArcbrandVo.getId());
            }
        }
        // post 品牌
        cCustomerVo.setPost(brandName);
        String phone = flowDataDTO.getPhone();
        if (org.apache.commons.lang3.StringUtils.isNumeric(phone)) {
            cCustomerVo.setPhone(phone);
        }

        cCustomerVo.setCCityId(flowDataDTO.getCCityId());
        cCustomerVo.setCreationdate(new Date());
        cCustomerVo.setModifieddate(new Date());
        // 签约公司
        cCustomerVo.setAddress(null);
        // 默认期货折扣
        cCustomerVo.setFirsaledis(cCustomerVo.getFirsaledis());
        cCustomerVo.setIsactive("Y");

        // 复制ID重新设置
        Long seqId = cCustomerMapper.getSequencesByTableName("C_CUSTOMER");

        lock.lock();
        try {
            seqId = generatorId(seqId, 1);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            num.set(0);
            lock.unlock();
        }
        cCustomerVo.setId(seqId);
        // 创建人 修改人
        cCustomerVo.setOwnerid(893L);
        cCustomerVo.setModifierid(893L);

        // 经销商销售区域 C_AREA_ID
        String newAreaId = flowDataDTO.getNewAreaId();
        if (org.apache.commons.lang3.StringUtils.isBlank(newAreaId)) {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(flowDataDTO.getCAreaId())) {
                // 销售区域赋值 5是创建经销商 6是创建经销商道具
                List<CAreaVo> cAreaList;
                if ("5".equals(index)) {
                    cAreaList = getArea(flowDataDTO.getCAreaId());
                } else {
                    cAreaList = getArea(flowDataDTO.getCAreaId() + "-道具");
                }
                if (!org.springframework.util.CollectionUtils.isEmpty(cAreaList)) {
                    cCustomerVo.setCAreaId(cAreaList.get(0).getId());
                }
            }
        } else {
            // 销售区域赋值 5是创建经销商 6是创建经销商道具
            if (org.apache.commons.lang3.StringUtils.isNumeric(newAreaId)) {
                if ("5".equals(index)) {
                    cCustomerVo.setCAreaId(Long.parseLong(newAreaId));
                } else {
                    // 创建经销商道具
                    final List<CAreaVo> areaList = getAreaById(newAreaId);

                    if (org.springframework.util.CollectionUtils.isEmpty(areaList)) {
                        log.error("=== OA流程传递的AreaId销售区域错误: {} ====", newAreaId);
                    } else {

                        final String areaName = areaList.get(0).getName();

                        List<CAreaVo> areaNewList = getArea(areaName + "-道具");
                        if (!org.springframework.util.CollectionUtils.isEmpty(areaNewList)) {
                            cCustomerVo.setCAreaId(areaNewList.get(0).getId());
                        }
                    }
                }
            } else {
                log.error("=== OA流程传递的AreaId销售区域错误: {} ====", newAreaId);
            }
        }
    }

    private CArcbrand getBrandFrameworkName(String brandName) {
        QueryWrapper<CArcbrand> arcBrandWrapper = new QueryWrapper<>();
        arcBrandWrapper.eq("NAME", brandName);
        return cArcbrandMapper.selectOne(arcBrandWrapper);
    }

    public String generatorName(BrandRuleContrast ruleContrast, String dealerLetter, String cityCode, String areaNum,
                                String cityName, String address) {
        // 生成编号 &==>A/B/C/D  ***==>地区编码对照比如695
        // ADDRESS==》替换地址 例如：北京王府井 AA:自增编号 01/02/03 (5D&000)XXXADDRESS女(道具)
        return ruleContrast.getRuleNum()
                .replace("&", dealerLetter == null ? "" : dealerLetter)
                .replace("***", cityCode == null ? "" : cityCode)
                .replace("AA", areaNum == null ? "" : areaNum)
                .replace("XXX", cityName == null ? "" : cityName)
                .replace("ADDRESS", address == null ? "" : address);
    }


    public static String createNext(String string) {
        char[] tempChar = string.toCharArray();
        for (int i = tempChar.length - 1; i >= 1; i--) {
            if (tempChar[i] < 'z') {
                tempChar[i] = (char) (tempChar[i] + 1);
                break;
            } else {
                tempChar[i] = 'a';
                tempChar[i - 1] = (char) (tempChar[i - 1] + 1);
                if (tempChar[i - 1] <= 'z') {
                    i = 0;
                }
            }
        }
        return String.valueOf(tempChar);
    }

    public String customerCountByName(String dealerName, String cityName) {
        QueryWrapper<CCustomerVo> wrapper = new QueryWrapper<>();
        wrapper.eq("NAME", dealerName);
        Integer count = cCustomerMapper.selectCount(wrapper);
        if (count > 0) {
            String replaceName = dealerName.replace(cityName, cityName + "店");
            log.info("=== 经销商名称重复 原名称:{} 新名称：{} ===", dealerName, replaceName);
            return replaceName;
        }
        return dealerName;
    }


    public void executeDbSqlCustomer(CCustomerVo cCustomerVo) {
        final Long customerId = cCustomerVo.getId();
        Map<String, Object> map = new HashMap<>(1);
        map.put("customerId", customerId);
        cCustomerMapper.insertIntoCustomerChildTableAc(map);
        cCustomerMapper.insertIntoCustomerChildTableAm(map);
    }


    /**
     * 经销商折扣数据清洗
     *
     * @param cCustomerVo       新建的经销商参数对象
     * @param brandRuleContrast 规则数据
     * @return 结果集
     */
    public Boolean dealerDiscountUpdate(CCustomerVo cCustomerVo, BrandRuleContrast brandRuleContrast) {

        CCustomerVo customerData = getCustomerData(brandRuleContrast.getResourceCode());
        if (customerData == null) {
            log.error("=== error: 经销商折扣数据清洗，无法根据");
            return null;
        }

        /* 1.查询示例记录的默认折扣填写字段数据 */
        List<CCusdisdefVo> cCusList = getCCusdisdefByCustomerId(customerData.getId());
        if (org.springframework.util.CollectionUtils.isEmpty(cCusList)) {
            return null;
        }

        Map<String, CCusdisdefVo> cusBrandList = new HashMap<>();
        for (CCusdisdefVo cCusdisdefVo : cCusList) {
            String key = "_" + cCusdisdefVo.getMDim1Id();
            cusBrandList.put(key, cCusdisdefVo);
        }

        /* 2.查询刚新增的折扣表数据记录*/
        final List<CCusdisdefVo> newCusRecordList = getCCusdisdefByCustomerId(cCustomerVo.getId());
        if (org.springframework.util.CollectionUtils.isEmpty(newCusRecordList)) {
            log.error("=== 查询刚新增的折扣表数据记录为空 === ");
            return null;
        }

        List<Boolean> booleanList = new ArrayList<>();

        CCusdisdefVo cCusNew;
        for (CCusdisdefVo cCusdisdefVo : newCusRecordList) {

            String key = "_" + cCusdisdefVo.getMDim1Id();
            CCusdisdefVo newCusValue = cusBrandList.get(key);

            if (newCusValue == null) {
                log.error("== 经销商折扣数据清洗,获取Map参考数据为空 ==");
                continue;
            }

            cCusNew = new CCusdisdefVo();
            /* 属性赋值 */
            cusdisdefField(newCusValue, cCusNew, cCusdisdefVo);

            QueryWrapper<CCusdisdefVo> wrapper2 = new QueryWrapper<>();
            wrapper2.eq("C_CUSTOMER_ID", cCusdisdefVo.getCCustomerId());
            wrapper2.eq("M_DIM1_ID", cCusdisdefVo.getMDim1Id());
            booleanList.add(cCusdisdefMapper.update(cCusNew, wrapper2) > 0);
        }

        log.info("=== 经销商折扣数据清洗SUCCESS 折扣记录数：{}===", booleanList.size());
        return booleanList.stream().allMatch(e -> e.equals(true));
    }


    public static String generatorPropNum(String str) {
        if (GlobalCommonVariable.A.equals(str)) {
            return STR;
        }
        Integer letterSerial = getLetterSerial(str);
        if (letterSerial <= 3) {
            return String.valueOf(letterSerial - 1);
        }
        return String.valueOf(letterSerial - 2);
    }


    /**
     * 根据字符获取所在序号
     *
     * @return 结果集
     */
    public static Integer getLetterSerial(String str) {
        char ch = str.charAt(0);
        int x = ch;
        if (ch >= 'a' && ch <= 'z') {
            x = (ch - 'a' + 1);
        }
        if (ch >= 'A' && ch <= 'Z') {
            x = (ch - 'A' + 1);
        }
        return x;
    }


    /**
     * 动态分隔字符串，获取经销商编号
     * 经销商 5KA699 ->5KA ->A  / 道具 5K10699 ->5k10 ->10
     *
     * @param codeRes       参数字符串5KA699
     * @param generatorCode 城市编码699
     * @param brandRuleCode 分割规则5K
     * @param flag          true 表示直营  false表示经销
     * @return 截取的数据结果
     */
    public String generatorCodeIndex(String codeRes, String generatorCode, String brandRuleCode, Boolean flag) {
        if (org.apache.commons.lang3.StringUtils.isBlank(codeRes)) {
            return null;
        }

        String replace = codeRes.replace(generatorCode, "");
        if (org.apache.commons.lang3.StringUtils.isBlank(replace)) {
            log.error("=== 分割字符串失败：{} ==", codeRes);
            return null;
        }

        String newReplace = replace.replace(brandRuleCode, "");
        if (org.apache.commons.lang3.StringUtils.isBlank(newReplace)) {
            log.error("=== 分割字符串之后返回null，目前值newReplace：{}, 直营店是对的，如果是经销就是异常 Code：{} ==", Objects.equals(newReplace, "") ? "当前newReplace是空值" : newReplace, codeRes);
            if (flag == null) {
                log.error("=== 当前是创建经销商，直接忽略 ===");
                return null;
            }

            // flag true 表示直营店仓  false表示经销
            if (flag) {
                log.error("=== 当前是直营店仓，直营店数据正确，流程向下执行 newReplace：{} ===", newReplace);
            } else {
                log.error("=== 当前是经销店仓，数据错误请排查，流程终止 newReplace：{}===", newReplace);
            }
        }
        return newReplace;
    }

    /**
     * ID 递归根据序列获取ID
     *
     * @param id   主键编号
     * @param type 1 表示经销商 2表示店仓
     * @return 结果集
     */
    public Long generatorId(Long id, Integer type) {
        try {
            num.getAndIncrement();
            int currentIndex = num.get();
            if (currentIndex > GlobalCommonVariable.INDEX) {
                log.error("---递归generatorId超过{}次,直接抛出异常,避免CPU100%, 同时请检查数据库序列 ---", currentIndex);
                throw new RuntimeException("递归generatorId超过5 次: {},直接抛出异常,避免CPU100%, 同时请检查数据库序列");
            }

            if (type == 1) {
                QueryWrapper<CCustomerVo> wrapper = new QueryWrapper<>();
                wrapper.eq("ID", id);
                CCustomerVo customerById = cCustomerMapper.selectOne(wrapper);
                if (customerById == null) {
                    return id;
                }
                log.info("---经销递归一次: {}---", new Date());
                id = cCustomerMapper.getSequences("SEQ_C_CUSTOMER");
                generatorId(id, 1);
                return id;
            }

            QueryWrapper<CStoreVo> wrapper = new QueryWrapper<>();
            wrapper.eq("ID", id);
            CStoreVo cStoreVo = cStoreMapper.selectOne(wrapper);
            if (cStoreVo == null) {
                return id;
            }
            log.info("---店仓递归一次: {}---", new Date());
            id = cCustomerMapper.getSequences("SEQ_C_STORE");
            generatorId(id, 2);
            return id;

        } catch (Exception e) {
            log.error("=== 递归获取序列用于ID递增发生异常 ==== ", e);
        }
        return null;
    }

    public List<CAreaVo> getArea(String brandName) {
        QueryWrapper<CAreaVo> areaWrapper = new QueryWrapper<>();
        areaWrapper.eq("NAME", brandName);
        return cAreaMapper.selectList(areaWrapper);
    }


    public List<CAreaVo> getAreaById(String id) {
        QueryWrapper<CAreaVo> areaWrapper = new QueryWrapper<>();
        areaWrapper.eq("ID", id);
        return cAreaMapper.selectList(areaWrapper);
    }

    public CCustomerVo getCustomerData(String code) {
        QueryWrapper<CCustomerVo> cCustomerVoQueryWrapper = new QueryWrapper<>();
        cCustomerVoQueryWrapper.eq("CODE", code);
        return cCustomerMapper.selectOne(cCustomerVoQueryWrapper);
    }

    public List<CCusdisdefVo> getCCusdisdefByCustomerId(Long customerId) {
        QueryWrapper<CCusdisdefVo> wrapper1 = new QueryWrapper<>();
        wrapper1.eq("C_CUSTOMER_ID", customerId);
        return cCusdisdefMapper.selectList(wrapper1);
    }


    /**
     * 属性参数赋值
     *
     * @param newCusValue 对象
     * @param cCusNew     对象
     */
    public void cusdisdefField(CCusdisdefVo newCusValue, CCusdisdefVo cCusNew, CCusdisdefVo cCusdisdefVo) {
        // 是否允许现货买断
        cCusNew.setCanButsale(newCusValue.getCanButsale());
        // 是否允许现货非买断
        cCusNew.setCanNotbutsale(newCusValue.getCanNotbutsale());
        // 是否允许代销
        cCusNew.setCanAgtsale(newCusValue.getCanAgtsale());
        // 默认期货折扣
        cCusNew.setFirsaledis(newCusValue.getFirsaledis());
        // 默认现货折扣
        cCusNew.setSaledis(newCusValue.getSaledis());
        // 默认现货买断折扣
        cCusNew.setButsaledis(newCusValue.getButsaledis());
        // 默认代销折扣
        cCusNew.setAgtsaledis(newCusValue.getAgtsaledis());
        // 默认期货退货折扣
        cCusNew.setFirsaleretdis(newCusValue.getFirsaleretdis());
        // 默认现货退货折扣
        cCusNew.setSaleretdis(newCusValue.getSaleretdis());
        // 默认现货买断退货折扣
        cCusNew.setPtr4saleretdis(newCusValue.getPtr4saleretdis());
        // 默认代销退货折扣
        cCusNew.setAgtsaleretdis(newCusValue.getAgtsaleretdis());
        cCusNew.setModifieddate(new Date());
        cCusNew.setIsactive("Y");
    }
}
