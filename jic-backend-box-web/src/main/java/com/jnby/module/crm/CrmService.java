package com.jnby.module.crm;

import com.jnby.common.Page;
import com.jnby.dto.*;
import com.jnby.dto.oa.FormattableMain42Dto;
import com.jnby.infrastructure.bojun.model.CArcbrand;
import com.jnby.infrastructure.bojun.model.CAreaVo;
import com.jnby.infrastructure.bojun.model.CAreaVo1;

import java.util.List;

public interface CrmService {
    /**
     * 创建or编辑
     * @param requestData
     */
    void createOrUpdate(CrmCreateDto requestData);

    /**
     * 变更客户档案
     * @param requestData
     */
    void changeCrmCustomerMain(ChangeCrmCustomerMainDto requestData);

    /**
     * 变更下级公司
     * @param requestData
     */
    void changeCrmRelation(ChangeCrmRelationDto requestData);

    /**
     * 获取客户下绑定的经销商信息
     * @param crmCustomerMainId
     * @return
     */
    List<CrmRelationDto> getCrmRelation(String crmCustomerMainId);

    /**
     * 查询状态为  0 1 2 3  的
     * @return
     */
    List<CrmCreateDto> getCanBindingCustomerMain();

    /**
     * 列表信息  分页
     *
     * @param requestData
     * @param page
     * @param component
     * @param userId
     * @return
     */
    List<CrmCreateDto> list(CrmCustomerListReq requestData, Page page, String component, String userId);

    /**
     * 查询信息
     * @param requestData
     * @return
     */
    List<FindCustomerListDto> findCustomerByCodes(List<String> requestData,String path,String username);

    /**
     * 获取经销区域
     * @return
     */
    List<CAreaVo> getArea();

    /**
     * 查询最新的一条合同
     * @param requestData
     * @return
     */
    List<FormattableMain42Dto> findHtByCustomerId(List<String> requestData);


    List<FormattableMain42Dto> findConsSalesByCustomerIds(List<String> requestData);

    /**
     * 导入旧的数据
     * @param requestData
     */
    void importOldData(String requestData);

    ListCountStatusDto listCountStatus(ListCountReq requestData, String component, String userId);

    /**
     * 查询品牌信息
     * @return
     */
    List<CArcbrand> getBrandInfo();

    List<CAreaVo1> getArea1();

    /**
     * 查询店铺信息
     * @param requestData
     * @param page
     * @return
     */
    List<StoreCustomerResp> getStoreList(StoreCustomerReq requestData, Page page);

    public Long createCustomerBojun(String requestId);

    /**
     * 查询合作中的
     */
    void syncStopStatus();

    /**
     * 解约合同
     * @param requestId
     */
    void terminateContract(String requestId);

    /**
     * 根据uninid查询一条数据
     * @param requestData
     * @return
     */
    CrmCreateDto findByUnionid(String requestData);

    /**
     * 刷标签
     */
    void flushYearsLabel();

    /**
     * 地址信息
     * @param url
     */
    void importCrmFileChangeData(String url);

    /**
     * 获取OAtoken
     * @return
     */
    String getOaToken();

    String createOaFlow(CreateOaFlowDto requestData);

    /**
     * 更新数据
     * @param requestData
     */
    void updateRelation(List<UpdateCrmRelationDto> requestData);
}
