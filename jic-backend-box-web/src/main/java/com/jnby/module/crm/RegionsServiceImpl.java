package com.jnby.module.crm;

import com.jnby.infrastructure.bojun.mapper.CRegionsMapper;
import com.jnby.infrastructure.bojun.model.CRegions;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class RegionsServiceImpl implements IRegionsService {

    @Autowired
    private CRegionsMapper cRegionsMapper;


    @Override
    public List<CRegions> getProvinces() {
        // 获取所有省
        List<CRegions> provinces = cRegionsMapper.getProvinces();
        // 获取所有市
        List<CRegions> citys  = cRegionsMapper.getAllCity();
        // 获取所有区
        List<CRegions> districts  = cRegionsMapper.getAllDistricts();

        Map<Long, List<CRegions>> districtMap = districts.stream().collect(Collectors.groupingBy(r -> r.getPId()));
        for (CRegions city : citys) {
            city.setChildren(districtMap.get(city.getId()));
        }

        Map<Long, List<CRegions>> cityMap = citys.stream().collect(Collectors.groupingBy(r -> r.getPId()));
        for (CRegions province : provinces) {
            province.setChildren(cityMap.get(province.getId()));
        }
        return provinces;
    }

    @Override
    public List<CRegions> getCities(Long provinceId) {
        return cRegionsMapper.getCities(provinceId);
    }

    @Override
    public List<CRegions> getDistricts(Long cityId) {
        return cRegionsMapper.getDistricts(cityId);
    }


}
