package com.jnby.module.remote.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * {"event":"consume_integral","appKey":"**********","data":[{"brandId":"**********","time":1757552579000,"event":"consume_integral","eventKeyId":"*********","eventJson":"{\"wid\":null,\"cno\":\"G016750722\",\"cnoId\":\"********\",\"cardId\":\"********\",\"cardNo\":\"WX32165570\",\"brandId\":\"**********\",\"consumeId\":\"*********\",\"integralNo\":\"548900336405********090259\",\"billTime\":\"2025-09-11 09:02:59\",\"billDate\":\"********\",\"integral\":1500,\"reasonId\":\"10\",\"reason\":\"全域营销（点亮品牌得1500积分）\",\"description\":\"由活动产生积分!\",\"exchangeType\":null,\"vipTypeId\":\"65\",\"changeNo\":\"速写003\",\"vipTypeLvl\":0,\"vipTypeUpId\":55,\"intlOnceUp\":2500,\"monIntl\":6,\"intlUp\":3800,\"twodepart\":\"QD002\",\"p_coupon_gettime\":null,\"store_id\":\"412478\",\"storeId\":\"412478\",\"storeCode\":\"9KB44904\"}","type":"track","properties":{"wid":null,"cno":"G016750722","cnoId":"********","cardId":"********","cardNo":"WX32165570","brandId":"**********","consumeId":"*********","integralNo":"548900336405********090259","billTime":"2025-09-11 09:02:59","billDate":"********","integral":1500,"reasonId":"10","reason":"全域营销（点亮品牌得1500积分）","description":"由活动产生积分!","exchangeType":null,"vipTypeId":"65","changeNo":"速写003","vipTypeLvl":0,"vipTypeUpId":55,"intlOnceUp":2500,"monIntl":6,"intlUp":3800,"twodepart":"QD002","p_coupon_gettime":null,"store_id":"412478","storeId":"412478","storeCode":"9KB44904"},"account":{"wx_unionid":null,"mobile":null,"wx_openid":null,"external_user_id":null,"diy_id":{"wid":null,"cno":"G016750722","cnoId":"********","cardId":"********","cardNo":"WX32165570","brandId":"**********"}}}]}
 */
@Data
@Accessors(chain = true)
public class CdpEventReqEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "事件code", required = true)
    private String event;
    @ApiModelProperty(value = "上报空间appKey", required = true)
    private String appKey;
    @ApiModelProperty(value = "是否是历史事件,默认false", required = true)
    private Boolean isHistory = false;
    @ApiModelProperty(value = "事件数据")
    private List<CdpEventDataReqEntity> data;
}
