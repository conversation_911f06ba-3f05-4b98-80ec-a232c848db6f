package com.jnby.module.remote;


import com.jnby.module.remote.entity.CdpEventReqEntity;
import com.jnby.module.remote.entity.CdpPushReqEntity;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;


/**
 * cdp服务
 */
public interface ICdpRemoteHttpApi {

    /**
     * 数据上报
     */
    @POST("/cdp/cdp-center/cdp/data/push")
    Call<JicBaseResp> pushData(@Body CdpPushReqEntity req);


    /**
     * 实时时间上报
     */
    @POST("mq/cdp-mq/cdp/event/report/api")
    Call<JicBaseResp> pushEvent(@Body CdpEventReqEntity req);
}
