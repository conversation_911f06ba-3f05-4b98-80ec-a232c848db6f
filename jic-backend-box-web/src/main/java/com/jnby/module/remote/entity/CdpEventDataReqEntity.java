package com.jnby.module.remote.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class CdpEventDataReqEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    private String brandId;
    private Long time = System.currentTimeMillis();
    private String event;
    private String eventKeyId;
    private String type = "track";
    private Object properties;
    private Account account = new Account();

    @Data
    @Accessors(chain = true)
    public static class Account {
        private String wx_unionid;
        private String mobile;
        private String wx_openid;
        private String external_user_id;
        private CdpMemberAccountReqEntity diy_id;
    }

    @Data
    @Accessors(chain = true)
    public static class CdpMemberAccountReqEntity {
        private String wid;
        private String cno;
        private String cnoId;
        private String cardId;
        private String cardNo;
        private String brandId;
    }
}
