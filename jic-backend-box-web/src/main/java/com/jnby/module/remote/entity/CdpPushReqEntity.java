package com.jnby.module.remote.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Builder
@Data
public class CdpPushReqEntity {

    @ApiModelProperty(value = "表名。例如:box_customer_detail")
    private String entityKey;

    @ApiModelProperty(value = "中文解释。例如:BOX-注册表")
    private String entityName;

    @ApiModelProperty(value = "上报内容")
    private List<Map<String, Object>> contents;

}
