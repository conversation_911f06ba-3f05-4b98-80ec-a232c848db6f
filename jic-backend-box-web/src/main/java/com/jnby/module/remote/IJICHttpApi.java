package com.jnby.module.remote;

import com.jnby.module.remote.entity.JICCommonVouResponse;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;
import retrofit2.http.Path;
import retrofit2.http.Query;

import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/11/17 14:15
 */
public interface IJICHttpApi {


    @POST("/wechat/mini-center/ma/generateShortLink")
    Call<JICCommonVouResponse<String>> generateShortLink(@Query ("appid") String appid,@Body Map<String,Object> params);


    @POST("/wechat/mini-center/ma/generateLink")
    Call<JICCommonVouResponse<String>> generateLink(@Query ("appid") String appid,@Body Map<String,Object> params);

    @POST("/wechat/mini-center/weixin/qrcode/{appid}/unlimited/info")
    Call<JICCommonVouResponse<Map<String,String>>> generateMiniappCode(@Path("appid") String appid,@Body Map<String, Object> map);
}
