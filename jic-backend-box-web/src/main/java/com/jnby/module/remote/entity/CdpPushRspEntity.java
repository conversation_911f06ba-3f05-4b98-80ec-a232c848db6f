package com.jnby.module.remote.entity;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 成功案例:
 * {
 *     "successCount": 1,
 *     "failCount": 0,
 *     "failRecordList": [],
 *     "entityIdList": [
 *         "{}"
 *     ]
 * }
 * 失败案例:
 *
 * {
 *     "successCount": 0,
 *     "failCount": 1,
 *     "failRecordList": [
 *         {
 *             "rawData": {
 *                 "identity": null,
 *                 "entityKey": null,
 *                 "property": {
 *                     "createTime": "Jun 5, 2024 6:17:52 PM",
 *                     "sourceType": 1,
 *                     "keyId": "4179550753104445440",
 *                     "createFasId": "5f2105b18f57411e92a373d11429b167",
 *                     "type": 10,
 *                     "wx_unionid": "oZpUxs7E-g7VZkK5Qsb_QmKxwWUU",
 *                     "boxSn": "BX24060500000001",
 *                     "status": 6
 *                 },
 *                 "keyId": null,
 *                 "id": null
 *             },
 *             "cause": "创建时间字段数据类型不匹配,数据类型:日期时间"
 *         }
 *     ],
 *     "entityIdList": [
 *         "{}"
 *     ]
 * }
 */
@Builder
@Data
public class CdpPushRspEntity {
//    @ApiModelProperty(value = "请求ID")
//    private String requestId;
//
//    @ApiModelProperty("响应数据")
//    private String data;


    @ApiModelProperty(value = "成功行数")
    private Integer successCount;

    @ApiModelProperty(value = "失败行数")
    private Integer failCount;

    @ApiModelProperty(value = "失败具体错误信息")
    private List<FailObj> failRecordList;

    // 失败具体错误信息
    @Data
    public static class FailObj {
        @ApiModelProperty(value = "原始数据")
        private RawData rawData;
        @ApiModelProperty(value = "错误原因")
        private String cause;
    }

    // 单行记录
    @Data
    public static class RawData {
        @ApiModelProperty(value = "属性")
        private PropertyObj property;
    }

    // 属性值
    @Data
    public static class PropertyObj {
        @ApiModelProperty(value = "keyId")
        private String keyId;
    }
}
