package com.jnby.module.remote.entity;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date:2022/10/9 14:26
 */
@Data
public class JICCommonVouResponse<T> implements Serializable {
    /**
     * 状态码
     */
    @SerializedName("code")
    private String code;
    /**
     * 是否成功
     */
    @SerializedName("success")
    private boolean success;
    /**
     * 返回消息
     */
    @SerializedName("msg")
    private String msg;
    /**
     * 返回数据
     */
    @SerializedName("data")
    private T data;
}
