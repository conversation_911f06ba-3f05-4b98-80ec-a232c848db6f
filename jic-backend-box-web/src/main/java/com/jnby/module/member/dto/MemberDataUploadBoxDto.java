package com.jnby.module.member.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jnby.common.util.DateUtil;
import com.jnby.infrastructure.box.model.Box;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

import static com.jnby.common.util.DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS;

@ApiModel(value = "BOX-服务单表")
@Data
public class MemberDataUploadBoxDto implements Serializable {

    public static String ENTITY_KEY = "box_service";
    public static String ENTITY_NAME = "BOX-服务单表";

    @ApiModelProperty(value = "主键ID")
    private String keyId;
    @ApiModelProperty(value = "微信unionId")
    private String wx_unionid;
    @ApiModelProperty(value = "服务单编号")
    private String boxSn;
    @ApiModelProperty(value = "搭配师ID")
    private String createFasId;
    @ApiModelProperty(value = "主动要盒ID")
    private String askId;
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String createTime_;
    @ApiModelProperty(value = "服务单类型 10:搭配师服务单 20:导购服务单 30:有搭服务单 40:主题盒子服务单 50:先试后买")
    private Integer type;
    @ApiModelProperty(value = "来源类型： 0免费盒子  1订阅主动要盒 2单次主动要盒")
    private Integer sourceType;
    @ApiModelProperty(value = "状态(-1:待提交;0:未发货;1:已发货;2:已签收;3:已完成未入库;4:已评价,5.已完成已入库 6:已取消,7:系统作废,8:被合单后) 9:待还货 10：换货中")
    private Integer status;
    @ApiModelProperty(value = "集团卡号")
    private String cno;


    public MemberDataUploadBoxDto toDto(Box db, String cno, String askId) {
        if (db == null) {
            return null;
        }
        this.keyId = db.getId();
        this.wx_unionid = db.getUnionid();
        this.boxSn = db.getBoxSn();
        this.createFasId = db.getCreateFasId();
        this.askId = StringUtils.isNotBlank(db.getAskId()) ? db.getAskId() : askId;
        if (db.getCreateTime() != null) {
            this.createTime_ = DateUtil.parseDate(db.getCreateTime(), DATEFORMATE_YYYY_MM_DD_HHMMSS);
        }
        if (db.getType() != null) {
            this.type = db.getType().intValue();
        }
        if (db.getSourceType() != null) {
            this.sourceType = db.getSourceType().intValue();
        }
        if (db.getStatus() != null) {
            this.status = db.getStatus().intValue();
        }
        this.cno = cno;
        return this;
    }
}
