package com.jnby.module.member.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class BSubscribeInfoBO {
    @ApiModelProperty(value = "搭配师ID")
    private String fashionerId;
    @ApiModelProperty(value = "邀约人hr_emp_id")
    private String invitePeople;
    @ApiModelProperty(value = "邀约人门店ID")
    private String storeId;
    @ApiModelProperty(value = "邀约人品牌名称")
    private String invitePeopleBrand;

}
