package com.jnby.module.member.dto;

import com.jnby.common.util.DateUtil;
import com.jnby.infrastructure.box.model.CustomerDetails;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import static com.jnby.common.util.DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS;

@ApiModel(value = "BOX-注册表")
@Data
public class MemberDataUploadCustomerDetailsDto {

    public static String ENTITY_KEY = "box_customer_detail";
    public static String ENTITY_NAME = "BOX-注册表";

    @ApiModelProperty(value = "主键ID")
    private String keyId;
    @ApiModelProperty(value = "微信unionId")
    private String wx_unionid;
    @ApiModelProperty(value = "绑定的搭配师")
    private String fashionerId;
    @ApiModelProperty(value = "BOX注册时间")
    private String boxCreateTime;
    @ApiModelProperty(value = "集团卡号")
    private String cno;
    @ApiModelProperty(value = "不止盒子小程序openId")
    private String wx_applet_openid;
    @ApiModelProperty(value = "不止盒子公众号openId")
    private String wx_openid;
    @ApiModelProperty(value = "用户类型:701010：恶意试衣；70：客户违约\t；7910：花名册拉黑\t；56：正常用户; 57:黑名单未分类")
    private String categoryId;

    public MemberDataUploadCustomerDetailsDto toDto(CustomerDetails db, String wxOpenId, String mpOpenId, String categoryId) {
        if (db == null) {
            return null;
        }
        this.keyId = db.getId();
        this.wx_unionid = db.getUnionid();
        this.fashionerId = db.getFashionerId();
        if (db.getCreateTime() != null) {
            this.boxCreateTime = DateUtil.parseDate(db.getCreateTime(), DATEFORMATE_YYYY_MM_DD_HHMMSS);
        }
        this.cno = db.getJnbyCardNo();
        this.wx_applet_openid = wxOpenId;
        this.wx_openid = mpOpenId;
        this.categoryId = categoryId;
        return this;
    }
}
