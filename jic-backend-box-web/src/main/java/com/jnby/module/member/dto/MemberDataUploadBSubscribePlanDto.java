package com.jnby.module.member.dto;

import com.jnby.common.util.DateUtil;
import com.jnby.infrastructure.box.model.BSubscribePlan;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import static com.jnby.common.util.DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS;

@ApiModel(value = "BOX-订阅计划表")
@Data
public class MemberDataUploadBSubscribePlanDto {

    public static String ENTITY_KEY = "b_subscribe_plan";
    public static String ENTITY_NAME = "BOX-订阅计划表";

    @ApiModelProperty(value = "主键ID")
    private String keyId;
    @ApiModelProperty(value = "要盒月份")
    private Long plan_mouth_str;
    @ApiModelProperty(value = "集团卡号")
    private String CARD_NO;
    @ApiModelProperty(value = "状态（0待要盒  1要盒未搭盒  2已搭盒  3已完成  4已失效）")
    private Long STATUS;
    @ApiModelProperty(value = "计划要盒月份")
    private String PLAN_MONTH;
    @ApiModelProperty(value = "订阅ID")
    private String SUB_ID;
    @ApiModelProperty(value = "微信标识")
    private String UNIONID;

    public MemberDataUploadBSubscribePlanDto toDto(BSubscribePlan db, String cno) {
        if (db == null) {
            return null;
        }
        this.keyId = db.getId();
        this.CARD_NO = cno;
        if (db.getPlanMonth() != null) {
            this.PLAN_MONTH = DateUtil.parseDate(db.getPlanMonth(), DATEFORMATE_YYYY_MM_DD_HHMMSS);
        }
        this.plan_mouth_str = db.getPlanMonthStr();
        this.STATUS = db.getStatus();
        this.SUB_ID = db.getSubId();
        this.UNIONID = db.getUnionid();
        return this;
    }
}
