package com.jnby.module.member.dto;

import com.jnby.common.util.DateUtil;
import com.jnby.infrastructure.box.model.BSubscribeInfo;
import com.jnby.module.member.bo.BSubscribeInfoBO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import static com.jnby.common.util.DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS;

@ApiModel(value = "BOX-订阅表")
@Data
public class MemberDataUploadBSubscribeInfoDto {

    public static String ENTITY_KEY = "B_SUBSCRIBE_INFO";
    public static String ENTITY_NAME = "BOX-订阅表";

    @ApiModelProperty(value = "主键ID")
    private String keyId;
    @ApiModelProperty(value = "微信unionId")
    private String wx_unionid;
    @ApiModelProperty(value = "用户ID")
    private String custId;
    @ApiModelProperty(value = "卡类型（1正式卡 2单次卡 3免费卡）")
    private Long cardType;
    @ApiModelProperty(value = "1订阅中 2已结束 3已退订")
    private Long status;
    @ApiModelProperty(value = "删除（0否 1是）")
    private Long delFlag;
    @ApiModelProperty(value = "开始时间")
    private String startTime;
    @ApiModelProperty(value = "结束时间")
    private String endTime;
    @ApiModelProperty(value = "集团卡号")
    private String cno;
    @ApiModelProperty(value = "邀约人hr_emp_id")
    private String invitePeople;
    @ApiModelProperty(value = "邀约人品牌名称")
    private String invitePeopleBrand;

    public MemberDataUploadBSubscribeInfoDto toDto(BSubscribeInfo db, String cno, BSubscribeInfoBO bo) {
        if (db == null) {
            return null;
        }
        this.keyId = db.getId();
        this.wx_unionid = db.getUnionid();
        this.custId = db.getCustId();
        this.cardType = db.getCardType();
        this.status = db.getStatus();
        this.delFlag = db.getDelFlag();
        if (db.getStartTime() != null) {
            this.startTime = DateUtil.parseDate(db.getStartTime(), DATEFORMATE_YYYY_MM_DD_HHMMSS);
        }
        if (db.getEndTime() != null) {
            this.endTime = DateUtil.parseDate(db.getEndTime(), DATEFORMATE_YYYY_MM_DD_HHMMSS);
        }
        this.cno = cno;
        if (bo != null) {
            this.invitePeople = bo.getInvitePeople();
            this.invitePeopleBrand = bo.getInvitePeopleBrand();
        }
        return this;
    }
}
