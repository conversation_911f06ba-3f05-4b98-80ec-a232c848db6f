package com.jnby.module.member.dto;

import com.jnby.common.util.DateUtil;
import com.jnby.infrastructure.box.model.Order;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import static com.jnby.common.util.DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS;

@ApiModel(value = "BOX-交易订单表")
@Data
public class MemberDataUploadOrderDto {

    public static String ENTITY_KEY = "box_order__";
    public static String ENTITY_NAME = "BOX-交易订单表";

    @ApiModelProperty(value = "主键ID")
    private String keyId;
    @ApiModelProperty(value = "微信unionId")
    private String wx_unionid;
    @ApiModelProperty(value = "订单编号")
    private String orderSn;
    @ApiModelProperty(value = "服务单号")
    private String boxSn;
    @ApiModelProperty(value = "用户ID")
    private String customerId;
    @ApiModelProperty(value = "支付金额")
    private Double paidAmount;
    @ApiModelProperty(value = "商品总金额")
    private Double productTotalPrice;
    @ApiModelProperty(value = "优惠券金额")
    private Double discountAmount;
    @ApiModelProperty(value = "商品优惠金额")
    private Double productDiscount;
    @ApiModelProperty(value = "会员优惠金额")
    private Double vipDiscount;
    @ApiModelProperty(value = "创建时间")
    private String createTime_;
    @ApiModelProperty(value = "订单类型 10:搭配师订单 20:导购订单 30:有搭订单 40:主题盒子订单")
    private Long orderType;
    @ApiModelProperty(value = "订单状态:0-未支付,1-已支付2-已发货,3-已完成,4-已取消,5-已退款,6 已签收,7 部分发货'")
    private Long orderStatus;
    @ApiModelProperty(value = "集团卡号")
    private String cno;

    public MemberDataUploadOrderDto toDto(Order db, String unionId, String cno) {
        if (db == null || unionId == null) {
            return null;
        }
        this.keyId = db.getId();
        this.wx_unionid = unionId;
        this.orderSn = db.getOrderSn();
        this.boxSn = db.getBoxSn();
        this.customerId = db.getCustomerId();
        if (db.getPaidAmount() != null) {
            this.paidAmount = db.getPaidAmount();
        } else {
            this.paidAmount = 0d;
        }
        if (db.getProductTotalPrice() != null) {
            this.productTotalPrice = db.getProductTotalPrice();
        } else {
            this.productTotalPrice = 0d;
        }
        if (db.getDiscountAmount() != null) {
            this.discountAmount = db.getDiscountAmount();
        } else {
            this.discountAmount = 0d;
        }
        if (db.getProductDiscount() != null) {
            this.productDiscount = db.getProductDiscount();
        } else {
            this.productDiscount = 0d;
        }
        if (db.getVipDiscount() != null) {
            this.vipDiscount = db.getVipDiscount();
        } else {
            this.vipDiscount = 0d;
        }
        if (db.getCreateTime() != null) {
            this.createTime_ = DateUtil.parseDate(db.getCreateTime(), DATEFORMATE_YYYY_MM_DD_HHMMSS);
        }
        this.orderType = db.getType();
        this.orderStatus = db.getOrderStatus();
        this.cno = cno;
        return this;
    }
}
