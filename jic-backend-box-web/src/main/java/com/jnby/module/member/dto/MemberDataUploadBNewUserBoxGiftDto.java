package com.jnby.module.member.dto;

import com.jnby.common.util.DateUtil;
import com.jnby.dto.marketing.BNewUserBoxGiftDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import static com.jnby.common.util.DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS;

@ApiModel(value = "BOX-收盒礼表")
@Data
public class MemberDataUploadBNewUserBoxGiftDto {

    public static String ENTITY_KEY = "box_b_new_user_box_gift";
    public static String ENTITY_NAME = "BOX-收盒礼表";

    @ApiModelProperty(value = "主键ID")
    private String keyId;
    @ApiModelProperty(value = "用户unionId")
    private String unionid;
    @ApiModelProperty(value = "微信unionId")
    private String wx_unionid;
    @ApiModelProperty(value = "集团卡号")
    private String cno;
    @ApiModelProperty(value = "第几个盒子发送快递：1、3、6")
    private Integer send_num;
    @ApiModelProperty(value = "商品sku")
    private String sku;
    @ApiModelProperty(value = "礼物名称")
    private String box_gift_name;
    @ApiModelProperty(value = "收盒礼的使用状态: 1=正常，0=禁用，2=暂停, 3=已过期，4=已用完")
    private Integer status;
    @ApiModelProperty(value = "创建时间")
    private String create_time;
    @ApiModelProperty(value = "修改时间")
    private String update_time;
    @ApiModelProperty(value = "是否删除:0=否，1=是")
    private Long is_del;
    @ApiModelProperty(value = "订阅id")
    private String sub_id;

    public MemberDataUploadBNewUserBoxGiftDto toDto(BNewUserBoxGiftDTO db, String cno) {
        if (db == null) {
            return null;
        }
        this.keyId = db.getId();
        this.unionid = db.getUnionid();
        this.wx_unionid = db.getUnionid();
        this.cno = cno;
        this.send_num = db.getSendNum();
        this.sku = db.getSku();
        this.box_gift_name = db.getBoxGiftName();
        this.status = db.getStatus();
        if (db.getCreateTime() != null) {
            this.create_time = DateUtil.parseDate(db.getCreateTime(), DATEFORMATE_YYYY_MM_DD_HHMMSS);
        }
        if (db.getUpdateTime() != null) {
            this.update_time = DateUtil.parseDate(db.getUpdateTime(), DATEFORMATE_YYYY_MM_DD_HHMMSS);
        }
        this.is_del = db.getIsDel();
        this.sub_id = db.getSubId();
        return this;
    }
}
