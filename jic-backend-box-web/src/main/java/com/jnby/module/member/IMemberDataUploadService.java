package com.jnby.module.member;

import com.jnby.dto.JobParamDto;

import java.util.Date;

public interface IMemberDataUploadService {

    /**
     * 存量上传
     *
     * @param fromDate 时间范围左区间
     * @param toDate   时间范围右区间
     * @param unionId  微信唯一id
     * @param phone    手机号查询
     */
//    void fullUpload(Date fromDate, Date toDate, String unionId, String phone);

    /**
     * 增量上传
     *
     * @param fromDate    时间范围左区间
     * @param toDate      时间范围右区间
     * @param jobParamDto
     */
    void incrementalUpload(Date fromDate, Date toDate, JobParamDto jobParamDto);
}
