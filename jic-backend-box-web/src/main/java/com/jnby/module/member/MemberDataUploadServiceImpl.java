package com.jnby.module.member;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jnby.common.util.DateUtil;
import com.jnby.config.CommonProperties;
import com.jnby.dto.JobParamDto;
import com.jnby.dto.bojun.CstoreBrandDto;
import com.jnby.dto.marketing.BNewUserBoxGiftDTO;
import com.jnby.infrastructure.bojun.mapper.CStoreMapper;
import com.jnby.infrastructure.box.mapper.*;
import com.jnby.infrastructure.box.model.*;
import com.jnby.infrastructure.marketing.mapper.BNewUserBoxGiftMapper;
import com.jnby.module.member.bo.BSubscribeInfoBO;
import com.jnby.module.member.dto.*;
import com.jnby.module.remote.ICdpRemoteHttpApi;
import com.jnby.module.remote.JicBaseResp;
import com.jnby.module.remote.entity.CdpEventDataReqEntity;
import com.jnby.module.remote.entity.CdpEventReqEntity;
import com.jnby.module.remote.entity.CdpPushReqEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MemberDataUploadServiceImpl implements IMemberDataUploadService {
    // 初始化一个4个核心线程的线程池
    public static final AtomicInteger ATOMIC_INTEGER = new AtomicInteger();
    private static final ThreadPoolExecutor MY_THREAD_POOL = new ThreadPoolExecutor(
            8,
            8,
            60,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1024),
            r -> {
                Thread thread = new Thread(r);
                thread.setName("MemberDataUploadServiceImpl-thread-" + ATOMIC_INTEGER.addAndGet(1));
                return thread;
            },
            new ThreadPoolExecutor.CallerRunsPolicy());

    @Autowired
    private CustomerDetailsMapper customerDetailsMapper;

    @Autowired
    private BSubscribeInfoMapper bSubscribeInfoMapper;

    @Autowired
    private BSubscribePlanMapper bSubscribePlanMapper;

    @Autowired
    private CustomerAskBoxMapper customerAskBoxMapper;

    @Autowired
    private CardmainMapper cardmainMapper;

    @Autowired
    private FashionerMapper fashionerMapper;

    @Autowired
    private BoxMapper boxMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Resource
    private ICdpRemoteHttpApi iCdpRemoteHttpApi;

    @Autowired
    private WeiXinFansMapper weiXinFansMapper;

    @Autowired
    private BCustomerInformationMapper bCustomerInformationMapper;

    @Autowired
    private CommonProperties commonProperties;

    @Autowired
    private CStoreMapper storeMapper;

    @Autowired
    private BUserPointAccountMapper bUserPointAccountMapper;

    @Autowired
    private BNewUserBoxGiftMapper bNewUserBoxGiftMapper;
    @Resource
    private BPointDetailMapper bPointDetailMapper;

    // 全量上传分页
    private static final Integer FULL_PAGE_SIZE = 100;
    // 增量上传分页
    private static final Integer INCREMENT_PAGE_SIZE = 100;

    private void partitionUpload(List<Map<String, Object>> mapList, String entityKey, String entityName) {
        if (CollectionUtils.isEmpty(mapList)) {
            return;
        }
        // 分组上报，每组100个记录
        List<List<Map<String, Object>>> list = Lists.partition(mapList, FULL_PAGE_SIZE);
        for (List<Map<String, Object>> partitionList : list) {
            CdpPushReqEntity req = CdpPushReqEntity.builder().entityKey(entityKey).entityName(entityName).contents(partitionList).build();
            log.info("CDP远程上报数据,请求参数：{}", JSONObject.toJSONString(req));
            try {
                Response<JicBaseResp> result = iCdpRemoteHttpApi.pushData(req).execute();
                if (result.isSuccessful()) {
                    JicBaseResp<String> jicBaseResp = result.body();
                    if (jicBaseResp != null && jicBaseResp.isSuccess()) {
                        log.info("CDP远程上报数据,jicBaseResp：{}", JSONObject.toJSONString(jicBaseResp));
                        JSONObject rspJsonObject = JSONObject.parseObject(jicBaseResp.getData());
                        if (rspJsonObject != null) {
                            JSONObject dataJsonObject = (JSONObject) rspJsonObject.get("data");
                            if (dataJsonObject != null) {
                                Integer failCount = dataJsonObject.getInteger("failCount");
                                if (failCount != null && failCount > 0) {
                                    List<JSONObject> failRecordList = (List<JSONObject>) dataJsonObject.get("failRecordList");
                                    log.error("CDP远程上报参数异常: [{}]", failRecordList.get(0).getString("cause"));
                                }
                            }
                        }
                    } else {
                        log.error("CDP远程上报数据业务异常，jicBaseResp：{}", JSONObject.toJSONString(jicBaseResp));
                    }
                }
            } catch (Exception e) {
                log.error("CDP远程上报数据调用异常", e);
            }
        }
    }

    private void eventUpload(List<Map<String, Object>> mapList, String entityKey) {
        if (CollectionUtils.isEmpty(mapList)) {
            return;
        }
        List<CdpEventDataReqEntity> dataList = new ArrayList<>(mapList.size());
        for (Map<String, Object> map : mapList) {
            CdpEventDataReqEntity.CdpMemberAccountReqEntity diyAccountEntity = new CdpEventDataReqEntity.CdpMemberAccountReqEntity().setCno(map.get("cno").toString());
            CdpEventDataReqEntity.Account accountEntity = new CdpEventDataReqEntity.Account().setDiy_id(diyAccountEntity).setWx_unionid(map.get("wx_unionid").toString());
            CdpEventDataReqEntity dataEntity = new CdpEventDataReqEntity().setBrandId("0").setAccount(accountEntity).setEventKeyId(map.get("keyId").toString()).setEvent(entityKey).setTime(System.currentTimeMillis()).setProperties(map);
            dataList.add(dataEntity);
        }
        CdpEventReqEntity req = new CdpEventReqEntity().setEvent(entityKey).setAppKey("0SERVE06LOCLZRNKKO").setData(Lists.newArrayList(dataList));
        log.info("CDP远程上报事件数据 请求参数：{}", JSONObject.toJSONString(req));
        try {
            Response<JicBaseResp> result = iCdpRemoteHttpApi.pushEvent(req).execute();
            if (!result.isSuccessful()) {
                throw new RuntimeException("CDP远程上报事件数据 接口调用出错");
            }
            JicBaseResp<Boolean> jicBaseResp = result.body();
            log.info("CDP远程上报事件数据 jicBaseResp：{}", JSONObject.toJSONString(jicBaseResp));
            if (jicBaseResp == null || !jicBaseResp.isSuccess()) {
                throw new RuntimeException("CDP远程上报事件数据 接口调用出错");
            }
        } catch (Exception e) {
            log.error("CDP远程上报事件数据 接口调用异常", e);
        }
    }

    private void assemblyOrderList(List<Order> orderList, List<Map<String, Object>> mapList) {
        // 需要反查unionId，所以需要查询一次box
        List<CustomerDetails> customerDetailsList = customerDetailsMapper.batchSelectByPrimaryKey(orderList.stream().map(Order::getCustomerId).distinct().collect(Collectors.toList()));
        Map<String, String> custId2UnionIdMap = Optional.ofNullable(customerDetailsList).orElse(Lists.newArrayList()).stream()
                .filter(customerDetails -> StringUtils.isNotBlank(customerDetails.getUnionid()))
                .collect(Collectors.toMap(CustomerDetails::getId, CustomerDetails::getUnionid));

        Map<String, String> unoinId2CnoMap = Optional.ofNullable(customerDetailsList).orElse(Lists.newArrayList()).stream()
                .filter(customerDetails -> StringUtils.isNotBlank(customerDetails.getJnbyCardNo()))
                .collect(Collectors.toMap(CustomerDetails::getUnionid, CustomerDetails::getJnbyCardNo));

        Optional.ofNullable(orderList).orElse(Lists.newArrayList()).forEach(order -> {
            String myUnionId  = custId2UnionIdMap.get(order.getCustomerId());
            // 如果微信id不存在，则不处理
            if (StringUtils.isBlank(myUnionId)) {
                return;
            }
            MemberDataUploadOrderDto orderDTO = new MemberDataUploadOrderDto().toDto(order, myUnionId, unoinId2CnoMap.get(myUnionId));
            Map<String, Object> orderMap = BeanUtil.beanToMap(orderDTO);
            mapList.add(orderMap);
        });
    }

    private void assemblyBoxList(List<Box> boxList, List<Map<String, Object>> mapList) {
        List<String> ids = Optional.ofNullable(boxList).orElse(Lists.newArrayList()).stream()
                .map(Box::getUnionid).distinct().collect(Collectors.toList());
        if (ids.isEmpty()) {
            return;
        }
        Map<String, String> unoinId2CnoMap = getUnoinId2CnoMap(ids);

        // box反查customer表的ask_id
        List<String> boxIds = Optional.ofNullable(boxList).orElse(Lists.newArrayList()).stream()
                .map(Box::getId).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<CustomerAskBox> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CustomerAskBox::getBoxId, boxIds);
        Map<String, String> boxId2CabIdMap = Optional.ofNullable(customerAskBoxMapper.selectList(queryWrapper)).orElse(Lists.newArrayList()).stream()
                .filter(customerAskBox -> StringUtils.isNotBlank(customerAskBox.getId()))
                .collect(Collectors.toMap(CustomerAskBox::getBoxId, CustomerAskBox::getId, (k1, k2) -> k1));

        Optional.ofNullable(boxList).orElse(Lists.newArrayList()).forEach(box -> {
            MemberDataUploadBoxDto boxDTO = new MemberDataUploadBoxDto().toDto(box, unoinId2CnoMap.get(box.getUnionid()), boxId2CabIdMap.get(box.getId()));
            Map<String, Object> boxMap = BeanUtil.beanToMap(boxDTO);
            mapList.add(boxMap);
        });
    }

    private void assemblyCustomerAskBoxList(List<CustomerAskBox> customerAskBoxList, List<Map<String, Object>> mapList) {
        List<String> ids = Optional.ofNullable(customerAskBoxList).orElse(Lists.newArrayList()).stream()
                .map(CustomerAskBox::getUnionid).distinct().collect(Collectors.toList());
        if (ids.isEmpty()) {
            return;
        }
        Map<String, String> unoinId2CnoMap = getUnoinId2CnoMap(ids);

        Optional.ofNullable(customerAskBoxList).orElse(Lists.newArrayList()).forEach(customerAskBox -> {
            MemberDataUploadCustomerAskBoxDto customerAskBoxDTO = new MemberDataUploadCustomerAskBoxDto()
                    .toDto(customerAskBox, unoinId2CnoMap.get(customerAskBox.getUnionid()));
            Map<String, Object> customerAskBoxMap = BeanUtil.beanToMap(customerAskBoxDTO);
            mapList.add(customerAskBoxMap);
        });
    }

    private void assemblySubscribeInfoList(List<BSubscribeInfo> bSubscribeInfos, List<Map<String, Object>> mapList) {
        List<String> ids = Optional.ofNullable(bSubscribeInfos).orElse(Lists.newArrayList()).stream()
                .map(BSubscribeInfo::getUnionid).distinct().collect(Collectors.toList());
        if (ids.isEmpty()) {
            return;
        }
        // 获取集团卡号
        Map<String, String> unoinId2CnoMap = getUnoinId2CnoMap(ids);
        List<String> invitePeopleIds = Optional.ofNullable(bSubscribeInfos).orElse(Lists.newArrayList()).stream()
                .map(BSubscribeInfo::getInvitePeople).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        // 获取导购hr_emp_id
        Map<String, BSubscribeInfoBO> invitePeopleId2HrEmpIdMap = getInvitePeopleId2HrEmpIdMap(invitePeopleIds);

        Optional.ofNullable(bSubscribeInfos).orElse(Lists.newArrayList()).forEach(bSubscribeInfo -> {
            String invitePeopleId = bSubscribeInfo.getInvitePeople();
            BSubscribeInfoBO bo = null;
            if (StringUtils.isNotBlank(invitePeopleId)) {
                bo = invitePeopleId2HrEmpIdMap.get(invitePeopleId);
            }
            MemberDataUploadBSubscribeInfoDto subscribeDTO = new MemberDataUploadBSubscribeInfoDto()
                    .toDto(bSubscribeInfo, unoinId2CnoMap.get(bSubscribeInfo.getUnionid()), bo);
            Map<String, Object> subscribeMap = BeanUtil.beanToMap(subscribeDTO);
            mapList.add(subscribeMap);
        });
    }

    private void assemblySubscribePlanList(List<BSubscribePlan> bSubscribePlans, List<Map<String, Object>> mapList) {
        List<String> ids = Optional.ofNullable(bSubscribePlans).orElse(Lists.newArrayList()).stream()
                .map(BSubscribePlan::getUnionid).distinct().collect(Collectors.toList());
        if (ids.isEmpty()) {
            return;
        }
        Map<String, String> unoinId2CnoMap = getUnoinId2CnoMap(ids);

        Optional.ofNullable(bSubscribePlans).orElse(Lists.newArrayList()).forEach(bSubscribePlan -> {
            MemberDataUploadBSubscribePlanDto subscribeDTO = new MemberDataUploadBSubscribePlanDto()
                    .toDto(bSubscribePlan, unoinId2CnoMap.get(bSubscribePlan.getUnionid()));
            Map<String, Object> subscribeMap = BeanUtil.beanToMap(subscribeDTO);
            mapList.add(subscribeMap);
        });
    }

    //    private Map<String, String> getUnoinId2CnoMap(List<String> ids) {
//        LambdaQueryWrapper<CustomerDetails> queryWrapper = new LambdaQueryWrapper<CustomerDetails>()
//                .select(CustomerDetails::getUnionid, CustomerDetails::getJnbyCardNo)
//                .in(CustomerDetails::getUnionid, ids);
//        Map<String, String> unoinId2CnoMap = Optional.ofNullable(customerDetailsMapper.selectList(queryWrapper)).orElse(Lists.newArrayList()).stream()
//                .filter(customerDetails -> StringUtils.isNotBlank(customerDetails.getJnbyCardNo()))
//                .collect(Collectors.toMap(CustomerDetails::getUnionid, CustomerDetails::getJnbyCardNo, (k1, k2) -> k1));
//        return unoinId2CnoMap;
//    }
    private Map<String, String> getUnoinId2CnoMap(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }
        Map<String, String> unoinId2CnoMap = Optional.ofNullable(cardmainMapper.listCardmainByUnionId(ids)).orElse(Lists.newArrayList()).stream()
                .filter(Cardmain -> StringUtils.isNotBlank(Cardmain.getRemark()))
                .collect(Collectors.toMap(Cardmain::getUnionid, Cardmain::getRemark, (k1, k2) -> k1));
        log.info("集团卡号获取: {}", JSONObject.toJSONString(unoinId2CnoMap));
        return unoinId2CnoMap;
    }

    private Map<String, BSubscribeInfoBO> getInvitePeopleId2HrEmpIdMap(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            log.info("邀请人id为空");
            return new HashMap<>();
        }
        LambdaQueryWrapper<Fashioner> queryWrapper = new LambdaQueryWrapper<Fashioner>()
                .select(Fashioner::getId, Fashioner::getHrEmpId, Fashioner::getcStoreId)
                .in(Fashioner::getId, ids);

        List<BSubscribeInfoBO> hrEmpAndStoreIdList = Optional.ofNullable(fashionerMapper.selectList(queryWrapper)).orElse(Lists.newArrayList()).stream()
                .filter(f -> (StringUtils.isNotBlank(f.getHrEmpId()) && StringUtils.isNotBlank(f.getcStoreId())))
                .map(f -> BSubscribeInfoBO.builder().fashionerId(f.getId()).invitePeople(f.getHrEmpId()).storeId(f.getcStoreId()).build())
                .collect(Collectors.toList());
        log.info("邀请人id和门店获取: {}", JSONObject.toJSONString(hrEmpAndStoreIdList));
        // 取出门店ID，查询品牌
        List<Long> storeId = hrEmpAndStoreIdList.stream().map(bo -> bo.getStoreId())
                .filter(StringUtils::isNotBlank).distinct().map(Long::valueOf).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(storeId)) {
            log.info("邀请人门店获取为空");
            return new HashMap<>();
        }
        // 获取全部门店
        List<CstoreBrandDto> dbStoreBrandList = storeMapper.listStoreByStoreId(storeId);
        log.info("转换前全部门店: {}", JSONObject.toJSONString(dbStoreBrandList));

        /**
         * 判断1-当前邀约人的店铺勾选了奥莱？
         * 判断1-是，则品牌为奥莱。
         * 判断1-否，店铺品牌架构是江南布衣+,且是集合店？
         * 	  判断2-是，则品牌未江南布衣+。
         * 	  判断2-否，店铺是否集合店？
         * 		  判断3-是，按品牌顺序上传：'JNBY','LESS','CROQUIS','蓬马','童装','JNBYHOME'。
         * 		  判断3-否，当前门店所属品牌。
         */
        // 迭代处理门店，将集合店的品牌名称处理成随机门店
        dbStoreBrandList.forEach(
                next -> {
                    if (Objects.equals(next.getOutlets(), "是")) {
                        log.info("当前门店[{}]是奥莱，替换成奥莱", next.getStoreName());
                        next.setBrandName("奥莱");
                        return;
                    }
                    if (Objects.equals(next.getBrandName(), "江南布衣+")) {
                        log.info("当前门店[{}]是江南布衣+，替换成江南布衣+", next.getStoreName());
                        next.setBrandName("江南布衣+");
                        return;
                    }
                    if (StringUtils.isNotBlank(next.getUnionStoreId()) && Objects.equals(next.getStoreId(), next.getUnionStoreId())) {
                        List<CstoreBrandDto> unionStoreList = storeMapper.listStoreAndBrandNameByUnionStoreId(Long.valueOf(next.getUnionStoreId()));
                        log.info("当前门店[{}]是集合店，查询信息: {}", next.getStoreName(), JSONObject.toJSONString(unionStoreList));
                        if (CollectionUtils.isNotEmpty(unionStoreList) && Objects.nonNull(unionStoreList.get(0))) {
                            CstoreBrandDto cstoreBrandDto = unionStoreList.get(0);
                            next.setBrandName(cstoreBrandDto.getBrandName());
                            log.info("替换品牌名称: {}", cstoreBrandDto.getBrandName());
                        }
                    }
                }
        );
        log.info("转换后全部门店: {}", JSONObject.toJSONString(dbStoreBrandList));
        // 将 dbStoreBrandList 转换成 <storeId, brandName> 的map
        Map<String, String> storeId2BrandNameMap = dbStoreBrandList.stream()
                .filter(cstoreBrandDto -> StringUtils.isNotBlank(cstoreBrandDto.getStoreId()))
                .filter(cstoreBrandDto -> StringUtils.isNotBlank(cstoreBrandDto.getBrandName()))
                .collect(Collectors.toMap(CstoreBrandDto::getStoreId, CstoreBrandDto::getBrandName, (k1, k2) -> k1));

        // 将 hrEmpAndStoreIdList 转换成 <Fashioner::getId, BSubscribeInfoBO> 的map，并且需要根据 BSubscribeInfoBO.storeId 从 storeId2BrandNameMap中取 brandName
        Map<String, BSubscribeInfoBO> resultMap = hrEmpAndStoreIdList.stream()
                .filter(bo -> StringUtils.isNotBlank(bo.getStoreId()))
                .collect(Collectors.toMap(BSubscribeInfoBO::getFashionerId, bo -> {
                    bo.setInvitePeopleBrand(storeId2BrandNameMap.get(bo.getStoreId()));
                    return bo;
                }));
        log.info("导购id和品牌获取结果: {}", JSONObject.toJSONString(resultMap));
        return resultMap;
    }

    @Override
    public void incrementalUpload(Date from, Date to, JobParamDto jobParamDto) {
        if (from == null || to == null) {
            log.info("时间参数为空, 无法处理");
            return;
        }
        String fromDate = DateUtil.getStrDateTime(from);
        String toDate = DateUtil.getStrDateTime(to);
        // 用户注册表
        summaryCustomerDetails(fromDate, toDate, jobParamDto.getExecuteCustomerDetails());

        // 订阅数据表
        summarySubscribeInfo(fromDate, toDate, jobParamDto.getExecuteSubscribeInfo());

        // 订阅计划表
        summarySubscribePlan(fromDate, toDate, jobParamDto.getExecuteSubscribePlan());

        // 主动要盒数据表
        summaryCustomerAskBox(fromDate, toDate, jobParamDto.getExecuteCustomerAskBox());

        // 服务订单表
        summaryBox(fromDate, toDate, jobParamDto.getExecuteBox());

        // 交易订单表
        summaryOrder(fromDate, toDate, jobParamDto.getExecuteOrder());

        // BOX-兑换值表
        summaryBUserPointAccount(fromDate, toDate, jobParamDto.getExecuteBUserPointAccount());

        // BOX-收盒礼表
        summaryBNewUserBoxGift(fromDate, toDate, jobParamDto.getExecuteBNewUserBoxGift());
    }

    @Override
    public void uploadBoxEvent(List<String> ids) {
        List<Box> boxList = boxMapper.listByPrimaryKey(ids);
        if (CollectionUtils.isEmpty(boxList)) {
            log.info("box数据为空, 不处理, ids:[{}]", JSON.toJSONString(ids));
            return;
        }
        List<Map<String, Object>> mapList = Lists.newArrayList();
        assemblyBoxList(boxList, mapList);
        if (CollectionUtils.isEmpty(mapList) || Objects.isNull(mapList.get(0))) {
            log.info("转换后box数据为空, 不处理, ids:[{}]", JSON.toJSONString(ids));
            return;
        }
        eventUpload(mapList, MemberDataUploadBoxDto.ENTITY_KEY);
    }

    @Override
    public void uploadOrderEvent(List<String> ids) {
        List<Order> orderList = orderMapper.selectList(new LambdaQueryWrapper<Order>().in(Order::getId, ids));
        if (CollectionUtils.isEmpty(orderList)) {
            log.info("order数据为空, 不处理, ids:[{}]", JSON.toJSONString(ids));
            return;
        }
        List<Map<String, Object>> mapList = Lists.newArrayList();
        assemblyOrderList(orderList, mapList);
        if (CollectionUtils.isEmpty(mapList)) {
            log.info("转换后order数据为空, 不处理, ids:[{}]", JSON.toJSONString(ids));
            return;
        }
        eventUpload(mapList, MemberDataUploadOrderDto.ENTITY_KEY);
    }

    @Override
    public void cdpBoxEventFullDataUploadJob() {
        // 分页同步box单 SELECT id FROM box ORDER BY id ASC;
        int total = boxMapper.selectCount(null);
    }

    @Override
    public void cdpOrderEventFullDataUploadJob() {

    }

    private void summaryOrder(String fromDate, String toDate, Boolean execute) {
        if (!Boolean.TRUE.equals(execute)) {
            log.info("表[{}]不执行增量上传", MemberDataUploadOrderDto.ENTITY_KEY);
            return;
        }
        Integer count = orderMapper.selectIncrementCountByTime(fromDate, toDate);
        log.info("[{}]增量数据总量:{}", MemberDataUploadOrderDto.ENTITY_KEY, count);
        if (count == 0) {
            return;
        }
        int pages = count / INCREMENT_PAGE_SIZE;
        if (count % INCREMENT_PAGE_SIZE > 0) {
            pages += 1;
        }
        List<Map<String, Object>> mapList = Lists.newArrayList();

        for (int pageNum = 1; pageNum <= pages; pageNum++) {
            Integer start = (pageNum - 1) * INCREMENT_PAGE_SIZE + 1;
            Integer end = pageNum * INCREMENT_PAGE_SIZE;
            List<Order> orderList = orderMapper.selectIncrementListByTime(
                    fromDate, toDate, start, end);
            if (CollectionUtils.isEmpty(orderList)) {
                return;
            }
            assemblyOrderList(orderList, mapList);

            partitionUpload(mapList, MemberDataUploadOrderDto.ENTITY_KEY, MemberDataUploadOrderDto.ENTITY_NAME);
            mapList.clear();
        }
    }

    private void summaryBox(String fromDate, String toDate, Boolean execute) {
        if (!Boolean.TRUE.equals(execute)) {
            log.info("表[{}]不执行增量上传", MemberDataUploadBoxDto.ENTITY_KEY);
            return;
        }
        Integer count = boxMapper.selectIncrementCountByTime(fromDate, toDate);
        log.info("[{}]增量数据总量:{}", MemberDataUploadBoxDto.ENTITY_KEY, count);
        if (count == 0) {
            return;
        }
        int pages = count / INCREMENT_PAGE_SIZE;
        if (count % INCREMENT_PAGE_SIZE > 0) {
            pages += 1;
        }
        List<Map<String, Object>> mapList = Lists.newArrayList();
        for (int pageNum = 1; pageNum <= pages; pageNum++) {
            Integer start = (pageNum - 1) * INCREMENT_PAGE_SIZE + 1;
            Integer end = pageNum * INCREMENT_PAGE_SIZE;
            List<Box> boxList = boxMapper.selectIncrementListByTime(
                    fromDate, toDate, start, end);
            log.info("[{}]增量数据：当前页数[{}]", MemberDataUploadBoxDto.ENTITY_KEY, pageNum);
            assemblyBoxList(boxList, mapList);
            partitionUpload(mapList, MemberDataUploadBoxDto.ENTITY_KEY, MemberDataUploadBoxDto.ENTITY_NAME);
            mapList.clear();
        }
    }

    private void summaryCustomerAskBox(String fromDate, String toDate, Boolean execute) {
        if (!Boolean.TRUE.equals(execute)) {
            log.info("表[{}]不执行增量上传", MemberDataUploadCustomerAskBoxDto.ENTITY_KEY);
            return;
        }
        Integer count = customerAskBoxMapper.selectIncrementCountByTime(fromDate, toDate);
        log.info("[{}]增量数据总量:{}", MemberDataUploadCustomerAskBoxDto.ENTITY_KEY, count);
        if (count == 0) {
            return;
        }
        int pages = count / INCREMENT_PAGE_SIZE;
        if (count % INCREMENT_PAGE_SIZE > 0) {
            pages += 1;
        }
        List<Map<String, Object>> mapList = Lists.newArrayList();
        for (int pageNum = 1; pageNum <= pages; pageNum++) {
            Integer start = (pageNum - 1) * INCREMENT_PAGE_SIZE + 1;
            Integer end = pageNum * INCREMENT_PAGE_SIZE;
            List<CustomerAskBox> customerAskBoxList = customerAskBoxMapper.selectIncrementListByTime(
                    fromDate, toDate, start, end);
            log.info("[{}]增量数据：当前页数[{}]", MemberDataUploadCustomerAskBoxDto.ENTITY_KEY, pageNum);
            assemblyCustomerAskBoxList(customerAskBoxList, mapList);
            partitionUpload(mapList, MemberDataUploadCustomerAskBoxDto.ENTITY_KEY, MemberDataUploadCustomerAskBoxDto.ENTITY_NAME);
            mapList.clear();
        }
    }

    private void summarySubscribeInfo(String fromDate, String toDate, Boolean execute) {
        if (!Boolean.TRUE.equals(execute)) {
            log.info("表[{}]不执行增量上传", MemberDataUploadBSubscribeInfoDto.ENTITY_KEY);
            return;
        }
        Integer count = bSubscribeInfoMapper.selectIncrementCountByTime(fromDate, toDate);
        log.info("[{}]增量数据总量:{}", MemberDataUploadBSubscribeInfoDto.ENTITY_KEY, count);
        if (count == 0) {
            return;
        }
        int pages = count / INCREMENT_PAGE_SIZE;
        if (count % INCREMENT_PAGE_SIZE > 0) {
            pages += 1;
        }
        List<Map<String, Object>> mapList = Lists.newArrayList();
        for (int pageNum = 1; pageNum <= pages; pageNum++) {
            Integer start = (pageNum - 1) * INCREMENT_PAGE_SIZE + 1;
            Integer end = pageNum * INCREMENT_PAGE_SIZE;
            List<BSubscribeInfo> bSubscribeInfoList = bSubscribeInfoMapper.selectIncrementListByTime(
                    fromDate, toDate, start, end);
            log.info("[{}]增量数据：当前页数[{}]", MemberDataUploadBSubscribeInfoDto.ENTITY_KEY, pageNum);
            assemblySubscribeInfoList(bSubscribeInfoList, mapList);
            partitionUpload(mapList, MemberDataUploadBSubscribeInfoDto.ENTITY_KEY, MemberDataUploadBSubscribeInfoDto.ENTITY_NAME);
            mapList.clear();
        }
    }

    private void summarySubscribePlan(String fromDate, String toDate, Boolean execute) {
        if (!Boolean.TRUE.equals(execute)) {
            log.info("表[{}]不执行增量上传", MemberDataUploadBSubscribePlanDto.ENTITY_KEY);
            return;
        }
        Integer count = bSubscribePlanMapper.selectIncrementCountByTime(fromDate, toDate);
        log.info("[{}]增量数据总量:{}", MemberDataUploadBSubscribePlanDto.ENTITY_KEY, count);
        if (count == 0) {
            return;
        }
        int pages = count / INCREMENT_PAGE_SIZE;
        if (count % INCREMENT_PAGE_SIZE > 0) {
            pages += 1;
        }
        List<Map<String, Object>> mapList = Lists.newArrayList();
        for (int pageNum = 1; pageNum <= pages; pageNum++) {
            Integer start = (pageNum - 1) * INCREMENT_PAGE_SIZE + 1;
            Integer end = pageNum * INCREMENT_PAGE_SIZE;
            List<BSubscribePlan> bSubscribeInfoList = bSubscribePlanMapper.selectIncrementListByTime(
                    fromDate, toDate, start, end);
            log.info("[{}]增量数据：当前页数[{}]", MemberDataUploadBSubscribePlanDto.ENTITY_KEY, pageNum);
            assemblySubscribePlanList(bSubscribeInfoList, mapList);
            partitionUpload(mapList, MemberDataUploadBSubscribePlanDto.ENTITY_KEY, MemberDataUploadBSubscribePlanDto.ENTITY_NAME);
            mapList.clear();
        }
    }

    private void summaryCustomerDetails(String fromDate, String toDate, Boolean execute) {
        if (!Boolean.TRUE.equals(execute)) {
            log.info("表[{}]不执行增量上传", MemberDataUploadCustomerDetailsDto.ENTITY_KEY);
            return;
        }
        Integer count = customerDetailsMapper.selectIncrementCountByTime(fromDate, toDate);
        log.info("表[{}]增量数据总量:{}", MemberDataUploadCustomerDetailsDto.ENTITY_KEY, count);
        if (count == 0) {
            return;
        }
        int pages = count / INCREMENT_PAGE_SIZE;
        if (count % INCREMENT_PAGE_SIZE > 0) {
            pages += 1;
        }
        List<Map<String, Object>> mapList = Lists.newArrayList();
        for (int pageNum = 1; pageNum <= pages; pageNum++) {
            Integer start = (pageNum - 1) * INCREMENT_PAGE_SIZE + 1;
            Integer end = pageNum * INCREMENT_PAGE_SIZE;
            List<CustomerDetails> customerDetails = customerDetailsMapper.selectIncrementListByTime(
                    fromDate, toDate, start, end);
            log.info("表[{}]增量数据：当前页数[{}]", MemberDataUploadCustomerDetailsDto.ENTITY_KEY, pageNum);
            if (CollectionUtils.isEmpty(customerDetails)) {
                return;
            }
            assemblyCustomerList(customerDetails, mapList);
            partitionUpload(mapList, MemberDataUploadCustomerDetailsDto.ENTITY_KEY, MemberDataUploadCustomerDetailsDto.ENTITY_NAME);
            mapList.clear();
        }
    }

    private void assemblyBUserPointAccountList(List<BUserPointAccount> bUserPointAccounts, List<Map<String, Object>> mapList) {
        List<String> ids = Optional.ofNullable(bUserPointAccounts).orElse(Lists.newArrayList()).stream()
                .map(BUserPointAccount::getUnionId).distinct().collect(Collectors.toList());
        if (ids.isEmpty()) {
            return;
        }
        Map<String, String> unoinId2CnoMap = getUnoinId2CnoMap(ids);

        // 过滤出totalPoint>0的数据，根据unionId获取临期兑换值
        List<String> filterHasPointsUnionIds = bUserPointAccounts.stream().filter(bUserPointAccount ->
                        (bUserPointAccount.getTotalPoint() != null && bUserPointAccount.getTotalPoint().compareTo(BigDecimal.ZERO) > 0))
                .map(BUserPointAccount::getUnionId).distinct().collect(Collectors.toList());
        Map<String, BigDecimal> unionId2PointMap = getFutureExpiredPoint(filterHasPointsUnionIds);

        Optional.ofNullable(bUserPointAccounts).orElse(Lists.newArrayList()).forEach(bUserPointAccount -> {
            MemberDataUploadBUserPointAccountDto dto = new MemberDataUploadBUserPointAccountDto()
                    .toDto(bUserPointAccount, unoinId2CnoMap.get(bUserPointAccount.getUnionId()),
                            unionId2PointMap.get(bUserPointAccount.getUnionId()) == null ? BigDecimal.ZERO : unionId2PointMap.get(bUserPointAccount.getUnionId()));
            Map<String, Object> map = BeanUtil.beanToMap(dto);
            mapList.add(map);
        });
    }

    private Map<String, BigDecimal> getFutureExpiredPoint(List<String> filterHasPointsUnionIds) {
        if (CollectionUtils.isEmpty(filterHasPointsUnionIds)) {
            return new HashMap<>();
        }
        List<BPointDetail> listByUnionIdAndTime = bPointDetailMapper.getListByUnionIdAndTime(filterHasPointsUnionIds, 30);
        if (CollectionUtils.isNotEmpty(listByUnionIdAndTime)) {
            // 按照unionId分组，然后再汇总
            return listByUnionIdAndTime.stream().collect(Collectors.groupingBy(BPointDetail::getUnionId))
                    .entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> {
                        BigDecimal total = entry.getValue().stream().map(BPointDetail::getPoint).reduce(BigDecimal.ZERO, BigDecimal::add);
                        return total;
                    }));
        }
        return new HashMap<>();
    }

    private void assemblyBNewUserBoxGiftList(List<BNewUserBoxGiftDTO> bNewUserBoxGifts, List<Map<String, Object>> mapList) {
        List<String> ids = Optional.ofNullable(bNewUserBoxGifts).orElse(Lists.newArrayList()).stream()
                .map(BNewUserBoxGiftDTO::getUnionid).distinct().collect(Collectors.toList());
        if (ids.isEmpty()) {
            return;
        }
        Map<String, String> unoinId2CnoMap = getUnoinId2CnoMap(ids);

        Optional.ofNullable(bNewUserBoxGifts).orElse(Lists.newArrayList()).forEach(bNewUserBoxGiftDTO -> {
            MemberDataUploadBNewUserBoxGiftDto dto = new MemberDataUploadBNewUserBoxGiftDto()
                    .toDto(bNewUserBoxGiftDTO, unoinId2CnoMap.get(bNewUserBoxGiftDTO.getUnionid()));
            Map<String, Object> map = BeanUtil.beanToMap(dto);
            mapList.add(map);
        });
    }

    private void assemblyCustomerList(List<CustomerDetails> customerDetails, List<Map<String, Object>> mapList) {
        // 批量获取微信公众号openId
        List<String> unionIdList = customerDetails.stream().map(CustomerDetails::getUnionid)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        LambdaQueryWrapper<WeiXinFans> weixinFansQueryWrapper = new LambdaQueryWrapper<WeiXinFans>()
                .select(WeiXinFans::getUnionid, WeiXinFans::getOpenid)
                .in(WeiXinFans::getUnionid, unionIdList)
                .groupBy(WeiXinFans::getUnionid, WeiXinFans::getOpenid);
        Map<String, String> unionId2minOpenId = Optional.ofNullable(weiXinFansMapper.selectList(weixinFansQueryWrapper))
                .orElse(Lists.newArrayList())
                .stream().collect(Collectors.toMap(WeiXinFans::getUnionid, WeiXinFans::getOpenid, (k1, k2) -> k1));
        // 批量获取黑名单信息
        List<String> userIdList = customerDetails.stream().map(CustomerDetails::getId).collect(Collectors.toList());
        Map<String, BCustomerInformation> userId2CustomerInformation = Optional.ofNullable(
                        bCustomerInformationMapper.selectList(
                                new LambdaQueryWrapper<BCustomerInformation>().in(BCustomerInformation::getUserId, userIdList)))
                .orElse(Lists.newArrayList())
                .stream().collect(Collectors.toMap(BCustomerInformation::getUserId, Function.identity(), (k1, k2) -> k1));

        Optional.ofNullable(customerDetails).orElse(Lists.newArrayList()).forEach(customerDetail -> {
            if (customerDetail == null) {
                return;
            }
            // 获取微信公众号openId
            String minOpenId = unionId2minOpenId.get(customerDetail.getUnionid());
            // 批量获取黑名单类型。57为黑名单用户，需要取原因字段，如果原因为空，则认为
            String categoryIdStr = null;
            BCustomerInformation bCustomerInformation = userId2CustomerInformation.get(customerDetail.getId());
            if (bCustomerInformation != null) {
                Long categoryId = bCustomerInformation.getbCategoryId();
                categoryIdStr = String.valueOf(categoryId);
                if (categoryId == 57 && bCustomerInformation.getJoinCategoryReasonId() != null) {
                    categoryIdStr = String.valueOf(bCustomerInformation.getJoinCategoryReasonId());
                }
            }
            if (StringUtils.isNotBlank(minOpenId)) {
                minOpenId = commonProperties.getMpAppId() + "_" + minOpenId;
            }

            // box小程序openId
            String openid = customerDetail.getOpenid();
            if (StringUtils.isNotBlank(openid)) {
                openid = commonProperties.getBoxAppId() + "_" + openid;
            }
            MemberDataUploadCustomerDetailsDto dto = new MemberDataUploadCustomerDetailsDto().toDto(customerDetail, openid, minOpenId, categoryIdStr);
            Map<String, Object> map = BeanUtil.beanToMap(dto);
            mapList.add(map);
        });
    }

    private void summaryBUserPointAccount(String fromDate, String toDate, Boolean execute) {
        if (!Boolean.TRUE.equals(execute)) {
            log.info("表[{}]不执行增量上传", MemberDataUploadBUserPointAccountDto.ENTITY_KEY);
            return;
        }
        Integer count = bUserPointAccountMapper.selectIncrementCountByTime(fromDate, toDate);
        log.info("[{}]增量数据总量:{}", MemberDataUploadBUserPointAccountDto.ENTITY_KEY, count);
        if (count == 0) {
            return;
        }
        int pages = count / INCREMENT_PAGE_SIZE;
        if (count % INCREMENT_PAGE_SIZE > 0) {
            pages += 1;
        }
        List<Map<String, Object>> mapList = Lists.newArrayList();
        for (int pageNum = 1; pageNum <= pages; pageNum++) {
            Integer start = (pageNum - 1) * INCREMENT_PAGE_SIZE + 1;
            Integer end = pageNum * INCREMENT_PAGE_SIZE;
            List<BUserPointAccount> bUserPointAccountList = bUserPointAccountMapper.selectIncrementListByTime(
                    fromDate, toDate, start, end);
            log.info("[{}]增量数据：当前页数[{}]", MemberDataUploadBUserPointAccountDto.ENTITY_KEY, pageNum);
            assemblyBUserPointAccountList(bUserPointAccountList, mapList);
            partitionUpload(mapList, MemberDataUploadBUserPointAccountDto.ENTITY_KEY, MemberDataUploadBUserPointAccountDto.ENTITY_NAME);
            mapList.clear();
        }
    }

    private void summaryBNewUserBoxGift(String fromDate, String toDate, Boolean execute) {
        if (!Boolean.TRUE.equals(execute)) {
            log.info("表[{}]不执行增量上传", MemberDataUploadBNewUserBoxGiftDto.ENTITY_KEY);
            return;
        }
        Integer count = bNewUserBoxGiftMapper.selectIncrementCountByTime(fromDate, toDate);
        log.info("[{}]增量数据总量:{}", MemberDataUploadBNewUserBoxGiftDto.ENTITY_KEY, count);
        if (count == 0) {
            return;
        }
        int pages = count / INCREMENT_PAGE_SIZE;
        if (count % INCREMENT_PAGE_SIZE > 0) {
            pages += 1;
        }
        List<Map<String, Object>> mapList = Lists.newArrayList();
        for (int pageNum = 1; pageNum <= pages; pageNum++) {
            Integer start = (pageNum - 1) * INCREMENT_PAGE_SIZE + 1;
            Integer end = pageNum * INCREMENT_PAGE_SIZE;
            List<BNewUserBoxGiftDTO> bNewUserBoxGiftList = bNewUserBoxGiftMapper.selectIncrementListByTime(
                    fromDate, toDate, start, end);
            log.info("[{}]增量数据：当前页数[{}]", MemberDataUploadBNewUserBoxGiftDto.ENTITY_KEY, pageNum);
            assemblyBNewUserBoxGiftList(bNewUserBoxGiftList, mapList);
            partitionUpload(mapList, MemberDataUploadBNewUserBoxGiftDto.ENTITY_KEY, MemberDataUploadBNewUserBoxGiftDto.ENTITY_NAME);
            mapList.clear();
        }
    }
}
