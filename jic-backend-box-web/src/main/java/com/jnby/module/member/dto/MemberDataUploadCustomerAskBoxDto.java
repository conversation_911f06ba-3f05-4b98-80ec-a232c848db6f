package com.jnby.module.member.dto;

import com.jnby.common.util.DateUtil;
import com.jnby.infrastructure.box.model.CustomerAskBox;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import static com.jnby.common.util.DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS;

@ApiModel(value = "BOX-主动要盒记录表")
@Data
public class MemberDataUploadCustomerAskBoxDto {

    public static String ENTITY_KEY = "CUSTOMER_ASK_BOX";
    public static String ENTITY_NAME = "BOX-主动要盒记录表";

    @ApiModelProperty(value = "主键ID")
    private String keyId;
    @ApiModelProperty(value = "微信unionId")
    private String wx_unionid;
    @ApiModelProperty(value = "搭配师ID")
    private String createFasId;
    @ApiModelProperty(value = "要盒来源: 0系统创建 1用户创建 2导购创建 3搭配师创建")
    private Long createBy;
    @ApiModelProperty(value = "订阅ID")
    private String subId;
    @ApiModelProperty(value = "订阅节点ID")
    private String subPlanId;
    @ApiModelProperty("状态:0待搭配,1已搭配,2已取消")
    private Short status;
    @ApiModelProperty("创建时间")
    private String createTime_;
    @ApiModelProperty(value = "集团卡号")
    private String cno;

    public MemberDataUploadCustomerAskBoxDto toDto(CustomerAskBox db, String cno) {
        if (db == null) {
            return null;
        }
        this.keyId = db.getId();
        this.wx_unionid = db.getUnionid();
        this.createFasId = db.getCreateFasId();
        this.createBy = db.getCreateBy();
        this.subId = db.getSubId();
        this.subPlanId = db.getSubPlanId();
        this.status = db.getStatus();
        if (db.getCreateTime() != null) {
            this.createTime_ = DateUtil.parseDate(db.getCreateTime(), DATEFORMATE_YYYY_MM_DD_HHMMSS);
        }
        this.cno = cno;
        return this;
    }
}
