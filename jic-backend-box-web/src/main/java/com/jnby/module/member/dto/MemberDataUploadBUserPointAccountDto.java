package com.jnby.module.member.dto;

import com.jnby.common.util.DateUtil;
import com.jnby.infrastructure.box.model.BUserPointAccount;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

import static com.jnby.common.util.DateUtil.DATEFORMATE_YYYY_MM_DD_HHMMSS;

@ApiModel(value = "BOX-兑换值表")
@Data
public class MemberDataUploadBUserPointAccountDto {

    public static String ENTITY_KEY = "box_b_user_point_account";
    public static String ENTITY_NAME = "BOX-兑换值表";

    @ApiModelProperty(value = "主键ID")
    private String keyId;
    @ApiModelProperty(value = "用户unionId")
    private String unionid;
    @ApiModelProperty(value = "微信unionId")
    private String wx_unionid;
    @ApiModelProperty(value = "集团卡号")
    private String cno;
    @ApiModelProperty(value = "可用兑换值")
    private BigDecimal total_point;
    @ApiModelProperty(value = "即将过期兑换值")
    private BigDecimal future_expired_point;
    @ApiModelProperty(value = "创建时间")
    private String create_time;
    @ApiModelProperty(value = "修改时间")
    private String update_time;
    @ApiModelProperty(value = "是否删除:0=否，1=是")
    private Long is_del;

    public MemberDataUploadBUserPointAccountDto toDto(BUserPointAccount db, String cno, BigDecimal futureExpiredPoint) {
        if (db == null) {
            return null;
        }
        this.keyId = db.getId();
        this.unionid = db.getUnionId();
        this.wx_unionid = db.getUnionId();
        this.cno = cno;
        this.total_point = db.getTotalPoint();
        // 注意：原表中没有 future_expired_point 字段，这里暂时设置为 canUsePoint
        // 如果需要真正的即将过期兑换值，需要在原表中添加该字段或从其他地方获取
        this.future_expired_point = futureExpiredPoint;
        if (db.getCreateTime() != null) {
            this.create_time = DateUtil.parseDate(db.getCreateTime(), DATEFORMATE_YYYY_MM_DD_HHMMSS);
        }
        if (db.getUpdateTime() != null) {
            this.update_time = DateUtil.parseDate(db.getUpdateTime(), DATEFORMATE_YYYY_MM_DD_HHMMSS);
        }
        this.is_del = db.getDelFlag();
        return this;
    }
}
