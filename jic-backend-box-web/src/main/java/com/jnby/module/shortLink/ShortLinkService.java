package com.jnby.module.shortLink;

import com.jnby.common.Page;
import com.jnby.dto.ShortLinkToolsDto;
import com.jnby.dto.ShortLinkToolsListReq;
import me.chanjar.weixin.common.error.WxErrorException;

import java.io.IOException;
import java.util.List;

public interface ShortLinkService {
    /**
     * 根据id生成短链接  太阳码等信息
     * @param id
     * @Param isCreateNewImg
     */
    void genShortLinkById(String id) throws WxErrorException, IOException;

    String miniShortLink(String id) throws WxErrorException, IOException;

    void changeStatus(String id, Integer isDel);

    String create(ShortLinkToolsDto requestData, String userId);

    /**
     * 分页列表
     * @param requestData
     * @param page
     * @return
     */
    List<ShortLinkToolsDto> list(ShortLinkToolsListReq requestData, Page page);

    ShortLinkToolsDto details(String requestData);
}
