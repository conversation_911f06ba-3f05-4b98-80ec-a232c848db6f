package com.jnby.module.shortLink.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.enums.IdConstant;
import com.jnby.common.util.*;
import com.jnby.dto.BrandConfig;
import com.jnby.dto.GenerateShortUrlReq;
import com.jnby.dto.ShortLinkToolsDto;
import com.jnby.dto.ShortLinkToolsListReq;
import com.jnby.infrastructure.box.mapper.ShortLinkToolsMapper;
import com.jnby.infrastructure.box.model.ShortLinkTools;
import com.jnby.module.remote.IJICHttpApi;
import com.jnby.module.remote.entity.JICCommonVouResponse;
import com.jnby.module.shortLink.ShortLinkService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import retrofit2.Response;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RefreshScope
public class ShortLinkServiceImpl implements ShortLinkService {

    @Autowired
    private ShortLinkToolsMapper shortLinkToolsMapper;

    @Autowired
    private GenerateShortUrlUtil generateShortUrlUtil;

    @Autowired
    private IJICHttpApi ijicHttpApi;

    @Value("${wx.brand.commonConfigs}")
    private String commonConfigs;

    @Value("${create.short.link.perfix}")
    private String shortLinkPerfix;

    @Value("${jic.express.url}")
    private String jicLinkPerfix;

    @Value("${jump.link.perfix}")
    private String jumpLinkPerfix;

    public static String imgPath = "https://img.bzhz.jnbygroup.com/";

    @Resource
    private RedissonUtil redissonUtil;

    @Autowired
    private RedisService redisService;

//    @Autowired
//    private ISceneQrcodeService sceneQrcodeService;

    @Override
    public void genShortLinkById(String id) throws WxErrorException, IOException {
        ShortLinkTools shortLinkTools = shortLinkToolsMapper.selectById(id);
        if(shortLinkTools == null){
            log.info("根据id未查询到生成短链的信息");
            return ;
        }
        // 查询是否有  exposureChannel=
        if(StringUtils.isNotBlank(shortLinkTools.getParamsSettingContent()) && !shortLinkTools.getParamsSettingContent().contains("exposureChannel=")){
            ShortLinkTools update = new ShortLinkTools();
            update.setParamsSettingContent("exposureChannel="+shortLinkTools.getParamsSettingContent());
            update.setId(id);
            shortLinkToolsMapper.updateById(update);
        }
        // 查询前缀
        if(shortLinkTools.getPathType().equals("1")){
            if(shortLinkTools.getWeid().equals("10000")){
                //
                if(!shortLinkTools.getPath().contains("pages/out/index?src=")){
                    ShortLinkTools update = new ShortLinkTools();
//                    String encode = URLEncoder.encode(shortLinkTools.getPath());
                    update.setPath("pages/out/index?src="+shortLinkTools.getPath());
                    update.setId(id);
                    shortLinkToolsMapper.updateById(update);
                }
            }else{
                //前缀增加 /packages/wm-cloud-jnbytools/h5share/index?url=
                if(shortLinkTools.getPageType().equals(0)){
                    if(!shortLinkTools.getPath().contains("packages/wm-cloud-jnbytools/h5share/index?url=")){
                        ShortLinkTools update = new ShortLinkTools();
                        update.setPath("packages/wm-cloud-jnbytools/h5share/index?url="+shortLinkTools.getPath());
                        update.setId(id);
                        shortLinkToolsMapper.updateById(update);
                    }
                }else{
                    if(!shortLinkTools.getPath().contains("packages/wm-cloud-jnbytools/loginInfoPage/index?url=")){
                        ShortLinkTools update = new ShortLinkTools();
                        update.setPath("packages/wm-cloud-jnbytools/loginInfoPage/index?url="+shortLinkTools.getPath());
                        update.setId(id);
                        shortLinkToolsMapper.updateById(update);
                    }
                }
            }
        }

//        // 如果是系统加参数的  不为 -1 那么就是拼接
//        if(!shortLinkTools.getSystemAddParam().equals("-1")){
//            ShortLinkTools update = new ShortLinkTools();
//            update.setPath("packages/wm-cloud-jnbytools/loginInfoPage/index?url="+shortLinkTools.getPath());
//            update.setId(id);
//            shortLinkToolsMapper.updateById(update);
//        }

        // 重新查询一遍
        shortLinkTools = shortLinkToolsMapper.selectById(id);

        String scene = "";
        // 为空才进行更改太阳码
        if(StringUtils.isBlank(shortLinkTools.getImgUrl())){
            // 生成小程序太阳码
            Map<String,String> map = createScene(shortLinkTools);
            String qrcodeUrl = map.get("imgUrl");
            scene = map.get("scene");
            if(StringUtils.isNotBlank(qrcodeUrl)){
                ShortLinkTools update = new ShortLinkTools();
                update.setImgUrl(qrcodeUrl);
                update.setId(id);
                shortLinkToolsMapper.updateById(update);
            }
            //        // 如果是 系统加参数的  重新生成第二遍短链接
//            if(!shortLinkTools.getSystemAddParam().equals("-1") && StringUtils.isNotBlank(scene)){
//                List<BrandConfig> brandConfigs = JSONObject.parseArray(commonConfigs, BrandConfig.class);
//                String appid = "";
//                for (BrandConfig brandConfig : brandConfigs) {
//                    if(brandConfig.getWeid().equals(shortLinkTools.getWeid())){
//                        // 获取appid
//                        appid = brandConfig.getAppid();
//                    }
//                }
//                Map<String,Object> paramsMap = new HashMap<>();
//                paramsMap.put("url","packages/wm-cloud-jnbytools/loginInfoPage/index");
//                paramsMap.put("isHyaline",true);
//                paramsMap.put("width",430);
//                paramsMap.put("scene","packages/wm-cloud-jnbytools/loginInfoPage/index?scene="+scene);
//                log.info("createScene req = {}",JSONObject.toJSONString(paramsMap));
//                try {
//                    Response<JICCommonVouResponse<Map<String,String>>> execute = ijicHttpApi.generateMiniappCode(appid,paramsMap).execute();
//                    if(execute.isSuccessful()){
//                        JICCommonVouResponse<Map<String, String>> body = execute.body();
//                        log.info("createScene resp = {}",JSONObject.toJSONString(body));
//                        Map<String, String> data = body.getData();//base64图
//                        scene = data.get("scene");
//                    }
//                }catch (Exception e){
//                    log.info("createScene = ",e);
//                }
//            }
        }

        // 为空才进行更改短链接
        if(StringUtils.isBlank(shortLinkTools.getShortLink())){
            String shortLink = "";
            shortLink = genShortLinkUrl(shortLinkTools,scene);
            // 更新短链接
            if(StringUtils.isNotBlank(shortLink)){
                ShortLinkTools update = new ShortLinkTools();
                update.setShortLink(shortLink);
                update.setId(id);
                update.setPermanentDate(new Date());
                shortLinkToolsMapper.updateById(update);
            }
        }
    }

    private Map<String,String> createScene(ShortLinkTools shortLinkTools) {
        Map<String,String> resultMap = new HashMap<>();

        //如果是box
        if(shortLinkTools.getWeid().equals("10000")){
            String pageUrl = "";
            if(shortLinkTools.getPath().contains("?")){
                if(shortLinkTools.getParamsSettingType().equals("0")){
                    if(shortLinkTools.getPathType().equals("1")){
                        pageUrl = shortLinkTools.getPath() +"?"+shortLinkTools.getParamsSettingContent();
                    }else{
                        pageUrl = shortLinkTools.getPath() +"&"+shortLinkTools.getParamsSettingContent();
                    }

                }else{
                    pageUrl = shortLinkTools.getPath();
                }
            }else{
                if(shortLinkTools.getParamsSettingType().equals("0")){
                    pageUrl = shortLinkTools.getPath() +"?"+shortLinkTools.getParamsSettingContent();
                }else{
                    pageUrl = shortLinkTools.getPath();
                }
            }

            // 如果设置了系统加参  和  页面  增加 参数到query中
            if(StringUtils.isBlank(pageUrl)){
                pageUrl = pageUrl +"pt="+shortLinkTools.getPageType();
            }else{
                if(pageUrl.contains("?")){
                    pageUrl = pageUrl +"&pt="+shortLinkTools.getPageType();
                }else{
                    pageUrl = pageUrl +"?pt="+shortLinkTools.getPageType();
                }
            }
            pageUrl = pageUrl +"&sap="+shortLinkTools.getSystemAddParam();
            pageUrl = pageUrl +"&sahod="+shortLinkTools.getSystemAddHourOrDay();


            Map<String,Object> map = new HashMap<>();
            if(pageUrl.contains("?")){
                map.put("page",pageUrl.substring(0,pageUrl.indexOf("?")));
            }else{
                map.put("page",pageUrl);
            }
            map.put("is_hyaline",true);
            map.put("width",430);
            if(pageUrl.contains("?")){
                map.put("scene",pageUrl.substring(pageUrl.indexOf("?") + 1 ));
            }else{
                map.put("scene","k=1");
            }
            // 生成box的
            byte[] qrCode =  generateShortUrlUtil.generateQrCode(map);
            if(qrCode != null && qrCode.length > 0){
                CommonRequest<Map<String,Object>> commonRequest  = new CommonRequest<>();
                Map<String,Object> mapParams  = new HashMap<>();
                String uploadKey = "SHORT_LINK" + shortLinkTools.getId();
                mapParams.put("bodyData",qrCode);
                mapParams.put("key",uploadKey);
                commonRequest.setRequestData(mapParams);
                HttpUtil.post(shortLinkPerfix + "/gateway/api/upload/base64", JSONObject.toJSONString(commonRequest));
                String imgUrl = imgPath + uploadKey;
                resultMap.put("imgUrl",imgUrl);
                return resultMap;
            }
            return resultMap;
        }else{
            // 微商城的
            String pageUrl = "";
            if(shortLinkTools.getPath().contains("?")){
                if(shortLinkTools.getParamsSettingType().equals("0")){
                    if(shortLinkTools.getPathType().equals("1")){
                        // H5  拆分
                        String s = shortLinkTools.getPath().split("url=")[1];
                        //包含问号
                        if(s.contains("?")){
                            pageUrl = shortLinkTools.getPath() +"&"+shortLinkTools.getParamsSettingContent();
                        }else{
                            pageUrl = shortLinkTools.getPath() +"?"+shortLinkTools.getParamsSettingContent();
                        }
                    }else{
                        pageUrl = shortLinkTools.getPath() +"&"+shortLinkTools.getParamsSettingContent();
                    }

                }else{
                    pageUrl = shortLinkTools.getPath();
                }
            }else{
                if(shortLinkTools.getParamsSettingType().equals("0")){
                    pageUrl = shortLinkTools.getPath() +"?"+shortLinkTools.getParamsSettingContent();
                }else{
                    pageUrl = shortLinkTools.getPath();
                }
            }
            // 调用微商城生成 codeUrl
            List<BrandConfig> brandConfigs = JSONObject.parseArray(commonConfigs, BrandConfig.class);
            String appid = "";
            for (BrandConfig brandConfig : brandConfigs) {
                if(brandConfig.getWeid().equals(shortLinkTools.getWeid())){
                    // 获取appid
                    appid = brandConfig.getAppid();
                }
            }
            if(StringUtils.isBlank(appid)){
                return resultMap;
            }

            // 如果设置了系统加参  和  页面  增加 参数到query中
            // 如果设置了系统加参  和  页面  增加 参数到query中
            if(shortLinkTools.getPathType().equals("0")){
                if(StringUtils.isBlank(pageUrl)){
                    pageUrl = pageUrl +"pt="+shortLinkTools.getPageType();
                }else{
                    if(pageUrl.contains("?")){
                        pageUrl = pageUrl +"&pt="+shortLinkTools.getPageType();
                    }else{
                        pageUrl = pageUrl +"?pt="+shortLinkTools.getPageType();
                    }
                }
                pageUrl = pageUrl +"&sap="+shortLinkTools.getSystemAddParam();
                pageUrl = pageUrl +"&sahod="+shortLinkTools.getSystemAddHourOrDay();
            }

            Map<String,Object> map = new HashMap<>();
            if(shortLinkTools.getSystemAddParam().equals("-1")){
                map.put("url","packages/wm-cloud-jnbytools/jiccode/index");
            }else{
                map.put("url","packages/wm-cloud-jnbytools/loginInfoPage/index");
            }
            map.put("isHyaline",true);
            map.put("width",430);
            if(shortLinkTools.getPathType().equals("0")){
                // 小程序
                map.put("scene",pageUrl);
            }else{
                if(pageUrl.contains("?")){
                    String shouldEncode = pageUrl.substring(pageUrl.indexOf("?") + 5);
                    String encode = URLEncoder.encode(shouldEncode);
                    String perfix = pageUrl.substring(0, pageUrl.indexOf("?"));
                    // 小程序带H5 同级拼接
                    if(shortLinkTools.getPathType().equals("1")){
                        if(StringUtils.isBlank(encode)){
                            encode = encode +"pt="+shortLinkTools.getPageType();
                        }else{
                            encode = encode +"&pt="+shortLinkTools.getPageType();
                        }
                        encode = encode +"&sap="+shortLinkTools.getSystemAddParam();
                        encode = encode +"&sahod="+shortLinkTools.getSystemAddHourOrDay();
                    }
                    map.put("scene",perfix + "?url=" + encode);
                }else{
                    map.put("scene",pageUrl+"?kx=1");
                }
            }

            log.info("createScene req = {}",JSONObject.toJSONString(map));
            try {
                Response<JICCommonVouResponse<Map<String,String>>> execute = ijicHttpApi.generateMiniappCode(appid,map).execute();
                if(execute.isSuccessful()){
                    JICCommonVouResponse<Map<String, String>> body = execute.body();
//                    log.info("createScene resp = {}",JSONObject.toJSONString(body));
                    Map<String, String> data = body.getData();//base64图片
// 如果不为空 则上传到七牛
                    String base64= data.get("base64");
                    String scene = data.get("scene");
                    CommonRequest<Map<String,Object>> commonRequest  = new CommonRequest<>();
                    Map<String,Object> mapParams  = new HashMap<>();
                    String uploadKey = "SHORT_LINK" + shortLinkTools.getId();
                    mapParams.put("bodyData",base64);
                    mapParams.put("key",uploadKey);
                    commonRequest.setRequestData(mapParams);
                    HttpUtil.post(shortLinkPerfix + "/gateway/api/upload/base64", JSONObject.toJSONString(commonRequest));
                    String imgUrl = imgPath + uploadKey;
                    resultMap.put("imgUrl",imgUrl);
                    resultMap.put("scene",scene);
                    return resultMap;
                }
            }catch (Exception e){
                log.info("createScene = ",e);
            }
            return resultMap;
        }
    }

    @Override
    public String miniShortLink(String id) throws WxErrorException, IOException {
        // 查询缓存
        String redisKey = "SHORTLINK:ID:" + id;
        Object o = redisService.get(redisKey);
        if(o != null){
            ShortLinkTools shortLinkTools = JSONObject.parseObject(o.toString(), ShortLinkTools.class);
            String s = updateRedisShortLink(shortLinkTools,redisKey);
            if(s == null){
                return shortLinkTools.getPermanentShortLink();
            }else{
                return s;
            }
        }

        ShortLinkTools shortLinkTools = shortLinkToolsMapper.selectById(id);
        if(shortLinkTools == null){
            log.info("根据id未查询到生成短链的信息");
            return null;
        }
        // 当前时间 减去 生成时间  大于 29天  则重新生成  否则直接返回
        // 默认加缓存  缓存时间小于 29天
        redisService.set(redisKey,JSONObject.toJSONString(shortLinkTools));
        String s = updateRedisShortLink(shortLinkTools,redisKey);
        if(s == null){
            return shortLinkTools.getPermanentShortLink();
        }else{
            return s;
        }
    }

    public String updateRedisShortLink(ShortLinkTools shortLinkTools,String redisKey){
        if((new Date().getTime() - shortLinkTools.getPermanentDate().getTime())/(24 *60 *60 *1000) > 29L
                ||  StringUtils.isBlank(shortLinkTools.getPermanentShortLink())
        ){
            String key = "MINSHORTLINK" + shortLinkTools.getId();
            boolean b = redissonUtil.tryLock(key);
            try {
                if(b){
                    // 如果是 saas 页面才会重新生成
                    // 重新生成一个图片  然后重新
                    // 生成小程序太阳码
                    String scene = "";
                    if(shortLinkTools.getPageType().equals(1)){
                        Map<String,String> map = createScene(shortLinkTools);
                        String qrcodeUrl = map.get("imgUrl");
                        scene = map.get("scene");
                        if(StringUtils.isNotBlank(qrcodeUrl)){
                            ShortLinkTools update = new ShortLinkTools();
                            update.setImgUrl(qrcodeUrl);
                            update.setId(shortLinkTools.getId());
                            shortLinkToolsMapper.updateById(update);
                        }
                    }

                    String permanentShortLink = genShortLinkUrlEffectTime(shortLinkTools, scene);
                    ShortLinkTools update = new ShortLinkTools();
                    update.setPermanentShortLink(permanentShortLink);
                    update.setId(shortLinkTools.getId());
                    update.setPermanentDate(new Date());
                    shortLinkToolsMapper.updateById(update);
                    // 更新缓存
                    ShortLinkTools shortLinkTools1 = shortLinkToolsMapper.selectById(shortLinkTools.getId());
                    redisService.set(redisKey,JSONObject.toJSONString(shortLinkTools1));
                    return permanentShortLink;
                }
            }catch (Exception e){
                log.info("miniShortLink出现错误=",e);
            }finally {
                redissonUtil.unlock(key);
            }
        }
        return null;
    }

    @Override
    public void changeStatus(String id, Integer isDel) {
        ShortLinkTools shortLinkTools = new ShortLinkTools();
        shortLinkTools.setId(id);
        shortLinkTools.setIsDel(isDel);
        shortLinkToolsMapper.updateById(shortLinkTools);
    }

//    public static void main(String[] args) {
//        StringBuilder randomStr = new StringBuilder();
//        for (int i = 0 ; i < 4 ; i++){
//            int randomNum = (int) (Math.random() * 26); // 生成0到25之间的随机数
//            char randomChar = (char) ('a' + randomNum); // 将随机数映射到'a'到'z'之间的字符
//            randomStr.append(randomChar);
//        }
//        System.out.println(randomStr.toString());
//    }

    @Override
    public String create(ShortLinkToolsDto requestData, String userId) {
        ShortLinkTools shortLinkTools = new ShortLinkTools();
        BeanUtils.copyProperties(requestData,shortLinkTools);
        if(requestData.getWeid().equals("10000")){
            // box默认不带参数
            shortLinkTools.setParamsSettingType("1");
        }
        String id = IdLeaf.getId(IdConstant.SHORT_LINK_TOOLS);
        // 四位 英文字符
        StringBuilder randomStr = new StringBuilder();
        for (int i = 0 ; i < 4 ; i++){
            int randomNum = (int) (Math.random() * 26); // 生成0到25之间的随机数
            char randomChar = (char) ('a' + randomNum); // 将随机数映射到'a'到'z'之间的字符
            randomStr.append(randomChar);
        }
        String finalId = id + randomStr.toString();
        shortLinkTools.setId(finalId);
        shortLinkTools.setCreateBy(userId);
        shortLinkTools.setCreateTime(new Date());
        shortLinkTools.setUpdateTime(new Date());
        shortLinkTools.setUpdateBy(userId);
        shortLinkToolsMapper.insert(shortLinkTools);
        return shortLinkTools.getId();
    }

    @Override
    public List<ShortLinkToolsDto> list(ShortLinkToolsListReq requestData, Page page) {
        com.github.pagehelper.Page<ShortLinkTools> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        shortLinkToolsMapper.selectByParams(requestData);
        PageInfo<ShortLinkTools> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        String jsonString = JSONObject.toJSONString(pageInfo.getList());
        return JSONObject.parseArray(jsonString,ShortLinkToolsDto.class);
    }

    @Override
    public ShortLinkToolsDto details(String id) {
        ShortLinkTools shortLinkTools = shortLinkToolsMapper.selectById(id);
        return JSONObject.parseObject(JSONObject.toJSONString(shortLinkTools),ShortLinkToolsDto.class);
    }

    private String genShortLinkUrl(ShortLinkTools shortLinkTools,
                                   String scene) throws WxErrorException, IOException {
        // 判断是否是短期短链接
        if(shortLinkTools.getEffectType() == 1){
            return genShortLinkUrlEffectTime(shortLinkTools,scene);
        }else{
            // 生成永久的
            if(shortLinkTools.getWeid().equals("10000")){
                // 走box 短期 短链接 生成一个假的链接
                return jumpLinkPerfix+"/"+shortLinkTools.getId();
            }else{
                return jumpLinkPerfix+"/"+shortLinkTools.getId();
            }
        }
    }

    private String genShortLinkUrlEffectTime(ShortLinkTools shortLinkTools,
                                             String scene) throws WxErrorException, IOException {

        // 走box 短期 短链接
        String query = "";
        if(shortLinkTools.getParamsSettingType().equals("0")){
            if(shortLinkTools.getPath().contains("?")){
                query = shortLinkTools.getPath().substring(shortLinkTools.getPath().indexOf("?") + 1) + "&" + shortLinkTools.getParamsSettingContent();
            }
        }else{
            //
            if(shortLinkTools.getPath().contains("?")){
                query = shortLinkTools.getPath().substring(shortLinkTools.getPath().indexOf("?") + 1);
            }
        }
        // 如果设置了系统加参  和  页面  增加 参数到query中
        if(StringUtils.isBlank(query)){
            query = query +"pt="+shortLinkTools.getPageType();
        }else{
            query = query +"&pt="+shortLinkTools.getPageType();
        }
        query = query +"&sap="+shortLinkTools.getSystemAddParam();
        query = query +"&sahod="+shortLinkTools.getSystemAddHourOrDay();
        // scene不为空 拼接scene
        if(StringUtils.isNotBlank(scene)){
            query = query +"&scene="+scene;
        }

        if(shortLinkTools.getWeid().equals("10000")){

            GenerateShortUrlReq generateShortUrlReq = new GenerateShortUrlReq();
            if(StringUtils.isNotBlank(query)){
                generateShortUrlReq.setQuery(query);
            }
            if(shortLinkTools.getPath().contains("?")){
                generateShortUrlReq.setPath(shortLinkTools.getPath().substring(0,shortLinkTools.getPath().indexOf("?")));
            }else{
                generateShortUrlReq.setPath(shortLinkTools.getPath());
            }
            generateShortUrlReq.setExpire_type(1L);
            if(shortLinkTools.getEffectDays() != null){
                generateShortUrlReq.setExpire_interval(shortLinkTools.getEffectDays().longValue());
            }
            return generateShortUrlUtil.generateShortUrl(generateShortUrlReq);
//            return generateShortUrlUtil.generateShortUrl(pageUrl,shortLinkTools.getPageName());
        }else{
            // 走微商城  短期 短链接  远程调用 根据weid映射小程序appid
            List<BrandConfig> brandConfigs = JSONObject.parseArray(commonConfigs, BrandConfig.class);
            String appid = "";
            for (BrandConfig brandConfig : brandConfigs) {
                if(brandConfig.getWeid().equals(shortLinkTools.getWeid())){
                    // 获取appid
                    appid = brandConfig.getAppid();
                }
            }
            if(StringUtils.isBlank(appid)){
                return "";
            }
            Map<String,Object> map = new HashMap<>();

            if(StringUtils.isNotBlank(query)){
                map.put("query",query);
            }
            if(shortLinkTools.getSystemAddParam().equals("-1")){
                if(shortLinkTools.getPath().contains("?")){
                    map.put("path",shortLinkTools.getPath().substring(0,shortLinkTools.getPath().indexOf("?")));
                }else{
                    map.put("path",shortLinkTools.getPath());
                }
            }else{
                map.put("path","packages/wm-cloud-jnbytools/loginInfoPage/index");
            }

            map.put("appid",appid);
            map.put("expire_type",1);
            if(shortLinkTools.getEffectDays() != null){
                map.put("expireInterval",shortLinkTools.getEffectDays().longValue());
            }else{
                map.put("expireInterval",30);
            }
            // 存储生成短链的路径和参数
            String id = shortLinkTools.getId();
            if(StringUtils.isNotBlank(id)){
                // 更新
                Object pathObj = map.get("path");
                Object queryObj = map.get("query");
                if(pathObj !=null && queryObj != null){
                    String createShortLinkPathParam = pathObj.toString() + "?" + queryObj.toString();
                    ShortLinkTools updateShort = new ShortLinkTools();
                    updateShort.setId(id);
                    updateShort.setCreateShortLinkPathParam(createShortLinkPathParam);
                    updateShort.setUpdateTime(new Date());
                    shortLinkToolsMapper.updateById(updateShort);
                }
            }

            log.info("generateLink req = {}",JSONObject.toJSONString(map));
            try {
                Response<JICCommonVouResponse<String>> execute = ijicHttpApi.generateLink(appid,map).execute();
                if(execute.isSuccessful()){
                    JICCommonVouResponse<String> body = execute.body();
                    log.info("generateLink resp = {}",JSONObject.toJSONString(body));
                    return body.getData();
                }
            }catch (Exception e){
                log.info("generateShortLink = ",e);
            }

        }
        return "";
    }

}
