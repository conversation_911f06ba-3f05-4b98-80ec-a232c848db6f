package com.jnby.module.order;

import com.jnby.dto.BoxDetailReq;
import com.jnby.dto.BoxStatusUpdateReq;
import com.jnby.dto.GetProductInfoByBoxIdResp;

public interface IOrderService {
    /**
     * 获取商品信息
     */
    GetProductInfoByBoxIdResp getProductInfoByBoxId(BoxDetailReq req);

    void updateBoxStatusAndDetailStatus(BoxStatusUpdateReq req);

    /**
     * 修改主服务单状态
     * @param boxSn
     * @param beforeStatus
     * @param afterStatus
     */
    void updateBoxStatus(String boxSn, Long beforeStatus, Long afterStatus);
}
