package com.jnby.module.order;

import com.google.common.base.Preconditions;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.jnby.common.CommonConstant;
import com.jnby.common.enums.BoxRefundStatusEnum;
import com.jnby.common.enums.UltimaBoxDetailsStatusEnum;
import com.jnby.dto.BoxDetailReq;
import com.jnby.dto.BoxStatusUpdateReq;
import com.jnby.dto.GetProductInfoByBoxIdResp;
import com.jnby.entity.BoxDetailsWithChangeEntity;
import com.jnby.entity.BoxDetailsWithSupplyEntity;
import com.jnby.infrastructure.box.mapper.*;
import com.jnby.infrastructure.box.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderServiceImpl implements IOrderService {
    @Autowired
    private BoxMapper boxMapper;
    @Autowired
    private BoxDetailsMapper boxDetailsMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private BoxSupplyMapper boxSupplyMapper;
    @Autowired
    private ExpressMapper expressMapper;
    @Autowired
    private BoxRefundMapper boxRefundMapper;
    @Autowired
    private BoxRefundDetailsMapper boxRefundDetailsMapper;
    @Autowired
    private OrderDetailMapper orderDetailMapper;
    @Autowired
    @Qualifier("boxTransactionTemplate")
    private TransactionTemplate template;
    @Override
    public GetProductInfoByBoxIdResp getProductInfoByBoxId(BoxDetailReq req) {
        // 新增boxSn查询逻辑，如果boxSn
        String boxId = req.getBoxId();
        String boxSn = req.getBoxSn();
        BoxWithBLOBs box;
        if (StringUtils.isNotBlank(boxSn)) {
            box = boxMapper.selectByBoxSn(boxSn);
        } else {
            box = boxMapper.selectByPrimaryKey(boxId);
        }
        Preconditions.checkNotNull(box, "服务单不存在");
        boxId = box.getId();

        List<BoxDetailsWithBLOBs> boxDetailsWithBLOBsList = boxDetailsMapper.selectListByBoxId(boxId).stream().sorted(Comparator.comparing(BoxDetailsWithBLOBs::getProductNo)).collect(Collectors.toList());
        Order searchOrder = new Order();
        searchOrder.setBoxSn(box.getBoxSn());
        searchOrder.setOrderStatus(Long.valueOf(1));
        List<Order> orderList = orderMapper.selectListBySelective(searchOrder);
        List<BoxSupply> boxSupplyList = boxSupplyMapper.findByBoxId(boxId);

        boxSupplyList.sort(Comparator.comparing(BoxSupply::getCreateTime));

        // 补货字典表
        Map<String, BoxSupply> supplyMap = new LinkedHashMap<>();
        Multimap<String, BoxDetailsWithBLOBs> multiMap = ArrayListMultimap.create();
        Map<String, Express> expressMap = getExpressMapByBoxId(boxId);

        for (BoxSupply boxSupply : boxSupplyList) {
            supplyMap.put(boxSupply.getId(), boxSupply);
        }

        GetProductInfoByBoxIdResp getProductInfoByBoxIdResp = new GetProductInfoByBoxIdResp();
        int buyCount = 0;
        int backCount = 0;
        int sendCount = 0;
        BigDecimal dealPrice = new BigDecimal(0);

        for (Order order : orderList) {
            dealPrice = dealPrice.add(new BigDecimal(order.getPaidAmount()));
        }

        // 搭盒
        List<BoxDetailsWithBLOBs> createBoxList = new ArrayList<>();
        // 补货
        List<BoxDetailsWithSupplyEntity> supplyRespList = new ArrayList<>();
        // 换货
        List<BoxDetailsWithChangeEntity> changeCodeList = new ArrayList<>();
        // 待发货 删款 取消
        Set<Long> sendSet = new HashSet<>();
        sendSet.add(UltimaBoxDetailsStatusEnum.DELETE.getCode().longValue());
        sendSet.add(UltimaBoxDetailsStatusEnum.CANCEL.getCode().longValue());
        sendSet.add(UltimaBoxDetailsStatusEnum.UNSEND.getCode().longValue());


        for (BoxDetailsWithBLOBs temp : boxDetailsWithBLOBsList) {
            // 补货
            if (StringUtils.isNotEmpty(temp.getSupplyId()) && !temp.getType().equals(Long.valueOf(30))) {
                multiMap.put(temp.getSupplyId(), temp);
            }
            // 搭盒
            if (StringUtils.isEmpty(temp.getSupplyId()) && !Long.valueOf(30).equals(temp.getType())) {
                createBoxList.add(temp);
            }
            // 换货（已购买）
            if (Long.valueOf(30).equals(temp.getType()) && (Long.valueOf(UltimaBoxDetailsStatusEnum.PAY.getCode()).equals(temp.getStatus()) || Long.valueOf(CommonConstant.STR_ONE).equals(temp.getOrderStatus()))) {
                BoxDetailsWithChangeEntity changeEntity = new BoxDetailsWithChangeEntity();
                BeanUtils.copyProperties(temp, changeEntity);
                Express express = expressMap.get(temp.getExpressId());
                if (temp.getStatus() > 2 && express != null && StringUtils.isNotEmpty(express.getExpressDocno())) {
                    changeEntity.setEbsn(express.getExpressDocno());
                }
                changeCodeList.add(changeEntity);
            }
            // 发货数量 除待发货 删款 取消
            if (!sendSet.contains(temp.getStatus())) {
                sendCount += 1;
            }
            // 退换数量
            if (Long.valueOf(UltimaBoxDetailsStatusEnum.RETURNED.getCode()).equals(temp.getStatus())) {
                backCount += 1;
            }

            if (Long.valueOf(UltimaBoxDetailsStatusEnum.PAY.getCode()).equals(temp.getStatus()) || Long.valueOf(1).equals(temp.getOrderStatus())) {
                buyCount += 1;
            }
        }

        // 补货商品
        Map<String, Collection<BoxDetailsWithBLOBs>> boxDetailsWithSupplyMap = multiMap.asMap();
        // 处理补货 逻辑
        for (String key : supplyMap.keySet()) {
            BoxDetailsWithSupplyEntity temp = new BoxDetailsWithSupplyEntity();
            temp.setBoxSupply(supplyMap.get(key));
            temp.setBoxDetailsWithBLOBsList((List<BoxDetailsWithBLOBs>) boxDetailsWithSupplyMap.get(key));
            supplyRespList.add(temp);
        }

        // 退款个数  退款金额  根据 boxId  获取box   根据  boxsn 获取order  根据 order_id h获取boxRefund
        BoxWithBLOBs box1 = boxMapper.selectByPrimaryKey(boxId);

        List<BoxRefund> boxRefunds = boxRefundMapper.findByBoxSn(box1.getBoxSn());
        List<BoxRefund> collect = new ArrayList<>();

        Map<String, List<BoxRefund>> refundIdsGroupBy = new HashMap<>();
        if (CollectionUtils.isNotEmpty(boxRefunds)) {
            collect = boxRefunds.stream().filter(r -> BoxRefundStatusEnum.REFUNDSUCCESS.getCode().intValue() == r.getStatus()).collect(Collectors.toList());
            refundIdsGroupBy = collect.stream().collect(Collectors.groupingBy(r -> r.getId()));
        }
        BigDecimal refundAmount = new BigDecimal(0);
        int refundCount = 0;
        if (CollectionUtils.isNotEmpty(collect)) {
            // 获取退款金额  和 退款个数
            for (BoxRefund boxRefund : collect) {
                refundAmount = refundAmount.add(new BigDecimal(String.valueOf(boxRefund.getRefundAmount())));
            }

            //
            List<String> refundIds = collect.stream().map(r -> r.getId()).collect(Collectors.toList());
            List<String> orderIds = collect.stream().map(r -> r.getOrderId()).collect(Collectors.toList());

            //所有的退款单详情
            List<BoxRefundDetails> boxRefundDetails = boxRefundDetailsMapper.selectByRefundIds(refundIds);
            Map<String, List<BoxRefundDetails>> groupByOrderDetailsId = new HashMap<>();
            if (CollectionUtils.isNotEmpty(boxRefundDetails)) {
                groupByOrderDetailsId = boxRefundDetails.stream().collect(Collectors.groupingBy(r -> r.getOrderDetailId()));
            }

            //查询到所有订单详情
            List<OrderDetail> orderDetails = orderDetailMapper.selectListByOrderIds(orderIds);
            Map<String, List<OrderDetail>> groupByBoxDetalsId = new HashMap<>();
            if (CollectionUtils.isNotEmpty(orderDetails)) {
                groupByBoxDetalsId = orderDetails.stream().collect(Collectors.groupingBy(r -> r.getBoxDetailId()));
            }

            //循环遍历
            setBoxDetailWithBlobs(createBoxList, groupByOrderDetailsId, groupByBoxDetalsId, refundIdsGroupBy);
            setChangeBoxDetailWithBlobs(changeCodeList, groupByOrderDetailsId, groupByBoxDetalsId, refundIdsGroupBy);
            for (BoxDetailsWithSupplyEntity boxDetailsWithSupplyEntity : supplyRespList) {
                List<BoxDetailsWithBLOBs> boxDetailsWithBLOBsList1 = boxDetailsWithSupplyEntity.getBoxDetailsWithBLOBsList();
                setBoxDetailWithBlobs(boxDetailsWithBLOBsList1, groupByOrderDetailsId, groupByBoxDetalsId, refundIdsGroupBy);
            }
            refundCount = boxRefundDetails.size();
        }

        getProductInfoByBoxIdResp.setRefundAmount(refundAmount);
        getProductInfoByBoxIdResp.setRefundCount(refundCount);

        getProductInfoByBoxIdResp.setCreateBoxList(createBoxList);
        getProductInfoByBoxIdResp.setChangeCodeList(changeCodeList);
        getProductInfoByBoxIdResp.setSupplyRespList(supplyRespList);

        getProductInfoByBoxIdResp.setAllCount(boxDetailsWithBLOBsList.size());
        getProductInfoByBoxIdResp.setBackCount(backCount);
        getProductInfoByBoxIdResp.setBuyCount(buyCount);
        getProductInfoByBoxIdResp.setSendCount(sendCount);
        getProductInfoByBoxIdResp.setDealPrice(dealPrice);
        return getProductInfoByBoxIdResp;
    }

    @Override
    public void updateBoxStatusAndDetailStatus(BoxStatusUpdateReq req) {
        BoxWithBLOBs box = new BoxWithBLOBs();
        box.setId(req.getBoxId());
        box.setStatus(Long.valueOf(req.getStatus()));

        List<BoxStatusUpdateReq.BoxDetailStatusUpdateReq> subList = req.getSubList();
        List<BoxDetails> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(subList)) {
            for (BoxStatusUpdateReq.BoxDetailStatusUpdateReq req1 : subList) {
                BoxDetails boxDetails = new BoxDetails();
                boxDetails.setId(req1.getBoxDetailId());
                boxDetails.setStatus(Long.valueOf(req1.getBoxDetailStatus()));
                list.add(boxDetails);
            }
        }
        template.execute(action -> {
            boxMapper.updateByPrimaryKeySelective(box);
            if (CollectionUtils.isNotEmpty(list)) {
                boxDetailsMapper.batchUpdateById(list);
            }
            return true;
        });

    }

    private void setChangeBoxDetailWithBlobs(List<BoxDetailsWithChangeEntity> changeCodeList,
                                             Map<String, List<BoxRefundDetails>> groupByOrderDetailsId,
                                             Map<String, List<OrderDetail>> groupByBoxDetalsId,
                                             Map<String, List<BoxRefund>> refundIdsGroupBy) {
        for (BoxDetailsWithBLOBs boxDetailsWithBLOBs : changeCodeList) {
            List<OrderDetail> orderDetailList = groupByBoxDetalsId.get(boxDetailsWithBLOBs.getId());
            if (CollectionUtils.isNotEmpty(orderDetailList)) {
                for (OrderDetail orderDetail : orderDetailList) {
                    List<BoxRefundDetails> boxRefundDetails1 = groupByOrderDetailsId.get(orderDetail.getId());
                    if (CollectionUtils.isNotEmpty(boxRefundDetails1)) {
                        for (BoxRefundDetails refundDetails : boxRefundDetails1) {
                            // 获取sn
                            List<BoxRefund> refundList = refundIdsGroupBy.get(refundDetails.getBoxRefundId());
                            if (CollectionUtils.isNotEmpty(refundList)) {
                                BoxRefund boxRefund = refundList.get(0);
                                boxDetailsWithBLOBs.setBoxRefundRemark(boxRefund.getRefundRemark());
                                boxDetailsWithBLOBs.setBoxRefundSn(boxRefund.getRefundSn());
                            }
                            boxDetailsWithBLOBs.setBoxRefundDetailsStatus(refundDetails.getStatus());
                            boxDetailsWithBLOBs.setBoxRefundId(refundDetails.getBoxRefundId());
                        }
                    }
                }
            }
        }
    }

    private void setBoxDetailWithBlobs(List<BoxDetailsWithBLOBs> boxDetailsWithBLOBsList1,
                                       Map<String, List<BoxRefundDetails>> groupByOrderDetailsId,
                                       Map<String, List<OrderDetail>> groupByBoxDetalsId,
                                       Map<String, List<BoxRefund>> refundIdsGroupBy) {
        for (BoxDetailsWithBLOBs boxDetailsWithBLOBs : boxDetailsWithBLOBsList1) {
            List<OrderDetail> orderDetailList = groupByBoxDetalsId.get(boxDetailsWithBLOBs.getId());
            if (CollectionUtils.isNotEmpty(orderDetailList)) {
                for (OrderDetail orderDetail : orderDetailList) {
                    List<BoxRefundDetails> boxRefundDetails1 = groupByOrderDetailsId.get(orderDetail.getId());
                    if (CollectionUtils.isNotEmpty(boxRefundDetails1)) {
                        for (BoxRefundDetails refundDetails : boxRefundDetails1) {
                            // 获取sn
                            List<BoxRefund> refundList = refundIdsGroupBy.get(refundDetails.getBoxRefundId());
                            if (CollectionUtils.isNotEmpty(refundList)) {
                                BoxRefund boxRefund = refundList.get(0);
                                boxDetailsWithBLOBs.setBoxRefundRemark(boxRefund.getRefundRemark());
                                boxDetailsWithBLOBs.setBoxRefundSn(boxRefund.getRefundSn());
                            }
                            boxDetailsWithBLOBs.setBoxRefundDetailsStatus(refundDetails.getStatus());
                            boxDetailsWithBLOBs.setBoxRefundId(refundDetails.getBoxRefundId());
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取物流
     *
     * @param boxId
     * @return
     */
    public Map<String, Express> getExpressMapByBoxId(String boxId) {
        Express condition = new Express();
        condition.setBoxId(boxId);
        List<Express> expressList = expressMapper.selectListBySelective(condition);
        Map<String, Express> map = new HashMap<>();
        for (Express express : expressList) {
            map.put(express.getId(), express);
        }
        return map;
    }

    /**
     * 更新主单状态
     */
    @Override
    public void updateBoxStatus(String boxSn, Long beforeStatus, Long afterStatus) {
        BoxWithBLOBs box = new BoxWithBLOBs();
        box.setBoxSn(boxSn);
        BoxWithBLOBs existsBox = boxMapper.selectByBoxSn(boxSn);
        if (existsBox == null) {
            return;
        }
        boxMapper.updateByPrimaryKeyWithStatus(afterStatus, existsBox.getId(), beforeStatus);
    }
}
