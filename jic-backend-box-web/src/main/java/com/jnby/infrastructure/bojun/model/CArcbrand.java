package com.jnby.infrastructure.bojun.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("C_ARCBRAND")
@ApiModel(value = "CArcbrandVo对象", description = "")
public class CArcbrand implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("ID")
    private Long id;

    @TableField("AD_CLIENT_ID")
    private Long adClientId;

    @TableField("AD_ORG_ID")
    private Long adOrgId;

    @TableField("CODE")
    private String code;

    @TableField("NAME")
    private String name;

    @TableField("OWNERID")
    private Long ownerid;

    @TableField("MODIFIERID")
    private Long modifierid;

    @TableField("CREATIONDATE")
    private Date creationdate;

    @TableField("MODIFIEDDATE")
    private Date modifieddate;

    @TableField("ISACTIVE")
    private String isactive;

    @TableField("BRANDNAME")
    private String brandname;

    @TableField("IS_APPROVE")
    private String isApprove;


}
