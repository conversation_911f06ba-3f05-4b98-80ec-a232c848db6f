package com.jnby.infrastructure.bojun.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("USERS")
@ApiModel(value = "UsersVo对象", description = "")
public class UsersVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("ID")
    private Long id;

    @TableField("AD_CLIENT_ID")
    private Long adClientId;

    @TableField("AD_ORG_ID")
    private Long adOrgId;

    @TableField("ISACTIVE")
    private String isactive;

    @TableField("SUPERVISOR_ID")
    private Long supervisorId;

    @TableField("PA_GOALPRIVATE_ID")
    private Long paGoalprivateId;

    @TableField("C_BPARTNER_ID")
    private Long cBpartnerId;

    @TableField("PROCESSING")
    private String processing;

    @TableField("C_BPARTNER_LOCATION_ID")
    private Long cBpartnerLocationId;

    @TableField("C_GREETING_ID")
    private Long cGreetingId;

    @TableField("LASTCONTACT")
    private Integer lastcontact;

    @TableField("BIRTHDAY")
    private Integer birthday;

    @TableField("AD_ORGTRX_ID")
    private Long adOrgtrxId;

    @TableField("ISLDAPAUTHORIZED")
    private String isldapauthorized;

    @TableField("MODIFIERID")
    private Long modifierid;

    @TableField("CREATIONDATE")
    private Date creationdate;

    @TableField("MODIFIEDDATE")
    private Date modifieddate;

    @TableField("OWNERID")
    private Long ownerid;

    @TableField("PASSWORDHASH")
    private String passwordhash;

    @TableField("ISENABLED")
    private Integer isenabled;

    @TableField("ISEMPLOYEE")
    private Integer isemployee;

    @TableField("ISADMIN")
    private Integer isadmin;

    @TableField("USERTYPE")
    private Integer usertype;

    @TableField("TRUENAME")
    private String truename;

    @TableField("NAME")
    private String name;

    @TableField("DESCRIPTION")
    private String description;

    @TableField("PASSWORD")
    private String password;

    @TableField("EMAIL")
    private String email;

    @TableField("EMAILUSER")
    private String emailuser;

    @TableField("EMAILUSERPW")
    private String emailuserpw;

    @TableField("TITLE")
    private String title;

    @TableField("COMMENTS")
    private String comments;

    @TableField("PHONE")
    private String phone;

    @TableField("PHONE2")
    private String phone2;

    @TableField("FAX")
    private String fax;

    @TableField("LASTRESULT")
    private String lastresult;

    @TableField("EMAILVERIFY")
    private String emailverify;

    @TableField("C_DEPARTMENT_ID")
    private Long cDepartmentId;

    @TableField("LANGUAGE")
    private String language;

    @TableField("IS_OTP")
    private String isOtp;

    @TableField("OTP_LENGTH")
    private Integer otpLength;

    @TableField("OTP_SECONDS")
    private Integer otpSeconds;

    @TableField("OTP_SECRET")
    private String otpSecret;

    @TableField("OTP_COUNTER")
    private Long otpCounter;

    @TableField("IS_OTP_ONLY")
    private String isOtpOnly;

    @TableField("OTP_CDATE")
    private Date otpCdate;

    @TableField("LOGIN_IP_RULE")
    private String loginIpRule;

    @TableField("ISSMS")
    private String issms;

    @TableField("IS_OUT")
    private String isOut;

    @TableField("ASSIGNEE_ID")
    private Long assigneeId;

    @TableField("ISSALER")
    private String issaler;

    @TableField("C_STORE_ID")
    private Long cStoreId;

    @TableField("DISCOUNTLIMIT")
    private BigDecimal discountlimit;

    @TableField("ISOPR")
    private String isopr;

    @TableField("SAASVENDOR")
    private String saasvendor;

    @TableField("SAASUSER")
    private String saasuser;

    @TableField("HR_EMPLOYEE_ID")
    private Long hrEmployeeId;

    @TableField("C_CUSTOMER_ID")
    private Long cCustomerId;

    @TableField("C_CUSTOMERUP_ID")
    private Long cCustomerupId;

    @TableField("AREAMNG_ID")
    private Long areamngId;

    @TableField("SGRADE")
    private Integer sgrade;

    @TableField("ISOVERSEAS")
    private String isoverseas;

    @ApiModelProperty(value = "用户可以访问的子系统，用逗号分隔标识，空表示读取权限配置。主要用于仅显示自定义的子系统来美化显示效果")
    @TableField("SUBSYSTEMS")
    private String subsystems;

    @TableField("WEBPOS_PER")
    private String webposPer;

    @TableField("C_SUPPLIER_ID")
    private Long cSupplierId;

    @TableField("ISRET")
    private String isret;

    @TableField("MAC")
    private String mac;

    @TableField("IS_CLOSE")
    private String isClose;

    @TableField("C_AREA_ID")
    private Long cAreaId;

    @TableField("C_CITY_ID")
    private Long cCityId;

    @TableField("PASSWORDEXPIRATIONDATE")
    private Date passwordexpirationdate;

    @ApiModelProperty(value = "HR用户ID")
    @TableField("HR_PSN_ID")
    private Long hrPsnId;

    @TableField("PASSWORDRESET")
    private String passwordreset;

    @TableField("QNVL_VIP")
    private String qnvlVip;

    @TableField("IS_CAN_EBSOOUT")
    private String isCanEbsoout;

    @TableField("WEBPOS20_PER")
    private String webpos20Per;

    @TableField("CHECKCODE")
    private String checkcode;

    @TableField("CHECKDATE")
    private Date checkdate;

    @TableField("IS_WXMESSAGE_PUSH")
    private String isWxmessagePush;

    @TableField("C_PRICEAREA_ID")
    private Long cPriceareaId;

    @TableField("C_PRICEREGION_ID")
    private Long cPriceregionId;

    @TableField("PRICE_REVISION")
    private String priceRevision;

    @TableField("DISCOUNTLIMIT_NEW")
    private BigDecimal discountlimitNew;

    @TableField("BPOS_SPECIAL_LANGUAGE")
    private String bposSpecialLanguage;

    @TableField("DISCOUNTMAX")
    private BigDecimal discountmax;

    @TableField("BPOSDATE")
    private Long bposdate;

    @TableField("IS_TORFID")
    private String isTorfid;

    @TableField("OLD_NAME")
    private String oldName;

    @TableField("C_MAINSTORE_ID")
    private Long cMainstoreId;

    @TableField("MODIFY_TOTAL_AMOUNT_LIMIT")
    private Long modifyTotalAmountLimit;

    @TableField("IS_TODD")
    private String isTodd;

    @TableField("IS_UPUSERPASSWORD")
    private String isUpuserpassword;

    @TableField("DEPARTMENT")
    private String department;

    @TableField("OACODE")
    private String oacode;


}
