package com.jnby.infrastructure.bojun.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("C_AREA1")
@ApiModel(value="CAreaVo对象", description="")
public class CAreaVo1 implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId("ID")
    private Long id;

    @TableField("AD_CLIENT_ID")
    private Long adClientId;

    @TableField("AD_ORG_ID")
    private Long adOrgId;

    @TableField("MODIFIERID")
    private Long modifierid;

    @TableField("CREATIONDATE")
    private Date creationdate;

    @TableField("MODIFIEDDATE")
    private Date modifieddate;

    @TableField("OWNERID")
    private Long ownerid;

    @TableField("ISACTIVE")
    private String isactive;

    @ApiModelProperty(value = "名称")
    @TableField("NAME")
    private String name;



}
