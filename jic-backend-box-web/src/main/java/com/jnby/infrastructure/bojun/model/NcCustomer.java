package com.jnby.infrastructure.bojun.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName NC_CUSTOMER
 */
@TableName(value ="NC_CUSTOMER")
@Data
public class NcCustomer implements Serializable {
    /**
     * 
     */
    @TableId(value = "ID")
    private Integer id;

    /**
     * 
     */
    @TableField(value = "NC_ACCOUNT_ID")
    private Integer ncAccountId;

    /**
     * 
     */
    @TableField(value = "CUSTCODE")
    private String custcode;

    /**
     * 
     */
    @TableField(value = "CUSTNAME")
    private String custname;

    /**
     * 
     */
    @TableField(value = "AD_CLIENT_ID")
    private Integer adClientId;

    /**
     * 
     */
    @TableField(value = "AD_ORG_ID")
    private Integer adOrgId;

    /**
     * 
     */
    @TableField(value = "OWNERID")
    private Integer ownerid;

    /**
     * 
     */
    @TableField(value = "MODIFIERID")
    private Integer modifierid;

    /**
     * 
     */
    @TableField(value = "CREATIONDATE")
    private Date creationdate;

    /**
     * 
     */
    @TableField(value = "MODIFIEDDATE")
    private Date modifieddate;

    /**
     * 
     */
    @TableField(value = "ISACTIVE")
    private String isactive;

    /**
     * 
     */
    @TableField(value = "ISNEW")
    private String isnew;

    /**
     * 
     */
    @TableField(value = "KSMC")
    private String ksmc;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}