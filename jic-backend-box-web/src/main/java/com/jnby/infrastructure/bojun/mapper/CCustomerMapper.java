package com.jnby.infrastructure.bojun.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jnby.dto.bojun.CustomerDto;
import com.jnby.infrastructure.bojun.model.CCustomerVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface CCustomerMapper extends BaseMapper<CCustomerVo> {


    List<CustomerDto> selectByCodes(@Param("codes") List<String> codes);

    List<CustomerDto> selectByCodesAll(@Param("codes") List<String> codes);

    /**
     * 经销商自增存储过程调用 C_CUSTOMER_AC/C_CUSTOMER_AM
     *
     * @param map 经销商主键
     */
    void insertIntoCustomerChildTableAc(Map<String, Object> map);

    /**
     * 经销商自增存储过程调用 C_CUSTOMER_AC/C_CUSTOMER_AM
     *
     * @param map 经销商主键
     */
    void insertIntoCustomerChildTableAm(Map<String, Object> map);

    Long getSequencesByTableName(@Param("tableName") String tableName);

    Long getSequences(@Param("seqName") String seqName);


    CustomerDto getCustomreById (@Param("id") String id);

    void insertIntoStoreChildTableAc(Map<String, Object> map);

    /**
     * 店仓新增数据存储过程调用 C_STORE_AC/C_STORE_AM
     *
     * @param map 经销商主键
     */
    void insertIntoStoreChildTableAm(Map<String, Object> map);
}
