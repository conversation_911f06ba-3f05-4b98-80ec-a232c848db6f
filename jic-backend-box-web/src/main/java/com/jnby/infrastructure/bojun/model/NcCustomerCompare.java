package com.jnby.infrastructure.bojun.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName NC_CUSTOMER_COMPARE
 */
@TableName(value ="NC_CUSTOMER_COMPARE")
@Data
public class NcCustomerCompare implements Serializable {
    /**
     * 
     */
    @TableId(value = "ID")
    private Integer id;

    /**
     * 
     */
    @TableField(value = "NC_ACCOUNT_ID")
    private Integer ncAccountId;

    /**
     * 
     */
    @TableField(value = "NC_CUSTOMER_ID")
    private Integer ncCustomerId;

    /**
     * 
     */
    @TableField(value = "C_CUSTOMER_ID")
    private Integer cCustomerId;

    /**
     * 
     */
    @TableField(value = "AD_CLIENT_ID")
    private Integer adClientId;

    /**
     * 
     */
    @TableField(value = "AD_ORG_ID")
    private Integer adOrgId;

    /**
     * 
     */
    @TableField(value = "OWNERID")
    private Integer ownerid;

    /**
     * 
     */
    @TableField(value = "MODIFIERID")
    private Integer modifierid;

    /**
     * 
     */
    @TableField(value = "CREATIONDATE")
    private Date creationdate;

    /**
     * 
     */
    @TableField(value = "MODIFIEDDATE")
    private Date modifieddate;

    /**
     * 
     */
    @TableField(value = "ISACTIVE")
    private String isactive;

    /**
     * 
     */
    @TableField(value = "C_INVOCEGROUPMODE_ID")
    private Integer cInvocegroupmodeId;

    /**
     * 
     */
    @TableField(value = "KS")
    private String ks;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}