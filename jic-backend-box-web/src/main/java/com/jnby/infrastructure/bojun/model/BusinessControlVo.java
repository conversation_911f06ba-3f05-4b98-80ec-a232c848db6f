package com.jnby.infrastructure.bojun.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("BUSINESS_CONTROL")
@ApiModel(value = "BusinessControlVo对象", description = "")
public class BusinessControlVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一编号")
    @TableId("ID")
    private Long id;

    @ApiModelProperty(value = "业务名称")
    @TableField("BUSINESS_NAME")
    private String businessName;

    @ApiModelProperty(value = "业务类型")
    @TableField("BUSINESS_TYPE")
    private String businessType;

    @ApiModelProperty(value = "1 开启 0关闭")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty(value = "业务创建时间")
    @TableField("CREATE_TIME")
    private Date createTime;

    @ApiModelProperty(value = "业务备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "业务ID")
    @TableField("BUSINESS_ID")
    private Long businessId;

    @ApiModelProperty(value = "执行业务SQL语句")
    @TableField("BUSINESS_SQL")
    private String businessSql;

    @ApiModelProperty(value = "业务更新时间")
    @TableField("UPDATE_TIME")
    private Date updateTime;


}
