package com.jnby.infrastructure.bojun.mapper;

import com.jnby.dto.StoreCustomerReq;
import com.jnby.dto.StoreCustomerResp;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jnby.dto.bojun.CstoreBrandDto;
import com.jnby.dto.bojun.CstoreSumDto;
import com.jnby.infrastructure.bojun.model.CStoreVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CStoreMapper extends BaseMapper<CStoreVo> {

    List<CstoreSumDto> selectCountStoreByCustomerIds(@Param("customerIds") List<Long> customerIds);

    List<StoreCustomerResp> selectByParams(StoreCustomerReq requestData);

    List<CstoreBrandDto> listStoreByStoreId(@Param("storeIds") List<Long> storeIds);

    List<CstoreBrandDto> listStoreAndBrandNameByUnionStoreId(@Param("unionStoreId") Long unionStoreId);

}
