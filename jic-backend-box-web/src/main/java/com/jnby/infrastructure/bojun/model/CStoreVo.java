package com.jnby.infrastructure.bojun.model;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-18
 */
@Data
@TableName("C_STORE")
@ApiModel(value = "CStoreVo对象", description = "")
public class CStoreVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private String countCode;

    @TableId("ID")
    private Long id;

    /**
     * 是否特卖
     */
    @TableField("IS_TM")
    private String isTm;

    @TableField("AD_CLIENT_ID")
    private Long adClientId;

    @TableField("AD_ORG_ID")
    private Long adOrgId;

    @TableField("ISACTIVE")
    private String isactive;

    @TableField("MODIFIERID")
    private Long modifierid;

    @TableField("CREATIONDATE")
    private Date creationdate;

    @TableField("MODIFIEDDATE")
    private Date modifieddate;

    @TableField("OWNERID")
    private Long ownerid;

    @TableField("NAME")
    private String name;

    @TableField("DESCRIPTION")
    private String description;

    @TableField("C_AREA_ID")
    private Long cAreaId;

    @TableField("LOCKCASH")
    private Long lockcash;

    @TableField("ADDRESS")
    private String address;

    @TableField("PHONE")
    private String phone;

    @TableField("FAX")
    private String fax;

    @TableField("CONTACTOR_ID")
    private Long contactorId;

    /**
     * 月租金
     */
    @TableField("MONTHFEE")
    private BigDecimal monthfee;

    @TableField("ISSTOP")
    private String isstop;

    @TableField("RENTBEGIN")
    private String rentbegin;

    @TableField("RENTEND")
    private String rentend;

    @TableField("PROPORTION")
    private String proportion;

    @TableField("EMPCNT")
    private String empcnt;

    @TableField("CHECKDATE")
    private Integer checkdate;

    @TableField("ISCENTER")
    private String iscenter;

    @TableField("ISRETAIL")
    private String isretail;

    @TableField("MOBIL")
    private String mobil;

    @TableField("SNAME")
    private String sname;

    @TableField("POSTCAL")
    private Long postcal;

    @TableField("CALCULATION")
    private String calculation;

    @TableField("C_CUSTOMER_ID")
    private Long cCustomerId;

    @TableField("C_CUSTOMERUP_ID")
    private Long cCustomerupId;

    @TableField("C_PRICEAREA_ID")
    private Long cPriceareaId;

    @TableField("ISFAIRORIG")
    private String isfairorig;

    @TableField("AREAMNG_ID")
    private Long areamngId;

    @TableField("LIMITQTY")
    private Long limitqty;

    /**
     * 金额限制
     */
    @TableField("LIMITAMT")
    private BigDecimal limitamt;

    @TableField("LIMITMO")
    private String limitmo;

    /**
     * 默认商场扣率 NUMBER(18,3)
     */
    @TableField("MARKDIS")
    private BigDecimal markdis;

    @TableField("DATEBLOCK")
    private Integer dateblock;

    @TableField("C_STORETYPE_JZ_ID")
    private Long cStoretypeJzId;

    @TableField("IMGURL1")
    private String imgurl1;

    @TableField("IMGURL2")
    private String imgurl2;

    @TableField("IMGURL3")
    private String imgurl3;

    @TableField("IMGURL4")
    private String imgurl4;

    @TableField("IMGURL5")
    private String imgurl5;

    @TableField("BIGAREAMNG_ID")
    private Long bigareamngId;

    @TableField("C_PROVINCE_ID")
    private Long cProvinceId;

    @TableField("C_CITY_ID")
    private Long cCityId;

    @TableField("STORESIGN")
    private Long storesign;

    @TableField("C_STORETYPE")
    private String cStoretype;

    @TableField("REMARK")
    private String remark;

    @TableField("CODE")
    private String code;

    @TableField("ISUFSTORE")
    private String isufstore;

    @TableField("C_STORE_ID")
    private Long cStoreId;

    /**
     * 特卖需求，所属店仓
     */
    @TableField("C_STOREGS_ID")
    private Long cStoreGsId;

    @TableField("C_DEPARTMENT_ID")
    private Long cDepartmentId;

    @TableField("C_CLASSCODE_ID")
    private Long cClasscodeId;

    @TableField("UF_CODE")
    private String ufCode;

    @TableField("BILLDATE_FRIST")
    private Integer billdateFrist;

    @TableField("PRIORITY")
    private Long priority;

    @TableField("C_BLOCK_ID")
    private Long cBlockId;

    @TableField("ISFICTITIOUS")
    private String isfictitious;

    @TableField("SHOP_RECEIVE_TYPE")
    private Integer shopReceiveType;

    @TableField("POSPW")
    private String pospw;

    @TableField("ISBLOCK")
    private String isblock;

    @TableField("CLOPSTORETYPE")
    private String clopstoretype;

    /**
     * 子公司折扣
     */
    @TableField("DISCOUNT")
    private BigDecimal discount;

    @TableField("CLOP_STORE")
    private String clopStore;

    @TableField("ISGIFT")
    private String isgift;

    @TableField("ISDISCOM")
    private String isdiscom;

    @TableField("C_DEPART_ID")
    private Long cDepartId;

    @TableField("IMP_MONTH")
    private Long impMonth;

    @TableField("IMP_TYPE1")
    private Long impType1;

    @TableField("IMP_TYPE2")
    private Long impType2;

    @TableField("IMP_TYPE3")
    private Long impType3;

    /**
     * 税率
     */
    @TableField("TAXRATE")
    private BigDecimal taxrate;

    /**
     * 城市级别
     */
    @TableField("C_STOREATTRIB1_ID")
    private Long cStoreattrib1Id;

    @TableField("C_STOREATTRIB2_ID")
    private Long cStoreattrib2Id;

    @TableField("C_STOREATTRIB3_ID")
    private Long cStoreattrib3Id;

    @TableField("C_STOREATTRIB4_ID")
    private Long cStoreattrib4Id;

    @TableField("C_STOREATTRIB5_ID")
    private Long cStoreattrib5Id;

    @TableField("C_STOREATTRIB6_ID")
    private Long cStoreattrib6Id;

    @TableField("C_STOREATTRIB7_ID")
    private Long cStoreattrib7Id;

    @TableField("C_STOREATTRIB8_ID")
    private Long cStoreattrib8Id;

    @TableField("C_STOREATTRIB9_ID")
    private Long cStoreattrib9Id;

    @TableField("C_STOREATTRIB10_ID")
    private Long cStoreattrib10Id;

    @TableField("C_STOREATTRIB11_ID")
    private Long cStoreattrib11Id;

    @TableField("C_STOREATTRIB12_ID")
    private Long cStoreattrib12Id;

    @TableField("C_STOREATTRIB13_ID")
    private Long cStoreattrib13Id;

    @TableField("C_STOREATTRIB14_ID")
    private Long cStoreattrib14Id;

    @TableField("C_STOREATTRIB15_ID")
    private Long cStoreattrib15Id;

    @TableField("C_STOREATTRIB16_ID")
    private Long cStoreattrib16Id;

    @TableField("C_STOREATTRIB17_ID")
    private Long cStoreattrib17Id;

    @TableField("C_STOREATTRIB18_ID")
    private Long cStoreattrib18Id;

    @TableField("C_STOREATTRIB19_ID")
    private Long cStoreattrib19Id;

    @TableField("C_STOREATTRIB20_ID")
    private Long cStoreattrib20Id;

    @TableField("ISNEGATIVE")
    private String isnegative;

    @TableField("TDEFDOWNTYPE_ID")
    private Long tdefdowntypeId;

    @TableField("C_QTYADDAREA_ID")
    private Long cQtyaddareaId;

    @TableField("C_CORP_ID")
    private Long cCorpId;

    @TableField("USBKEY")
    private String usbkey;

    @TableField("IFEBSTORE")
    private String ifebstore;

    @TableField("DATE_ENDACCOUNT")
    private Integer dateEndaccount;

    @TableField("ISSTOCK")
    private String isstock;

    @TableField("Y_STORE")
    private String yStore;

    @TableField("C_VIPTYPE_ID1")
    private String cViptypeId1;

    @TableField("IF_WMS")
    private String ifWms;

    @TableField("IFORDERSTORE")
    private String iforderstore;

    @TableField("ORDERLIMITDATE")
    private Long orderlimitdate;

    @TableField("WEBPOSLOGINURL")
    private String webposloginurl;

    @TableField("COMPTYPE")
    private Long comptype;

    @TableField("C_STORE_SQL")
    private String cStoreSql;

    @TableField("M_DIM1_ID")
    private Long mDim1Id;

    @TableField("STORETYPE")
    private String storetype;

    @TableField("RETCHKORG")
    private String retchkorg;

    @TableField("MARKET")
    private String market;

    @TableField("C_STOREGRADE_ID")
    private Long cStoregradeId;

    @TableField("C_STOREKIND_ID")
    private Long cStorekindId;

    @TableField("C_INTEGRALAREA_ID")
    private Long cIntegralareaId;

    @TableField("FRAMWORK_AREA_ID")
    private Long framworkAreaId;

    @TableField("C_ARCBRAND_ID")
    private Long cArcbrandId;

    @TableField("IS_RESTORE")
    private String isRestore;

    @TableField("IS_MARK")
    private String isMark;

    @TableField("IS_RET")
    private String isRet;

    @TableField("WEBPOS_OFFLINE")
    private String webposOffline;

    @TableField("LOWEST_DISCOUNT")
    private String lowestDiscount;

    @TableField("CHK_OVERDAYS")
    private Long chkOverdays;

    @TableField("C_MARKBALTYPE_ID")
    private Long cMarkbaltypeId;

    @TableField("ISVIPINTL")
    private String isvipintl;

    @TableField("ISVIPDIS")
    private String isvipdis;

    @TableField("ISONLYCARD")
    private String isonlycard;

    @TableField("C_PAYWAY_DEFAULT")
    private Long cPaywayDefault;

    @TableField("CREDITLIMIT")
    private Long creditlimit;

    @TableField("EB_CREDITRANK_ID")
    private Long ebCreditrankId;

    @TableField("EB_BONUSTYPE_ID")
    private Long ebBonustypeId;

    @TableField("IS_TAOBAO")
    private String isTaobao;

    @TableField("EB_SHIPTYPE_ID")
    private Long ebShiptypeId;

    @TableField("SHELFDEPTH")
    private Long shelfdepth;

    @TableField("IS_MORESALESREP")
    private String isMoresalesrep;

    @TableField("DIM1_FILTER")
    private String dim1Filter;

    @TableField("C_POSADDR_ID")
    private Long cPosaddrId;

    @TableField("POSPRINT")
    private String posprint;

    @TableField("IS_MODIFYPAYAMT")
    private String isModifypayamt;

    @TableField("OPENDATE")
    private Integer opendate;

    @TableField("LEASEPERIOD")
    private String leaseperiod;

    @TableField("ENDSALE")
    private String endsale;

    @TableField("USEMONTH")
    private String usemonth;

    @TableField("CONTRACT")
    private String contract;

    @TableField("ENDDATE")
    private Integer enddate;

    @TableField("RENCOST")
    private String rencost;

    @TableField("BEARCOMPANY_ID")
    private Long bearcompanyId;

    @TableField("CHARGEOF_ID")
    private Long chargeofId;

    @TableField("ALIPAY_KEY")
    private String alipayKey;

    @TableField("ALIPAY_PARTNERID")
    private String alipayPartnerid;

    @TableField("ALIPAY_SELL_MAIL")
    private String alipaySellMail;

    @TableField("IS_MARKETNO")
    private String isMarketno;

    @TableField("IS_MANUALINT")
    private String isManualint;

    @TableField("CONTACTOR")
    private String contactor;

    @TableField("BILLDATERANGE")
    private String billdaterange;

    @TableField("LONGITUDE")
    private BigDecimal longitude;

    @TableField("LATITUDE")
    private BigDecimal latitude;

    @TableField("IS_CREATEDATA")
    private String isCreatedata;

    @TableField("IS_TONC")
    private String isTonc;

    @TableField("IS_COMPARED")
    private String isCompared;

    @TableField("PREOPENDATE")
    private Integer preopendate;

    @TableField("C_STOREATTRIB21_ID")
    private Long cStoreattrib21Id;

    @TableField("IS_PROCLOSE")
    private String isProclose;

    @TableField("IS_CHECKALIAS")
    private String isCheckalias;

    @TableField("C_CONSUMEAREA_ID")
    private Long cConsumeareaId;

    @TableField("IS_COUNTER")
    private String isCounter;

    @TableField("IS_EXCSTORE")
    private String isExcstore;

    @TableField("ORGMODIFYPER")
    private String orgmodifyper;

    @TableField("C_DISTRICT_ID")
    private Long cDistrictId;

    @TableField("WECHAT_CUSTOMERID")
    private String wechatCustomerid;

    @TableField("SUB_MCH_ID")
    private String subMchId;

    @TableField("ISAIXIU")
    private String isaixiu;

    @TableField("IS_SMARTPAY")
    private String isSmartpay;

    @ApiModelProperty(value = "HR组织ID")
    @TableField("HR_GROUP_ID")
    private Long hrGroupId;

    @TableField("EB_STORAGE_TYPE")
    private Integer ebStorageType;

    @TableField("IS_JSTYPE")
    private String isJstype;

    @TableField("IS_BCLOUD_STORE")
    private String isBcloudStore;

    @TableField("RETAIL_RET_DAY")
    private Long retailRetDay;

    @TableField("Q_NVL")
    private String qNvl;

    @TableField("C_MALL_ID")
    private Long cMallId;

    @TableField("Q_PASSWORD")
    private String qPassword;

    @TableField("Q_SMS")
    private String qSms;

    @TableField("Q_CODE")
    private String qCode;

    @TableField("C_BIGAREA_ID")
    private Long cBigareaId;

    @TableField("IS_BANKBUY")
    private String isBankbuy;

    @TableField("MAINMEDIAADDRESS")
    private String mainmediaaddress;

    /**
     * 找零最大币值
     */
    @TableField("CHANGE")
    private Long change;

    @TableField("CURRENCY")
    private String currency;

    @TableField("CURRENCY_SIGN")
    private String currencySign;

    @TableField("ISRETPAY")
    private String isretpay;

    @TableField("SF_CARDNO")
    private String sfCardno;

    @TableField("C_PAYWAY_FILTER")
    private String cPaywayFilter;

    @TableField("IS_ALLPAYWAY")
    private String isAllpayway;

    @TableField("IS_DISSTORE")
    private String isDisstore;

    @TableField("IS_UNIONSTORE")
    private String isUnionstore;

    @TableField("RETAIL_LOCATION")
    private String retailLocation;

    @TableField("IS_MORECURRENCY")
    private String isMorecurrency;

    @TableField("C_CURRENCY_ID")
    private Long cCurrencyId;

    @TableField("POS_AUTO_DIS")
    private String posAutoDis;

    @TableField("SUBSYSTEM_NAME")
    private String subsystemName;

    @TableField("MENU_LIST")
    private String menuList;

    @TableField("ISRET_LEVEL_ON")
    private String isretLevelOn;

    @TableField("ISPADPOS")
    private String ispadpos;

    @TableField("ISKEEP_PWD")
    private String iskeepPwd;

    @TableField("DATE_HOUR_OFFSET")
    private Integer dateHourOffset;

    @TableField("POS_SERIALNO")
    private String posSerialno;

    @TableField("IS_HPMART")
    private String isHpmart;

    @TableField("SHOPID")
    private String shopid;

    @TableField("SHOPCODE")
    private String shopcode;

    @TableField("PRODUCTCODE")
    private String productcode;

    @TableField("Q_CODE_POS")
    private String qCodePos;

    @TableField("GUESTMACHINECOM")
    private String guestmachinecom;

    @TableField("GUESTMACHINE")
    private Integer guestmachine;

    @TableField("ISMUSTENTERVIP")
    private String ismustentervip;

    @TableField("CHKDAY")
    private Integer chkday;

    @TableField("MALLCODE")
    private String mallcode;

    @TableField("COUNTERNUM")
    private String counternum;

    @TableField("EB_EXPRESS_ID")
    private Long ebExpressId;

    @TableField("SDTSTORE")
    private String sdtstore;

    @TableField("IS_BPAYSHOWVOUCHER")
    private String isBpayshowvoucher;

    @TableField("WECHAT_CUSTOMERIDNEW")
    private String wechatCustomeridnew;

    @TableField("C_COUNTRY_ID")
    private Long cCountryId;

    @TableField("IS_O2O")
    private String isO2o;

    @TableField("ALLOW_RECEIPT")
    private String allowReceipt;

    @TableField("IS_MODIFYAMT_REASON")
    private String isModifyamtReason;

    @TableField("BPOSEX")
    private String bposex;

    @TableField("ISSDT")
    private String issdt;

    @TableField("DXLX")
    private String dxlx;

    @TableField("C_UNIONSTORE_ID")
    private Long cUnionstoreId;

    @TableField("IS_AUTOIN")
    private String isAutoin;

    @TableField("DEFAULT_CHARGEPAYWAY")
    private Long defaultChargepayway;

    @TableField("DEFAULTLASTDATE")
    private String defaultlastdate;

    @TableField("INVOICE_TEMPLATE")
    private String invoiceTemplate;

    @TableField("IS_SHOWVISITPLAN")
    private String isShowvisitplan;

    @TableField("INVOICE_TAXNO")
    private String invoiceTaxno;

    @TableField("CHECKDATADOWN")
    private Integer checkdatadown;

    @TableField("PORDERLIMITDATE")
    private Long porderlimitdate;

    @TableField("IS_CHECKSKUONLINE")
    private String isCheckskuonline;

    @TableField("COMBINEAFTER")
    private String combineafter;

    @TableField("IS_REFRESHNETWORK")
    private String isRefreshnetwork;

    @TableField("IS_FORCEBPOS")
    private String isForcebpos;

    @TableField("DESCRIPTION1")
    private String description1;

    @TableField("IS_OXO")
    private String isOxo;

    @TableField("JIT_STORECODE")
    private String jitStorecode;

    @TableField("C_DISTR__YXJ_ID")
    private Long cDistrYxjId;

    @TableField("IS_FILTERVIPTYPE")
    private String isFilterviptype;

    @TableField("IS_VIP_CHECK")
    private String isVipCheck;

    @TableField("IS_BRITHDAYDIS")
    private String isBrithdaydis;

    @TableField("BROWSER_OPEN_MODE")
    private String browserOpenMode;

    @TableField("IS_INTEGRAL_BASE")
    private String isIntegralBase;

    @TableField("IS_ORDER")
    private String isOrder;

    @TableField("CAN_GRAB")
    private String canGrab;

    @TableField("IS_PERFORMANCE_SHARE")
    private String isPerformanceShare;

    @TableField("MORLTHENOTE")
    private String morlthenote;

    @TableField("ISMORLTHENOTEP")
    private String ismorlthenotep;

    @TableField("IS_CONTROL")
    private String isControl;

    @TableField("IS_PRINT_ELECTRONICINVOICE")
    private String isPrintElectronicinvoice;

    @TableField("OPENVIP_AMT")
    private Long openvipAmt;

    @TableField("MORDERLIMITDATE")
    private Long morderlimitdate;

    @TableField("PRINTTPLS4SGLPDT")
    private String printtpls4sglpdt;

    @TableField("INVOICE_PHONE")
    private String invoicePhone;

    @TableField("INVOICE_ACCOUNT")
    private String invoiceAccount;

    @TableField("INVOICE_BANK")
    private String invoiceBank;

    @TableField("INVOICE_ADDR")
    private String invoiceAddr;

    @TableField("INVOICE_COM")
    private String invoiceCom;

    @TableField("MALL_NAME")
    private String mallName;

    @TableField("MALL_NO")
    private String mallNo;

    @TableField("MALL_ADDRESS")
    private String mallAddress;

    @TableField("IS_STONO")
    private String isStono;

    @TableField("IS_WMSSTORE")
    private String isWmsstore;

    @TableField("WMS_STORECODE")
    private String wmsStorecode;

    @TableField("IS_TOWMS")
    private String isTowms;

    @TableField("IS_MARKET")
    private String isMarket;

    @TableField("C_REALSTORE_ID")
    private Long cRealstoreId;

    @TableField("DESCRIPTION01")
    private String description01;

    @TableField("DESCRIPTION02")
    private String description02;

    @TableField("DESCRIPTION03")
    private String description03;

    @TableField("DESCRIPTION04")
    private String description04;

    @TableField("DESCRIPTION05")
    private String description05;

    @TableField("MERCHANT_NUMBER")
    private String merchantNumber;

    @TableField("PAYMENT_KEY")
    private String paymentKey;

    @TableField("IS_DELIVERY")
    private String isDelivery;

    @TableField("DESKEY")
    private String deskey;

    @TableField("CARRYMODE")
    private String carrymode;

    @TableField("IS_SELECTPRINT")
    private String isSelectprint;

    @TableField("PRINTTEMPLATELIST")
    private String printtemplatelist;

    @TableField("IS_EXAMINATION")
    private String isExamination;

    @TableField("SHOUQIANBACODE")
    private String shouqianbacode;

    @TableField("WECHAT_CUSTOMERIDNEW2")
    private String wechatCustomeridnew2;

    @TableField("SHOUQIANBA_USE")
    private String shouqianbaUse;

    @TableField("O2OVOICE")
    private String o2ovoice;

    @TableField("PENDVOICE")
    private String pendvoice;

    @TableField("MERCHANT")
    private String merchant;

    @TableField("VIPPRINTTEMPLATE")
    private String vipprinttemplate;

    @TableField("JTK")
    private String jtk;

    @TableField("RECHARGE")
    private String recharge;

    @TableField("COMFIRM_BEFOREPAY")
    private String comfirmBeforepay;

    @TableField("MONITOR_URL")
    private String monitorUrl;

    @TableField("MANAGERNAV_OPEN_MODE")
    private String managernavOpenMode;

    @TableField("VOUCHER_STORE_TYPE")
    private String voucherStoreType;

    @TableField("ISMONENY")
    private String ismoneny;

    @TableField("MOBILEPAYSOLUTION")
    private String mobilepaysolution;

    @TableField("INTERNAL_PURCHASE_STORE")
    private String internalPurchaseStore;

    @TableField("PADPOS_TEMPLATE")
    private String padposTemplate;

    @TableField("ALLOW_CUSTOMER")
    private String allowCustomer;

    @TableField("BRANCHIDCARD")
    private String branchidcard;

    @TableField("POSIDCARD")
    private String posidcard;

    @TableField("ONLINEORDER")
    private String onlineorder;

    @TableField("VIP_ACTIVATE")
    private String vipActivate;

    @TableField("IS_WADE")
    private String isWade;

    @TableField("ISPRINTHOLDRETAIL")
    private String isprintholdretail;

    @TableField("MD5")
    private String md5;

    @TableField("MISPOSCARD")
    private String misposcard;

    @TableField("MISPOSTERMINAL")
    private String misposterminal;

    @TableField("MISPOSVISION")
    private String misposvision;

    @TableField("WECHAT_CUSTOMERIDNEW3")
    private String wechatCustomeridnew3;

    @TableField("WECHAT_CUSTOMERIDNEW4")
    private String wechatCustomeridnew4;

    @TableField("CCB_POSB_TERM_NO")
    private String ccbPosbTermNo;

    @TableField("USER_ID")
    private String userId;

    @TableField("LANDI_UNIONPAY")
    private String landiUnionpay;

    @TableField("WECHAT_CUSTOMERIDNEW5")
    private String wechatCustomeridnew5;

    @TableField("POSBTOC_POSB_TERM_NO")
    private String posbtocPosbTermNo;

    @TableField("ISMOBILEPAYS")
    private String ismobilepays;

    @TableField("CONSUMER_CARD_PAY_TYPE")
    private String consumerCardPayType;

    @TableField("PAYWEB_APPKEY")
    private String paywebAppkey;

    @TableField("MANUALLYENTER")
    private String manuallyenter;

    @TableField("IS_VIP")
    private String isVip;

    @TableField("C_STOREATTRIB22_ID")
    private Long cStoreattrib22Id;

    @TableField("IS_YUNDONG")
    private String isYundong;

    @TableField("IS_PENGMA")
    private String isPengma;

    @TableField("INVENTORY_YEAR")
    private String inventoryYear;

    @TableField("WAREHOUSE_RANG")
    private String warehouseRang;

    @TableField("CLOSEDATE")
    private Integer closedate;

    @TableField("IS_TINGYE")
    private String isTingye;

    @TableField("C_STOREATTRIB24_ID")
    private String cStoreattrib24Id;

    @TableField("C_STOREATTRIB25_ID")
    private String cStoreattrib25Id;

    @TableField("C_STOREATTRIB26_ID")
    private String cStoreattrib26Id;

    @TableField("C_STOREATTRIB27_ID")
    private String cStoreattrib27Id;

    @TableField("C_STOREATTRIB28_ID")
    private String cStoreattrib28Id;

    @TableField("C_STOREATTRIB29_ID")
    private String cStoreattrib29Id;

    @TableField("C_STOREATTRIB30_ID")
    private Integer cStoreattrib30Id;

    @TableField("C_STOREATTRIB31_ID")
    private String cStoreattrib31Id;

    @TableField("C_STOREATTRIB32_ID")
    private String cStoreattrib32Id;

    @TableField("C_STOREATTRIB33_ID")
    private String cStoreattrib33Id;

    @TableField("C_STOREATTRIB34_ID")
    private String cStoreattrib34Id;

    @TableField("C_STOREATTRIB35_ID")
    private String cStoreattrib35Id;

    @TableField("C_STOREATTRIB36_ID")
    private String cStoreattrib36Id;

    @TableField("C_STOREATTRIB37_ID")
    private String cStoreattrib37Id;

    @TableField("C_STOREATTRIB38_ID")
    private String cStoreattrib38Id;

    @TableField("C_STOREATTRIB39_ID")
    private String cStoreattrib39Id;

    @TableField("HEDIAN_PINPAI")
    private String hedianPinpai;

    @TableField("MARKET_NAME")
    private String marketName;

    @TableField("YEAR_RENT")
    private String yearRent;

    @TableField("DEC_OPENDATE")
    private Integer decOpendate;

    @TableField("TAX_DIS")
    private String taxDis;

    @TableField("YEAR_AMT_BOT")
    private String yearAmtBot;

    @TableField("CONTRACT_STATUS")
    private String contractStatus;

    @TableField("CLOSE_RESON")
    private String closeReson;

    @TableField("DISPLAY")
    private String display;

    @TableField("STORE_TYPE")
    private String storeType;

    @TableField("IS_BUS_LICENSE")
    private String isBusLicense;

    @TableField("C_STOREATTRIB23_ID")
    private Long cStoreattrib23Id;

    @TableField("AREAMNG_NEW")
    private String areamngNew;

    @TableField("BIGAREAMNG_NEW")
    private String bigareamngNew;

    @TableField("TAX_NATURE")
    private String taxNature;

    @TableField("GRADE")
    private String grade;

    @TableField("IS_YTONO")
    private String isYtono;

    @TableField("DEFAULT01")
    private String default01;

    @TableField("DEFAULT02")
    private String default02;

    @TableField("DEFAULT04")
    private String default04;

    @TableField("DEFAULT05")
    private String default05;

    @TableField("DEFAULT06")
    private String default06;

    @TableField("DEFAULT15")
    private String default15;

    @TableField("DEFAULT03")
    private Long default03;

    @TableField("DEFAULT07")
    private String default07;

    @TableField("DEFAULT08")
    private String default08;

    @TableField("DEFAULT09")
    private String default09;

    @TableField("DEFAULT10")
    private String default10;

    @TableField("DEFAULT11")
    private String default11;

    @TableField("DEFAULT13")
    private String default13;

    @TableField("DEFAULT14")
    private String default14;

    @TableField("DEFAULT12")
    private String default12;

    @TableField("OP_DEVICE_ID")
    private String opDeviceId;

    @TableField("ISINVOICE")
    private String isinvoice;

    @TableField("RETURNPRICE")
    private String returnprice;

    @TableField("ZX_CUSTOMERID")
    private String zxCustomerid;

    @TableField("INVOICEURL")
    private String invoiceurl;

    @TableField("SBSNUM")
    private String sbsnum;

    @TableField("ISUSERRFID")
    private String isuserrfid;

    @TableField("SUBORDERZT")
    private String suborderzt;

    @TableField("CAN_GRAB_NOCAN")
    private String canGrabNocan;

    @TableField("IS_UNIQUE")
    private String isUnique;

    @TableField("IS_CHECKSKUECRM")
    private String isCheckskuecrm;

    @TableField("IS_TORFID")
    private String isTorfid;

    @TableField("USE_POS")
    private String usePos;

    @ApiModelProperty(value = "BPOS零售流水码是否从仓库发出")
    @TableField("ISUNIREC")
    private String isunirec;

    @ApiModelProperty(value = "京东城市仓")
    @TableField("C_STORE_JD_ID")
    private Long cStoreJdId;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK_JD")
    private String remarkJd;

    @ApiModelProperty(value = "物流公司")
    @TableField("EB_EXPRESS_JD_ID")
    private Long ebExpressJdId;

    @TableField("USEPOSITION")
    private String useposition;

    @TableField("POSITIONMD")
    private String positionmd;

    @TableField("FH_BRAND")
    private String fhBrand;

    @TableField("INVOICE_COMPANY")
    private String invoiceCompany;

    @TableField("WECHAT_CUSTOMERIDNEW5_2")
    private String wechatCustomeridnew52;

    @TableField("POSBTOC_POSB_TERM_NO_2")
    private String posbtocPosbTermNo2;

    @TableField("DATUM_NUMBER")
    private String datumNumber;

    @TableField("VOU_INPUT_TYPE")
    private String vouInputType;

    @TableField("INIT_INPUT_TYPE")
    private String initInputType;

    @TableField("STORE_CLERK")
    private String storeClerk;

    @TableField("STORE_SALEACCOUNT")
    private String storeSaleaccount;

    @TableField("STORE_SALETAXNUM")
    private String storeSaletaxnum;

    @TableField("OLD_CODE")
    private String oldCode;

    @TableField("SMALL_ROUTINE")
    private String smallRoutine;

    @TableField("CHECK_CODE")
    private String checkCode;

    @TableField("OLD_XSQY")
    private String oldXsqy;

    @TableField("STORE_NAME")
    private String storeName;

    @TableField("FLOOR_PLAN")
    private String floorPlan;

    @TableField("SEATING_IMG")
    private String seatingImg;

    @TableField("TOSOLVED")
    private String tosolved;

    @TableField("REMARK1")
    private String remark1;

    @TableField("REMARK7")
    private Long remark7;

}
