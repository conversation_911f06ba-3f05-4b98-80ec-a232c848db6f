package com.jnby.infrastructure.bojun.model;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("BRAND_RULE_CONTRAST")
@ApiModel(value = "BrandRuleContrastVo对象", description = "")
public class BrandRuleContrast implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键自增")
    @TableId("ID")
    private Long id;

    @ApiModelProperty(value = "记录规则名称")
    @TableField("RULE_NAME")
    private String ruleName;

    @ApiModelProperty(value = "编码")
    @TableField("CODE")
    private String code;

    @ApiModelProperty(value = "类型 1 经销总仓 2 经销店仓 3道具总仓 4道具店仓 5经销商档案")
    @TableField("TYPE")
    private String type;

    @ApiModelProperty(value = "1 经销 2 直营")
    @TableField("STORE_TYPE")
    private String storeType;

    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;

    @ApiModelProperty(value = "品牌")
    @TableField("BRAND")
    private String brand;

    @ApiModelProperty(value = "记录规则编号")
    @TableField("RULE_NUM")
    private String ruleNum;

    @ApiModelProperty(value = "资源code 可以根据code查询对应的经销商和店仓")
    @TableField("RESOURCE_CODE")
    private String resourceCode;


}
