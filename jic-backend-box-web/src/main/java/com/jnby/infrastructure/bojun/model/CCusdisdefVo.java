package com.jnby.infrastructure.bojun.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("C_CUSDISDEF")
@ApiModel(value = "CCusdisdefVo对象", description = "")
public class CCusdisdefVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("ID")
    private Long id;

    @TableField("AD_CLIENT_ID")
    private Long adClientId;

    @TableField("AD_ORG_ID")
    private Long adOrgId;

    @TableField("C_CUSTOMER_ID")
    private Long cCustomerId;

    @TableField("M_DIM1_ID")
    private Long mDim1Id;

    @TableField("CAN_BUTSALE")
    private String canButsale;

    @TableField("CAN_NOTBUTSALE")
    private String canNotbutsale;

    @TableField("CAN_AGTSALE")
    private String canAgtsale;

    @TableField("FIRSALEDIS")
    private BigDecimal firsaledis;

    @TableField("SALEDIS")
    private BigDecimal saledis;

    @TableField("BUTSALEDIS")
    private BigDecimal butsaledis;

    @TableField("AGTSALEDIS")
    private BigDecimal agtsaledis;

    @TableField("FIRSALERETDIS")
    private BigDecimal firsaleretdis;

    @TableField("SALERETDIS")
    private BigDecimal saleretdis;

    @TableField("AGTSALERETDIS")
    private BigDecimal agtsaleretdis;

    @TableField("TRANDIS")
    private BigDecimal trandis;

    @TableField("OWNERID")
    private Long ownerid;

    @TableField("MODIFIERID")
    private Long modifierid;

    @TableField("CREATIONDATE")
    private Date creationdate;

    @TableField("MODIFIEDDATE")
    private Date modifieddate;

    @TableField("ISACTIVE")
    private String isactive;

    @TableField("PTR4SALERETDIS")
    private BigDecimal ptr4saleretdis;


}
