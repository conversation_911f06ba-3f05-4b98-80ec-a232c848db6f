package com.jnby.infrastructure.bojun.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jnby.infrastructure.bojun.model.CRegions;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 伯俊省市区查询（非加载进内存的省市区）
 */
public interface CRegionsMapper {

     /**
      * 获取省
      * @param name
      * @return
      */
     List<CRegions> getProvince(@Param("name") String name);

     /**
      * 获取指定市
      * @param name
      * @param pId
      * @return
      */
     List<CRegions> getCity(@Param("name") String name,@Param("pId") Long pId);

     /**
      * 获取指定区
      * @param name
      * @param pId
      * @return
      */
     List<CRegions> getDistrict(@Param("name") String name,@Param("pId") Long pId);


     List<CRegions> getProvinces();

     List<CRegions> getCities(@Param("pId") Long pId);

     List<CRegions> getDistricts(@Param("pId") Long pId);

     CRegions getProvinceById(@Param("id") Long id);

     CRegions getCityById(@Param("id") Long id);

     CRegions getDistrictById(@Param("id") Long id);

     List<CRegions> getAllCity();

     List<CRegions> getAllDistricts();
}
