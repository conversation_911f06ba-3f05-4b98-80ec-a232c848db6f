package com.jnby.infrastructure.bojun.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jnby.infrastructure.bojun.model.UsersVo;

import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-29
 */
public interface UsersMapper extends BaseMapper<UsersVo> {

    /**
     * 经销商自增存储过程调用 C_CUSTOMER_AC/C_CUSTOMER_AM
     *
     * @param map 经销商主键
     */

    void insertIntoUsersTableAc(Map<String, Object> map);

    /**
     * 经销商自增存储过程调用 C_CUSTOMER_AC/C_CUSTOMER_AM
     *
     * @param map 经销商主键
     */
    void insertIntoUsersTableAm(Map<String, Object> map);

}
