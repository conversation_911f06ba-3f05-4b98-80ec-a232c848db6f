package com.jnby.infrastructure.bojun.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-18
 */
@Data
@TableName("C_CUSTOMER")
@ApiModel(value = "CCustomerVo对象", description = "经销商实体类对象")
public class CCustomerVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     * <p>
     * AUTO: 数据库ID自增
     * INPUT: 用户输入ID
     * ID_WORKER: 全局唯一ID，Long类型的主键
     * ID_WORKER_STR: 字符串全局唯一ID
     * UUID: 全局唯一ID，UUID类型的主键
     * NONE: 该类型为未设置主键类型
     */
    @TableId(value = "ID", type = IdType.INPUT)
    private Long id;

    @TableField("AD_CLIENT_ID")
    private Long adClientId;

    @TableField("AD_ORG_ID")
    private Long adOrgId;

    @TableField("MODIFIERID")
    private Long modifierid;

    @TableField("CREATIONDATE")
    private Date creationdate;

    @TableField("MODIFIEDDATE")
    private Date modifieddate;

    @TableField("OWNERID")
    private Long ownerid;

    @TableField("ISACTIVE")
    private String isactive;

    @TableField("NAME")
    private String name;

    @TableField("DESCRIPTION")
    private String description;

    @TableField("ENTERDATE")
    private Integer enterdate;

    @TableField("ISSTOP")
    private String isstop;

    @TableField("CONTACTER")
    private String contacter;

    @TableField("PHONE")
    private String phone;

    @TableField("ADDRESS")
    private String address;

    @TableField("POST")
    private String post;

    @TableField("ACCOUNT")
    private String account;

    @TableField("EMAIL")
    private String email;

    @TableField("REMARK")
    private String remark;

    @TableField("TAXNO")
    private String taxno;

    @TableField("FEEREMAIN")
    private Long feeremain;

    @TableField("FEESALE")
    private Long feesale;

    /**
     * 默认现货折扣
     */
    @TableField("SALEDIS")
    private BigDecimal saledis;

    @TableField("SALERETDIS")
    private Long saleretdis;

    @TableField("C_CUSRANK_ID")
    private Long cCusrankId;

    @TableField("C_CUSTOMERUP_ID")
    private Long cCustomerupId;

    /**
     * 默认期货折扣
     */
    @TableField("FIRSALEDIS")
    private BigDecimal firsaledis;

    @TableField("FEELSALE")
    private Long feelsale;

    /**
     * 提货余额下限 NUMBER(18,2)
     */
    @TableField("FEELTAKE")
    private BigDecimal feeltake;

    @TableField("ISPERIOD")
    private String isperiod;

    @TableField("AREAMNG_ID")
    private Long areamngId;

    @TableField("C_CITY_ID")
    private Long cCityId;

    @TableField("ISAUTOIN")
    private String isautoin;

    @TableField("ORDERRATE")
    private Long orderrate;

    @TableField("ISMTRLSTEP")
    private String ismtrlstep;

    @TableField("RET_RATE")
    private Long retRate;

    @TableField("BIGAREAMNG_ID")
    private Long bigareamngId;

    @TableField("ISGROUP")
    private String isgroup;

    @TableField("ISACCOUNT")
    private String isaccount;

    @TableField("C_DEPARTMENT_ID")
    private Long cDepartmentId;

    @TableField("C_CLASSCODE_ID")
    private Long cClasscodeId;

    @TableField("CODE")
    private String code;

    @TableField("UF_CODE")
    private String ufCode;

    @TableField("C_AREA_ID")
    private Long cAreaId;

    @TableField("PRIORITY")
    private Long priority;

    @TableField("M_DIMCUS1_ID")
    private Long mDimcus1Id;

    @TableField("M_DIMCUS2_ID")
    private Long mDimcus2Id;

    @TableField("M_DIMCUS3_ID")
    private Long mDimcus3Id;

    @TableField("M_DIMCUS4_ID")
    private Long mDimcus4Id;

    @TableField("M_DIMCUS5_ID")
    private Long mDimcus5Id;

    @TableField("M_DIMCUS6_ID")
    private Long mDimcus6Id;

    @TableField("M_DIMCUS7_ID")
    private Long mDimcus7Id;

    @TableField("M_DIMCUS8_ID")
    private Long mDimcus8Id;

    @TableField("M_DIMCUS9_ID")
    private Long mDimcus9Id;

    @TableField("M_DIMCUS10_ID")
    private Long mDimcus10Id;

    /**
     * 默认现货买断折扣
     */
    @TableField("BUTSALEDIS")
    private BigDecimal butsaledis;

    /**
     * 默认代销折扣
     */
    @TableField("AGTSALEDIS")
    private BigDecimal agtsaledis;

    /**
     * 默认期货退货折扣
     */
    @TableField("FIRSALERETDIS")
    private BigDecimal firsaleretdis;

    /**
     * 默认代销退货折扣
     */
    @TableField("AGTSALERETDIS")
    private BigDecimal agtsaleretdis;

    @TableField("TRANDIS")
    private Long trandis;

    @TableField("C_CUSATTRIB1_ID")
    private Long cCusattrib1Id;

    @TableField("C_CUSATTRIB2_ID")
    private Long cCusattrib2Id;

    @TableField("C_CUSATTRIB3_ID")
    private Long cCusattrib3Id;

    @TableField("C_CUSATTRIB4_ID")
    private Long cCusattrib4Id;

    @TableField("C_CUSATTRIB5_ID")
    private Long cCusattrib5Id;

    @TableField("C_CUSATTRIB6_ID")
    private Long cCusattrib6Id;

    @TableField("C_CUSATTRIB7_ID")
    private Long cCusattrib7Id;

    @TableField("C_CUSATTRIB8_ID")
    private Long cCusattrib8Id;

    @TableField("C_CUSATTRIB9_ID")
    private Long cCusattrib9Id;

    @TableField("C_CUSATTRIB10_ID")
    private Long cCusattrib10Id;

    @TableField("C_CUSATTRIB11_ID")
    private Long cCusattrib11Id;

    @TableField("C_CUSATTRIB12_ID")
    private Long cCusattrib12Id;

    @TableField("C_CUSATTRIB13_ID")
    private Long cCusattrib13Id;

    @TableField("C_CUSATTRIB14_ID")
    private Long cCusattrib14Id;

    @TableField("C_CUSATTRIB15_ID")
    private Long cCusattrib15Id;

    @TableField("C_CUSATTRIB16_ID")
    private Long cCusattrib16Id;

    @TableField("C_CUSATTRIB17_ID")
    private Long cCusattrib17Id;

    @TableField("C_CUSATTRIB18_ID")
    private Long cCusattrib18Id;

    @TableField("C_CUSATTRIB19_ID")
    private Long cCusattrib19Id;

    @TableField("C_CUSATTRIB20_ID")
    private Long cCusattrib20Id;

    @TableField("CAN_BUTSALE")
    private String canButsale;

    @TableField("CAN_NOTBUTSALE")
    private String canNotbutsale;

    @TableField("CAN_AGTSALE")
    private String canAgtsale;

    @TableField("C_CORPSALE_ID1")
    private Long cCorpsaleId1;

    @TableField("C_CORPSALE_ID2")
    private Long cCorpsaleId2;

    @TableField("C_CORPSALE_ID3")
    private Long cCorpsaleId3;

    @TableField("C_CORPRET_ID1")
    private Long cCorpretId1;

    @TableField("C_CORPRET_ID2")
    private Long cCorpretId2;

    @TableField("MOBILE")
    private String mobile;

    @TableField("C_CORPRET_ID3")
    private Long cCorpretId3;

    @TableField("YUAN_CUSTOMER")
    private String yuanCustomer;

    @TableField("IS_TRUNCOM_RET")
    private String isTruncomRet;

    @TableField("IS_TRUNCOM_SAL")
    private String isTruncomSal;

    @TableField("IS_SALE_PCK")
    private String isSalePck;

    @TableField("FRAMWORK_AREA_ID")
    private Long framworkAreaId;

    @TableField("IS_PASSWORDCHECK")
    private String isPasswordcheck;

    @TableField("IS_COMPLETE")
    private String isComplete;

    /**
     * 默认现货买断退货折扣
     */
    @TableField("PTR4SALERETDIS")
    private BigDecimal ptr4saleretdis;

    @TableField("FEELTAKE_EBSO")
    private Long feeltakeEbso;

    @TableField("TERMINATEDATE")
    private Integer terminatedate;

    @TableField("CONBEGDATE")
    private Integer conbegdate;

    @TableField("CONBEGEND")
    private Integer conbegend;

    @TableField("C_CONSUMEAREA_ID")
    private Long cConsumeareaId;

    @TableField("BANK")
    private String bank;

    @TableField("IS_AUTOCONFIRM")
    private String isAutoconfirm;

    @TableField("THIRD_SF")
    private String thirdSf;

    @TableField("IS_CREDIT")
    private String isCredit;

    @TableField("IS_AUTONTB")
    private String isAutontb;

    @TableField("IS_CONTROLRET")
    private String isControlret;

    @TableField("C_ARCBRAND_ID")
    private Long cArcbrandId;

    @TableField("CLOP_STORE")
    private String clopStore;


}
