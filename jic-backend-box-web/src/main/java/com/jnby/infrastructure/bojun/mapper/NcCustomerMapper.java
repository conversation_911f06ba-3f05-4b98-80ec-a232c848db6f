package com.jnby.infrastructure.bojun.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jnby.infrastructure.bojun.model.NcCustomer;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【NC_CUSTOMER】的数据库操作Mapper
* @createDate 2024-02-01 09:53:27
* @Entity org.springcenter.basemgt.center.mdm.module.oa.manager.domain.NcCustomer
*/
public interface NcCustomerMapper {



    NcCustomer selectLatestByCustomerName(@Param("ncCustomerName") String ncCustomerName, @Param("ncAccountId") Integer ncAccountId);
}
