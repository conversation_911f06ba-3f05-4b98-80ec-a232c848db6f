package com.jnby.infrastructure.box.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jnby.infrastructure.box.model.WeiXinFans;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface WeiXinFansMapper extends BaseMapper<WeiXinFans> {
    int insert(WeiXinFans record);

    int insertSelective(WeiXinFans record);


    //根据unionId查询关注过公众号的用户
    List<WeiXinFans> findWeiXinFans(@Param(value="unionid")String unionid);

    /**
     * 通过openID获取
     * @param openId
     * @return
     */
    List<WeiXinFans> findByOpenid(@Param(value="openId")String openId);
}
