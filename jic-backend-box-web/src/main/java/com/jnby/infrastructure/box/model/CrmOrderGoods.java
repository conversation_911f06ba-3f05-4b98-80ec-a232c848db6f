package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TableName("CRM_ORDER_GOODS")
@NoArgsConstructor
@AllArgsConstructor
@Data
public class CrmOrderGoods {

    @TableId(value = "ID")
    private String id;

    @TableField("CUSTOMER_ID")
    private String customerId;

    @TableField("SEASON")
    private String season;

    @TableField("ORDER_GOODS_NUM")
    private String orderGoodsNum;

    @TableField("ORDER_GOODS_AMT")
    private String orderGoodsAmt;

    @TableField("CREATE_TIME")
    private String createTime;

    @TableField("UPDATE_TIME")
    private String updateTime;


}
