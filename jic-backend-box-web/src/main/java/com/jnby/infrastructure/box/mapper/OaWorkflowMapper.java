package com.jnby.infrastructure.box.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jnby.dto.oa.GetHtListReqDto;
import com.jnby.infrastructure.box.model.OaWorkflow;
import com.jnby.infrastructure.box.model.WorkFlowModel;

import java.util.List;


/**
 * @Author: lwz
 * @Date: 2025-01-17 15:05:33
 * @Description: Mapper
 */
public interface OaWorkflowMapper extends BaseMapper<OaWorkflow> {

    List<WorkFlowModel> selectCommonList(GetHtListReqDto getHtListReqDto);



    List<WorkFlowModel> selectCwList(GetHtListReqDto getHtListReqDto);


    List<WorkFlowModel> selectXsList(GetHtListReqDto getHtListReqDto);
}
