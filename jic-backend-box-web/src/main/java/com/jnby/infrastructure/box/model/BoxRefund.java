package com.jnby.infrastructure.box.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

public class BoxRefund {
    private String id;

    private String refundSn;

    private String unionid;

    private String orderId;

    private Long status;

    private String memo;

    private String trackingNumber;

    private Double refundAmount;

    private String sendLogisticsId;

    private String getDate;

    private Long sendBack;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

    private String refundRemark;

    private String refundPhotos;

    private Long autoAmount;

    @ApiModelProperty(value = "微盟售后单号 ")
    private String weimoRefundId;


    @ApiModelProperty(value = "储值卡退款金额")
    private BigDecimal refundBalance;

    public BigDecimal getRefundBalance() {
        return refundBalance;
    }

    public void setRefundBalance(BigDecimal refundBalance) {
        this.refundBalance = refundBalance;
    }

    public String getWeimoRefundId() {
        return weimoRefundId;
    }

    public void setWeimoRefundId(String weimoRefundId) {
        this.weimoRefundId = weimoRefundId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getRefundSn() {
        return refundSn;
    }

    public void setRefundSn(String refundSn) {
        this.refundSn = refundSn == null ? null : refundSn.trim();
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid == null ? null : unionid.trim();
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo == null ? null : memo.trim();
    }

    public String getTrackingNumber() {
        return trackingNumber;
    }

    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber == null ? null : trackingNumber.trim();
    }

    public Double getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(Double refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getSendLogisticsId() {
        return sendLogisticsId;
    }

    public void setSendLogisticsId(String sendLogisticsId) {
        this.sendLogisticsId = sendLogisticsId == null ? null : sendLogisticsId.trim();
    }

    public String getGetDate() {
        return getDate;
    }

    public void setGetDate(String getDate) {
        this.getDate = getDate == null ? null : getDate.trim();
    }

    public Long getSendBack() {
        return sendBack;
    }

    public void setSendBack(Long sendBack) {
        this.sendBack = sendBack;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRefundRemark() {
        return refundRemark;
    }

    public void setRefundRemark(String refundRemark) {
        this.refundRemark = refundRemark == null ? null : refundRemark.trim();
    }

    public String getRefundPhotos() {
        return refundPhotos;
    }

    public void setRefundPhotos(String refundPhotos) {
        this.refundPhotos = refundPhotos == null ? null : refundPhotos.trim();
    }

    public Long getAutoAmount() {
        return autoAmount;
    }

    public void setAutoAmount(Long autoAmount) {
        this.autoAmount = autoAmount;
    }
}