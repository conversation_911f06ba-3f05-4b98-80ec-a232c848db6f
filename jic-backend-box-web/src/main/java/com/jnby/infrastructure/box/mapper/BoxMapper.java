package com.jnby.infrastructure.box.mapper;


import com.jnby.infrastructure.box.model.Box;
import com.jnby.infrastructure.box.model.BoxWithBLOBs;
import com.jnby.infrastructure.box.model.Order;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface BoxMapper {
    BoxWithBLOBs selectByPrimaryKey(String id);
    BoxWithBLOBs selectByBoxSn(String boxSn);

    List<Box> selectListByUnionId(String unionId);

    /**
     * 获取一段时间的增量数据
     *
     * @param fromDate 2024-06-12 13:00:00
     * @param toDate   2024-06-12 14:00:00
     * @return 一段时间的增量数据
     */
    Integer selectIncrementCountByTime(@Param("fromDate") String fromDate, @Param("toDate") String toDate);

    List<Box> selectIncrementListByTime(@Param("fromDate") String fromDate, @Param("toDate") String toDate,
                                        @Param("start") Integer start, @Param("end") Integer end);

    /**
     * 获取 boxSn : unionId
     *
     * @param boxSnList 集合
     * @return 只返回boxSn, unionId
     */
    List<Box> selectUnionIdListByBoxSnList(@Param("boxSnList") List<String> boxSnList);
    int updateByPrimaryKeySelective(BoxWithBLOBs record);

    @Update("update box set status = #{status} where id = #{id} and status = #{beforeStatus}")
    int updateByPrimaryKeyWithStatus(@Param("status") Long status, @Param("id") String id, @Param("beforeStatus") Long beforeStatus);

}
