package com.jnby.infrastructure.box.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import com.jnby.infrastructure.box.model.BPointDetail;

import java.math.BigDecimal;
import java.util.List;


/**
 * @Author: lwz
 * @Date: 2024-12-04 16:52:52
 * @Description: Mapper
 */
public interface BPointDetailMapper extends BaseMapper<BPointDetail> {
    /**
     * 查询未来多少天内的详细  （大于当前时间小于当前时间+days）
     * @param unionIds
     * @param days
     * @return
     */
    List<BPointDetail> getListByUnionIdAndTime(@Param("unionIds") List<String> unionIds,@Param("days") Integer days);
}
