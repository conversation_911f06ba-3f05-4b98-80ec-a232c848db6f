package com.jnby.infrastructure.box.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jnby.dto.ShortLinkToolsDto;
import com.jnby.dto.ShortLinkToolsListReq;
import com.jnby.infrastructure.box.model.ShortLinkTools;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【SHORT_LINK_TOOLS】的数据库操作Mapper
* @createDate 2024-10-11 14:22:46
* @Entity generator.domain.ShortLinkTools
*/
public interface ShortLinkToolsMapper extends BaseMapper<ShortLinkTools>{

    List<ShortLinkTools> selectByParams(ShortLinkToolsListReq requestData);
}




