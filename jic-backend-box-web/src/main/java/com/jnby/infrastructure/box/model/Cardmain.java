package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: lwz
 * @Date: 2024-01-04 10:52:53
 * @Description: 卡主档
 */
@TableName("CARDMAIN")
@Accessors(chain = true)
@ApiModel(value="Cardmain对象", description="卡主档")
public class Cardmain implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private Long id;
    @ApiModelProperty(value = "租户ID")
    @TableField("RENTID")
    private BigDecimal rentid;
    @TableField("WEID")
    private Long weid;
    @ApiModelProperty(value = "微信公众号的openid")
    @TableField("OPENID")
    private String openid;
    @ApiModelProperty(value = "卡类型id")
    @TableField("CARDTYPE")
    private BigDecimal cardtype;
    @ApiModelProperty(value = "卡号")
    @TableField("CARDNO")
    private String cardno;
    @ApiModelProperty(value = "用户姓名")
    @TableField("USERNAME")
    private String username;
    @ApiModelProperty(value = "剩余积分")
    @TableField("JFYE")
    private Long jfye;
    @ApiModelProperty(value = "总积分")
    @TableField("JFTOTAL")
    private Long jftotal;
    @ApiModelProperty(value = "消费积分")
    @TableField("JFXF")
    private Long jfxf;
    @ApiModelProperty(value = "签到积分")
    @TableField("JFSIGN")
    private Long jfsign;
    @ApiModelProperty(value = "总消费金额")
    @TableField("XFJE")
    private BigDecimal xfje;
    @ApiModelProperty(value = "手机号")
    @TableField("TEL")
    private String tel;
    @ApiModelProperty(value = "生日")
    @TableField("BIRTHDAY")
    private Date birthday;
    @ApiModelProperty(value = "联系地址")
    @TableField("ADDRESS")
    private String address;
    @ApiModelProperty(value = "性别")
    @TableField("SEX")
    private String sex;
    @ApiModelProperty(value = "Email")
    @TableField("EMAIL")
    private String email;
    @ApiModelProperty(value = "状态")
    @TableField("STATUS")
    private String status;
    @ApiModelProperty(value = "集团卡号")
    @TableField("REMARK")
    private String remark;
    @ApiModelProperty(value = "更新时间")
    @TableField("MODIFYDATE")
    private Date modifydate;
    @ApiModelProperty(value = "开卡时间")
    @TableField("INPUTDATE")
    private Date inputdate;
    @ApiModelProperty(value = "到期日期")
    @TableField("VALIDDATE")
    private Date validdate;
    @ApiModelProperty(value = "是否绑定")
    @TableField("ISLINK")
    private String islink;
    @ApiModelProperty(value = "绑定来源")
    @TableField("LINKSOURCE")
    private String linksource;
    @ApiModelProperty(value = "绑定ID")
    @TableField("LINKID")
    private String linkid;
    @ApiModelProperty(value = "角色ID，0会员，1客服 2阵列师")
    @TableField("ROLEID")
    private BigDecimal roleid;
    @ApiModelProperty(value = "绑定的客服ID")
    @TableField("KFID")
    private BigDecimal kfid;
    @ApiModelProperty(value = "开卡门店")
    @TableField("KKSTORE")
    private String kkstore;
    @ApiModelProperty(value = "生日当天是否发送生日祝福")
    @TableField("ISSEND")
    private String issend;
    @ApiModelProperty(value = "发送次数")
    @TableField("SENDTIME")
    private BigDecimal sendtime;
    @TableField("CDMUNIONID")
    private String cdmunionid;
    @ApiModelProperty(value = "来源类型")
    @TableField("CDMSOURCETYPE")
    private String cdmsourcetype;
    @ApiModelProperty(value = "来源值")
    @TableField("CDMSOURCEVAL")
    private String cdmsourceval;
    @ApiModelProperty(value = "是否邮寄试衣 ")
    @TableField("ISPOSTFITTING")
    private String ispostfitting;
    @ApiModelProperty(value = "寄送次数 1 ")
    @TableField("POSTFITTINGTIME")
    private Integer postfittingtime;
    @ApiModelProperty(value = "一年的消费金额")
    @TableField("OYEARPAYAMOUNT")
    private BigDecimal oyearpayamount;
    @ApiModelProperty(value = "当前年的消费金额")
    @TableField("DYEARPAYAMOUNT")
    private BigDecimal dyearpayamount;
    @ApiModelProperty(value = "一年的消费次数")
    @TableField("OYEARPAYCOUNT")
    private BigDecimal oyearpaycount;
    @ApiModelProperty(value = "当前年的消费次数")
    @TableField("DYEARPAYCOUNT")
    private BigDecimal dyearpaycount;
    @ApiModelProperty(value = "一整月的消费金额")
    @TableField("OMONTHPAYAMOUNT")
    private BigDecimal omonthpayamount;
    @ApiModelProperty(value = "当前月的消费金额")
    @TableField("DMONTHPAYAMOUNT")
    private BigDecimal dmonthpayamount;
    @ApiModelProperty(value = "一整月的消费次数")
    @TableField("OMONTHPAYCOUNT")
    private BigDecimal omonthpaycount;
    @ApiModelProperty(value = "当前月的消费次数")
    @TableField("DMONTHPAYCOUNT")
    private BigDecimal dmonthpaycount;
    @TableField("JNBEAN")
    private BigDecimal jnbean;
    @TableField("BY1")
    private BigDecimal by1;
    @TableField("BY2")
    private String by2;
    @TableField("BY3")
    private String by3;
    @ApiModelProperty(value = "微信小程序的openid")
    @TableField("WXOPENID")
    private String wxopenid;
    @ApiModelProperty(value = "手机APP端登录用uuid")
    @TableField("UUID")
    private String uuid;
    @ApiModelProperty(value = "微信开放平台uniond")
    @TableField("UNIONID")
    private String unionid;
    @ApiModelProperty(value = "昵称")
    @TableField("NICKNAME")
    private String nickname;
    @ApiModelProperty(value = "微盟会员唯一标识")
    @TableField("WID")
    private BigDecimal wid;
    @ApiModelProperty(value = "头像")
    @TableField("HEADIMGURL")
    private String headimgurl;
    @ApiModelProperty(value = "ERP卡id-c_client_vip")
    @TableField("CARDID")
    private BigDecimal cardid;
    @ApiModelProperty(value = "品牌卡类型名称")
    @TableField("CARDTYPENAME")
    private String cardtypename;
    @ApiModelProperty(value = "品牌卡类型id")
    @TableField("CARDTYPEID")
    private BigDecimal cardtypeid;
    @ApiModelProperty(value = "集团卡id-c_client_vip")
    @TableField("MEMBERID")
    private BigDecimal memberid;
    @ApiModelProperty(value = "集团卡类型")
    @TableField("MEMBERTYPEID")
    private BigDecimal membertypeid;
    @ApiModelProperty(value = "集团卡等级名称")
    @TableField("MEMBERTYPENAME")
    private String membertypename;
    @ApiModelProperty(value = "门店id")
    @TableField("STOREID")
    private BigDecimal storeid;
    @ApiModelProperty(value = "门店名称")
    @TableField("STORENAME")
    private String storename;
    @ApiModelProperty(value = "门店编号")
    @TableField("STORECODE")
    private String storecode;
    @ApiModelProperty(value = "导购id")
    @TableField("CLERKID")
    private BigDecimal clerkid;
    @ApiModelProperty(value = "导购名称")
    @TableField("CLERKNAME")
    private String clerkname;
    @ApiModelProperty(value = "导购编号")
    @TableField("CLERKCODE")
    private String clerkcode;
    @ApiModelProperty(value = "门店客户id")
    @TableField("CUSTOMERID")
    private BigDecimal customerid;
    @ApiModelProperty(value = "开卡时间")
    @TableField("OPENTIME")
    private Long opentime;
    @ApiModelProperty(value = "集团卡号")
    @TableField("MEMBERCODE")
    private String membercode;
    @ApiModelProperty(value = "是否有效")
    @TableField("ISACTIVE")
    private String isactive;
    @ApiModelProperty(value = "开卡渠道")
    @TableField("OPENCHANNEL")
    private String openchannel;
    @ApiModelProperty(value = "品牌积分")
    @TableField("INTEGRAL")
    private BigDecimal integral;
    @ApiModelProperty(value = "是否关注品牌公众号")
    @TableField("ISSUBSCRIBE")
    private String issubscribe;
    @ApiModelProperty(value = "是否加企微好友")
    @TableField("ISEXTERNALUSER")
    private String isexternaluser;
    @ApiModelProperty(value = "是否关注box公众号")
    @TableField("ISSUBSCRIBEBOX")
    private String issubscribebox;
    @ApiModelProperty(value = "是否领生日礼 0-待领取,1-已领取,2-核销生日折扣券,3-核销生日礼券,4-都核销")
    @TableField("ISRECEIVEBIRTH")
    private String isreceivebirth;
    @ApiModelProperty(value = "最后一次消费时间")
    @TableField("LASTCONSUMTIME")
    private Date lastconsumtime;
    @ApiModelProperty(value = "消费次数")
    @TableField("CONSUMTIMES")
    private BigDecimal consumtimes;
    @ApiModelProperty(value = "总消费金额")
    @TableField("TOTCONSUMAMT")
    private BigDecimal totconsumamt;
    @ApiModelProperty(value = "宝宝姓名")
    @TableField("EXT1")
    private String ext1;
    @ApiModelProperty(value = "宝宝性别")
    @TableField("EXT2")
    private String ext2;
    @ApiModelProperty(value = "宝宝生日")
    @TableField("EXT3")
    private String ext3;
    @TableField("EXT4")
    private String ext4;
    @TableField("EXT5")
    private String ext5;
    @TableField("EXT6")
    private String ext6;
    @TableField("EXT7")
    private String ext7;
    @TableField("EXT8")
    private String ext8;
    @TableField("EXT9")
    private String ext9;
    @TableField("EXT10")
    private String ext10;
    @ApiModelProperty(value = "集团卡开卡时间")
    @TableField("MEMBERTIME")
    private BigDecimal membertime;
    @ApiModelProperty(value = "集团积分")
    @TableField("MEMBERINTEGRAL")
    private BigDecimal memberintegral;
    @ApiModelProperty(value = "导购手机号")
    @TableField("CLERKPHONE")
    private String clerkphone;
    @ApiModelProperty(value = "卡状态")
    @TableField("CARDSTATUS")
    private String cardstatus;
    @ApiModelProperty(value = "第一次消费时间")
    @TableField("FIRSTCONSUMTIME")
    private Date firstconsumtime;
    @ApiModelProperty(value = "企微外部联系人id")
    @TableField("EXTERNALUSERID")
    private String externaluserid;
    @ApiModelProperty(value = "品牌卡等级")
    @TableField("CARDLVL")
    private BigDecimal cardlvl;
    @ApiModelProperty(value = "集团卡等级ID")
    @TableField("MEMBERLVL")
    private BigDecimal memberlvl;
    @TableField("CARDNUM")
    private String cardnum;
    @TableField("FIRSTCONSUMETIME")
    private Date firstconsumetime;
    @TableField("LASTCONSUMETIME")
    private Date lastconsumetime;
    @TableField("CONSUMETIMES")
    private BigDecimal consumetimes;
    @TableField("TOTCONSUMEAMT")
    private BigDecimal totconsumeamt;
    @TableField("EXT11")
    private String ext11;
    @TableField("EXT12")
    private String ext12;
    @TableField("EXT13")
    private String ext13;
    @TableField("EXT14")
    private String ext14;
    @TableField("EXT15")
    private String ext15;
    @ApiModelProperty(value = "开卡门店id")
    @TableField("FIRSTSTOREID")
    private BigDecimal firststoreid;
    @ApiModelProperty(value = "开卡门店名称")
    @TableField("FIRSTSTORENAME")
    private String firststorename;
    @ApiModelProperty(value = "开卡门店编号")
    @TableField("FIRSTSTORECODE")
    private String firststorecode;
    @ApiModelProperty(value = "开卡门店客户id")
    @TableField("FIRSTCUSTOMERID")
    private BigDecimal firstcustomerid;
    @ApiModelProperty(value = "开卡导购手机号")
    @TableField("FIRSTCLERKPHONE")
    private String firstclerkphone;
    @ApiModelProperty(value = "开卡导购id")
    @TableField("FIRSTCLERKID")
    private BigDecimal firstclerkid;
    @ApiModelProperty(value = "开卡导购名称")
    @TableField("FIRSTCLERKNAME")
    private String firstclerkname;
    @ApiModelProperty(value = "开卡导购编号")
    @TableField("FIRSTCLERKCODE")
    private String firstclerkcode;


    public Long getId() {
        return id;
    }

    public Cardmain setId(Long id) {
        this.id = id;
        return this;
    }

    public BigDecimal getRentid() {
        return rentid;
    }

    public Cardmain setRentid(BigDecimal rentid) {
        this.rentid = rentid;
        return this;
    }

    public Long getWeid() {
        return weid;
    }

    public Cardmain setWeid(Long weid) {
        this.weid = weid;
        return this;
    }

    public String getOpenid() {
        return openid;
    }

    public Cardmain setOpenid(String openid) {
        this.openid = openid;
        return this;
    }

    public BigDecimal getCardtype() {
        return cardtype;
    }

    public Cardmain setCardtype(BigDecimal cardtype) {
        this.cardtype = cardtype;
        return this;
    }

    public String getCardno() {
        return cardno;
    }

    public Cardmain setCardno(String cardno) {
        this.cardno = cardno;
        return this;
    }

    public String getUsername() {
        return username;
    }

    public Cardmain setUsername(String username) {
        this.username = username;
        return this;
    }

    public Long getJfye() {
        return jfye;
    }

    public Cardmain setJfye(Long jfye) {
        this.jfye = jfye;
        return this;
    }

    public Long getJftotal() {
        return jftotal;
    }

    public Cardmain setJftotal(Long jftotal) {
        this.jftotal = jftotal;
        return this;
    }

    public Long getJfxf() {
        return jfxf;
    }

    public Cardmain setJfxf(Long jfxf) {
        this.jfxf = jfxf;
        return this;
    }

    public Long getJfsign() {
        return jfsign;
    }

    public Cardmain setJfsign(Long jfsign) {
        this.jfsign = jfsign;
        return this;
    }

    public BigDecimal getXfje() {
        return xfje;
    }

    public Cardmain setXfje(BigDecimal xfje) {
        this.xfje = xfje;
        return this;
    }

    public String getTel() {
        return tel;
    }

    public Cardmain setTel(String tel) {
        this.tel = tel;
        return this;
    }

    public Date getBirthday() {
        return birthday;
    }

    public Cardmain setBirthday(Date birthday) {
        this.birthday = birthday;
        return this;
    }

    public String getAddress() {
        return address;
    }

    public Cardmain setAddress(String address) {
        this.address = address;
        return this;
    }

    public String getSex() {
        return sex;
    }

    public Cardmain setSex(String sex) {
        this.sex = sex;
        return this;
    }

    public String getEmail() {
        return email;
    }

    public Cardmain setEmail(String email) {
        this.email = email;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public Cardmain setStatus(String status) {
        this.status = status;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public Cardmain setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public Date getModifydate() {
        return modifydate;
    }

    public Cardmain setModifydate(Date modifydate) {
        this.modifydate = modifydate;
        return this;
    }

    public Date getInputdate() {
        return inputdate;
    }

    public Cardmain setInputdate(Date inputdate) {
        this.inputdate = inputdate;
        return this;
    }

    public Date getValiddate() {
        return validdate;
    }

    public Cardmain setValiddate(Date validdate) {
        this.validdate = validdate;
        return this;
    }

    public String getIslink() {
        return islink;
    }

    public Cardmain setIslink(String islink) {
        this.islink = islink;
        return this;
    }

    public String getLinksource() {
        return linksource;
    }

    public Cardmain setLinksource(String linksource) {
        this.linksource = linksource;
        return this;
    }

    public String getLinkid() {
        return linkid;
    }

    public Cardmain setLinkid(String linkid) {
        this.linkid = linkid;
        return this;
    }

    public BigDecimal getRoleid() {
        return roleid;
    }

    public Cardmain setRoleid(BigDecimal roleid) {
        this.roleid = roleid;
        return this;
    }

    public BigDecimal getKfid() {
        return kfid;
    }

    public Cardmain setKfid(BigDecimal kfid) {
        this.kfid = kfid;
        return this;
    }

    public String getKkstore() {
        return kkstore;
    }

    public Cardmain setKkstore(String kkstore) {
        this.kkstore = kkstore;
        return this;
    }

    public String getIssend() {
        return issend;
    }

    public Cardmain setIssend(String issend) {
        this.issend = issend;
        return this;
    }

    public BigDecimal getSendtime() {
        return sendtime;
    }

    public Cardmain setSendtime(BigDecimal sendtime) {
        this.sendtime = sendtime;
        return this;
    }

    public String getCdmunionid() {
        return cdmunionid;
    }

    public Cardmain setCdmunionid(String cdmunionid) {
        this.cdmunionid = cdmunionid;
        return this;
    }

    public String getCdmsourcetype() {
        return cdmsourcetype;
    }

    public Cardmain setCdmsourcetype(String cdmsourcetype) {
        this.cdmsourcetype = cdmsourcetype;
        return this;
    }

    public String getCdmsourceval() {
        return cdmsourceval;
    }

    public Cardmain setCdmsourceval(String cdmsourceval) {
        this.cdmsourceval = cdmsourceval;
        return this;
    }

    public String getIspostfitting() {
        return ispostfitting;
    }

    public Cardmain setIspostfitting(String ispostfitting) {
        this.ispostfitting = ispostfitting;
        return this;
    }

    public Integer getPostfittingtime() {
        return postfittingtime;
    }

    public Cardmain setPostfittingtime(Integer postfittingtime) {
        this.postfittingtime = postfittingtime;
        return this;
    }

    public BigDecimal getOyearpayamount() {
        return oyearpayamount;
    }

    public Cardmain setOyearpayamount(BigDecimal oyearpayamount) {
        this.oyearpayamount = oyearpayamount;
        return this;
    }

    public BigDecimal getDyearpayamount() {
        return dyearpayamount;
    }

    public Cardmain setDyearpayamount(BigDecimal dyearpayamount) {
        this.dyearpayamount = dyearpayamount;
        return this;
    }

    public BigDecimal getOyearpaycount() {
        return oyearpaycount;
    }

    public Cardmain setOyearpaycount(BigDecimal oyearpaycount) {
        this.oyearpaycount = oyearpaycount;
        return this;
    }

    public BigDecimal getDyearpaycount() {
        return dyearpaycount;
    }

    public Cardmain setDyearpaycount(BigDecimal dyearpaycount) {
        this.dyearpaycount = dyearpaycount;
        return this;
    }

    public BigDecimal getOmonthpayamount() {
        return omonthpayamount;
    }

    public Cardmain setOmonthpayamount(BigDecimal omonthpayamount) {
        this.omonthpayamount = omonthpayamount;
        return this;
    }

    public BigDecimal getDmonthpayamount() {
        return dmonthpayamount;
    }

    public Cardmain setDmonthpayamount(BigDecimal dmonthpayamount) {
        this.dmonthpayamount = dmonthpayamount;
        return this;
    }

    public BigDecimal getOmonthpaycount() {
        return omonthpaycount;
    }

    public Cardmain setOmonthpaycount(BigDecimal omonthpaycount) {
        this.omonthpaycount = omonthpaycount;
        return this;
    }

    public BigDecimal getDmonthpaycount() {
        return dmonthpaycount;
    }

    public Cardmain setDmonthpaycount(BigDecimal dmonthpaycount) {
        this.dmonthpaycount = dmonthpaycount;
        return this;
    }

    public BigDecimal getJnbean() {
        return jnbean;
    }

    public Cardmain setJnbean(BigDecimal jnbean) {
        this.jnbean = jnbean;
        return this;
    }

    public BigDecimal getBy1() {
        return by1;
    }

    public Cardmain setBy1(BigDecimal by1) {
        this.by1 = by1;
        return this;
    }

    public String getBy2() {
        return by2;
    }

    public Cardmain setBy2(String by2) {
        this.by2 = by2;
        return this;
    }

    public String getBy3() {
        return by3;
    }

    public Cardmain setBy3(String by3) {
        this.by3 = by3;
        return this;
    }

    public String getWxopenid() {
        return wxopenid;
    }

    public Cardmain setWxopenid(String wxopenid) {
        this.wxopenid = wxopenid;
        return this;
    }

    public String getUuid() {
        return uuid;
    }

    public Cardmain setUuid(String uuid) {
        this.uuid = uuid;
        return this;
    }

    public String getUnionid() {
        return unionid;
    }

    public Cardmain setUnionid(String unionid) {
        this.unionid = unionid;
        return this;
    }

    public String getNickname() {
        return nickname;
    }

    public Cardmain setNickname(String nickname) {
        this.nickname = nickname;
        return this;
    }

    public BigDecimal getWid() {
        return wid;
    }

    public Cardmain setWid(BigDecimal wid) {
        this.wid = wid;
        return this;
    }

    public String getHeadimgurl() {
        return headimgurl;
    }

    public Cardmain setHeadimgurl(String headimgurl) {
        this.headimgurl = headimgurl;
        return this;
    }

    public BigDecimal getCardid() {
        return cardid;
    }

    public Cardmain setCardid(BigDecimal cardid) {
        this.cardid = cardid;
        return this;
    }

    public String getCardtypename() {
        return cardtypename;
    }

    public Cardmain setCardtypename(String cardtypename) {
        this.cardtypename = cardtypename;
        return this;
    }

    public BigDecimal getCardtypeid() {
        return cardtypeid;
    }

    public Cardmain setCardtypeid(BigDecimal cardtypeid) {
        this.cardtypeid = cardtypeid;
        return this;
    }

    public BigDecimal getMemberid() {
        return memberid;
    }

    public Cardmain setMemberid(BigDecimal memberid) {
        this.memberid = memberid;
        return this;
    }

    public BigDecimal getMembertypeid() {
        return membertypeid;
    }

    public Cardmain setMembertypeid(BigDecimal membertypeid) {
        this.membertypeid = membertypeid;
        return this;
    }

    public String getMembertypename() {
        return membertypename;
    }

    public Cardmain setMembertypename(String membertypename) {
        this.membertypename = membertypename;
        return this;
    }

    public BigDecimal getStoreid() {
        return storeid;
    }

    public Cardmain setStoreid(BigDecimal storeid) {
        this.storeid = storeid;
        return this;
    }

    public String getStorename() {
        return storename;
    }

    public Cardmain setStorename(String storename) {
        this.storename = storename;
        return this;
    }

    public String getStorecode() {
        return storecode;
    }

    public Cardmain setStorecode(String storecode) {
        this.storecode = storecode;
        return this;
    }

    public BigDecimal getClerkid() {
        return clerkid;
    }

    public Cardmain setClerkid(BigDecimal clerkid) {
        this.clerkid = clerkid;
        return this;
    }

    public String getClerkname() {
        return clerkname;
    }

    public Cardmain setClerkname(String clerkname) {
        this.clerkname = clerkname;
        return this;
    }

    public String getClerkcode() {
        return clerkcode;
    }

    public Cardmain setClerkcode(String clerkcode) {
        this.clerkcode = clerkcode;
        return this;
    }

    public BigDecimal getCustomerid() {
        return customerid;
    }

    public Cardmain setCustomerid(BigDecimal customerid) {
        this.customerid = customerid;
        return this;
    }

    public Long getOpentime() {
        return opentime;
    }

    public Cardmain setOpentime(Long opentime) {
        this.opentime = opentime;
        return this;
    }

    public String getMembercode() {
        return membercode;
    }

    public Cardmain setMembercode(String membercode) {
        this.membercode = membercode;
        return this;
    }

    public String getIsactive() {
        return isactive;
    }

    public Cardmain setIsactive(String isactive) {
        this.isactive = isactive;
        return this;
    }

    public String getOpenchannel() {
        return openchannel;
    }

    public Cardmain setOpenchannel(String openchannel) {
        this.openchannel = openchannel;
        return this;
    }

    public BigDecimal getIntegral() {
        return integral;
    }

    public Cardmain setIntegral(BigDecimal integral) {
        this.integral = integral;
        return this;
    }

    public String getIssubscribe() {
        return issubscribe;
    }

    public Cardmain setIssubscribe(String issubscribe) {
        this.issubscribe = issubscribe;
        return this;
    }

    public String getIsexternaluser() {
        return isexternaluser;
    }

    public Cardmain setIsexternaluser(String isexternaluser) {
        this.isexternaluser = isexternaluser;
        return this;
    }

    public String getIssubscribebox() {
        return issubscribebox;
    }

    public Cardmain setIssubscribebox(String issubscribebox) {
        this.issubscribebox = issubscribebox;
        return this;
    }

    public String getIsreceivebirth() {
        return isreceivebirth;
    }

    public Cardmain setIsreceivebirth(String isreceivebirth) {
        this.isreceivebirth = isreceivebirth;
        return this;
    }

    public Date getLastconsumtime() {
        return lastconsumtime;
    }

    public Cardmain setLastconsumtime(Date lastconsumtime) {
        this.lastconsumtime = lastconsumtime;
        return this;
    }

    public BigDecimal getConsumtimes() {
        return consumtimes;
    }

    public Cardmain setConsumtimes(BigDecimal consumtimes) {
        this.consumtimes = consumtimes;
        return this;
    }

    public BigDecimal getTotconsumamt() {
        return totconsumamt;
    }

    public Cardmain setTotconsumamt(BigDecimal totconsumamt) {
        this.totconsumamt = totconsumamt;
        return this;
    }

    public String getExt1() {
        return ext1;
    }

    public Cardmain setExt1(String ext1) {
        this.ext1 = ext1;
        return this;
    }

    public String getExt2() {
        return ext2;
    }

    public Cardmain setExt2(String ext2) {
        this.ext2 = ext2;
        return this;
    }

    public String getExt3() {
        return ext3;
    }

    public Cardmain setExt3(String ext3) {
        this.ext3 = ext3;
        return this;
    }

    public String getExt4() {
        return ext4;
    }

    public Cardmain setExt4(String ext4) {
        this.ext4 = ext4;
        return this;
    }

    public String getExt5() {
        return ext5;
    }

    public Cardmain setExt5(String ext5) {
        this.ext5 = ext5;
        return this;
    }

    public String getExt6() {
        return ext6;
    }

    public Cardmain setExt6(String ext6) {
        this.ext6 = ext6;
        return this;
    }

    public String getExt7() {
        return ext7;
    }

    public Cardmain setExt7(String ext7) {
        this.ext7 = ext7;
        return this;
    }

    public String getExt8() {
        return ext8;
    }

    public Cardmain setExt8(String ext8) {
        this.ext8 = ext8;
        return this;
    }

    public String getExt9() {
        return ext9;
    }

    public Cardmain setExt9(String ext9) {
        this.ext9 = ext9;
        return this;
    }

    public String getExt10() {
        return ext10;
    }

    public Cardmain setExt10(String ext10) {
        this.ext10 = ext10;
        return this;
    }

    public BigDecimal getMembertime() {
        return membertime;
    }

    public Cardmain setMembertime(BigDecimal membertime) {
        this.membertime = membertime;
        return this;
    }

    public BigDecimal getMemberintegral() {
        return memberintegral;
    }

    public Cardmain setMemberintegral(BigDecimal memberintegral) {
        this.memberintegral = memberintegral;
        return this;
    }

    public String getClerkphone() {
        return clerkphone;
    }

    public Cardmain setClerkphone(String clerkphone) {
        this.clerkphone = clerkphone;
        return this;
    }

    public String getCardstatus() {
        return cardstatus;
    }

    public Cardmain setCardstatus(String cardstatus) {
        this.cardstatus = cardstatus;
        return this;
    }

    public Date getFirstconsumtime() {
        return firstconsumtime;
    }

    public Cardmain setFirstconsumtime(Date firstconsumtime) {
        this.firstconsumtime = firstconsumtime;
        return this;
    }

    public String getExternaluserid() {
        return externaluserid;
    }

    public Cardmain setExternaluserid(String externaluserid) {
        this.externaluserid = externaluserid;
        return this;
    }

    public BigDecimal getCardlvl() {
        return cardlvl;
    }

    public Cardmain setCardlvl(BigDecimal cardlvl) {
        this.cardlvl = cardlvl;
        return this;
    }

    public BigDecimal getMemberlvl() {
        return memberlvl;
    }

    public Cardmain setMemberlvl(BigDecimal memberlvl) {
        this.memberlvl = memberlvl;
        return this;
    }

    public String getCardnum() {
        return cardnum;
    }

    public Cardmain setCardnum(String cardnum) {
        this.cardnum = cardnum;
        return this;
    }

    public Date getFirstconsumetime() {
        return firstconsumetime;
    }

    public Cardmain setFirstconsumetime(Date firstconsumetime) {
        this.firstconsumetime = firstconsumetime;
        return this;
    }

    public Date getLastconsumetime() {
        return lastconsumetime;
    }

    public Cardmain setLastconsumetime(Date lastconsumetime) {
        this.lastconsumetime = lastconsumetime;
        return this;
    }

    public BigDecimal getConsumetimes() {
        return consumetimes;
    }

    public Cardmain setConsumetimes(BigDecimal consumetimes) {
        this.consumetimes = consumetimes;
        return this;
    }

    public BigDecimal getTotconsumeamt() {
        return totconsumeamt;
    }

    public Cardmain setTotconsumeamt(BigDecimal totconsumeamt) {
        this.totconsumeamt = totconsumeamt;
        return this;
    }

    public String getExt11() {
        return ext11;
    }

    public Cardmain setExt11(String ext11) {
        this.ext11 = ext11;
        return this;
    }

    public String getExt12() {
        return ext12;
    }

    public Cardmain setExt12(String ext12) {
        this.ext12 = ext12;
        return this;
    }

    public String getExt13() {
        return ext13;
    }

    public Cardmain setExt13(String ext13) {
        this.ext13 = ext13;
        return this;
    }

    public String getExt14() {
        return ext14;
    }

    public Cardmain setExt14(String ext14) {
        this.ext14 = ext14;
        return this;
    }

    public String getExt15() {
        return ext15;
    }

    public Cardmain setExt15(String ext15) {
        this.ext15 = ext15;
        return this;
    }

    public BigDecimal getFirststoreid() {
        return firststoreid;
    }

    public Cardmain setFirststoreid(BigDecimal firststoreid) {
        this.firststoreid = firststoreid;
        return this;
    }

    public String getFirststorename() {
        return firststorename;
    }

    public Cardmain setFirststorename(String firststorename) {
        this.firststorename = firststorename;
        return this;
    }

    public String getFirststorecode() {
        return firststorecode;
    }

    public Cardmain setFirststorecode(String firststorecode) {
        this.firststorecode = firststorecode;
        return this;
    }

    public BigDecimal getFirstcustomerid() {
        return firstcustomerid;
    }

    public Cardmain setFirstcustomerid(BigDecimal firstcustomerid) {
        this.firstcustomerid = firstcustomerid;
        return this;
    }

    public String getFirstclerkphone() {
        return firstclerkphone;
    }

    public Cardmain setFirstclerkphone(String firstclerkphone) {
        this.firstclerkphone = firstclerkphone;
        return this;
    }

    public BigDecimal getFirstclerkid() {
        return firstclerkid;
    }

    public Cardmain setFirstclerkid(BigDecimal firstclerkid) {
        this.firstclerkid = firstclerkid;
        return this;
    }

    public String getFirstclerkname() {
        return firstclerkname;
    }

    public Cardmain setFirstclerkname(String firstclerkname) {
        this.firstclerkname = firstclerkname;
        return this;
    }

    public String getFirstclerkcode() {
        return firstclerkcode;
    }

    public Cardmain setFirstclerkcode(String firstclerkcode) {
        this.firstclerkcode = firstclerkcode;
        return this;
    }

    @Override
    public String toString() {
        return "CardmainModel{" +
            "id=" + id +
            ", rentid=" + rentid +
            ", weid=" + weid +
            ", openid=" + openid +
            ", cardtype=" + cardtype +
            ", cardno=" + cardno +
            ", username=" + username +
            ", jfye=" + jfye +
            ", jftotal=" + jftotal +
            ", jfxf=" + jfxf +
            ", jfsign=" + jfsign +
            ", xfje=" + xfje +
            ", tel=" + tel +
            ", birthday=" + birthday +
            ", address=" + address +
            ", sex=" + sex +
            ", email=" + email +
            ", status=" + status +
            ", remark=" + remark +
            ", modifydate=" + modifydate +
            ", inputdate=" + inputdate +
            ", validdate=" + validdate +
            ", islink=" + islink +
            ", linksource=" + linksource +
            ", linkid=" + linkid +
            ", roleid=" + roleid +
            ", kfid=" + kfid +
            ", kkstore=" + kkstore +
            ", issend=" + issend +
            ", sendtime=" + sendtime +
            ", cdmunionid=" + cdmunionid +
            ", cdmsourcetype=" + cdmsourcetype +
            ", cdmsourceval=" + cdmsourceval +
            ", ispostfitting=" + ispostfitting +
            ", postfittingtime=" + postfittingtime +
            ", oyearpayamount=" + oyearpayamount +
            ", dyearpayamount=" + dyearpayamount +
            ", oyearpaycount=" + oyearpaycount +
            ", dyearpaycount=" + dyearpaycount +
            ", omonthpayamount=" + omonthpayamount +
            ", dmonthpayamount=" + dmonthpayamount +
            ", omonthpaycount=" + omonthpaycount +
            ", dmonthpaycount=" + dmonthpaycount +
            ", jnbean=" + jnbean +
            ", by1=" + by1 +
            ", by2=" + by2 +
            ", by3=" + by3 +
            ", wxopenid=" + wxopenid +
            ", uuid=" + uuid +
            ", unionid=" + unionid +
            ", nickname=" + nickname +
            ", wid=" + wid +
            ", headimgurl=" + headimgurl +
            ", cardid=" + cardid +
            ", cardtypename=" + cardtypename +
            ", cardtypeid=" + cardtypeid +
            ", memberid=" + memberid +
            ", membertypeid=" + membertypeid +
            ", membertypename=" + membertypename +
            ", storeid=" + storeid +
            ", storename=" + storename +
            ", storecode=" + storecode +
            ", clerkid=" + clerkid +
            ", clerkname=" + clerkname +
            ", clerkcode=" + clerkcode +
            ", customerid=" + customerid +
            ", opentime=" + opentime +
            ", membercode=" + membercode +
            ", isactive=" + isactive +
            ", openchannel=" + openchannel +
            ", integral=" + integral +
            ", issubscribe=" + issubscribe +
            ", isexternaluser=" + isexternaluser +
            ", issubscribebox=" + issubscribebox +
            ", isreceivebirth=" + isreceivebirth +
            ", lastconsumtime=" + lastconsumtime +
            ", consumtimes=" + consumtimes +
            ", totconsumamt=" + totconsumamt +
            ", ext1=" + ext1 +
            ", ext2=" + ext2 +
            ", ext3=" + ext3 +
            ", ext4=" + ext4 +
            ", ext5=" + ext5 +
            ", ext6=" + ext6 +
            ", ext7=" + ext7 +
            ", ext8=" + ext8 +
            ", ext9=" + ext9 +
            ", ext10=" + ext10 +
            ", membertime=" + membertime +
            ", memberintegral=" + memberintegral +
            ", clerkphone=" + clerkphone +
            ", cardstatus=" + cardstatus +
            ", firstconsumtime=" + firstconsumtime +
            ", externaluserid=" + externaluserid +
            ", cardlvl=" + cardlvl +
            ", memberlvl=" + memberlvl +
            ", cardnum=" + cardnum +
            ", firstconsumetime=" + firstconsumetime +
            ", lastconsumetime=" + lastconsumetime +
            ", consumetimes=" + consumetimes +
            ", totconsumeamt=" + totconsumeamt +
            ", ext11=" + ext11 +
            ", ext12=" + ext12 +
            ", ext13=" + ext13 +
            ", ext14=" + ext14 +
            ", ext15=" + ext15 +
            ", firststoreid=" + firststoreid +
            ", firststorename=" + firststorename +
            ", firststorecode=" + firststorecode +
            ", firstcustomerid=" + firstcustomerid +
            ", firstclerkphone=" + firstclerkphone +
            ", firstclerkid=" + firstclerkid +
            ", firstclerkname=" + firstclerkname +
            ", firstclerkcode=" + firstclerkcode +
            "}";
    }
}
