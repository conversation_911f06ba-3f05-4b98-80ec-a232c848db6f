package com.jnby.infrastructure.box.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class WorkFlowModel {

    /**
     *
     */
    private String requestId;


    @ApiModelProperty(value = "流程标题")
    private String requestName;



    @ApiModelProperty(value = "类型")
    private String workFlowName;


    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;


    @ApiModelProperty(value = "客户id")
    private String customerId;



    @ApiModelProperty(value = "品牌架构id")
    private String brandId;

    /**
     *创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;


    @ApiModelProperty(value = "创建时间")
    private String createTime;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String currentNodeType;
}
