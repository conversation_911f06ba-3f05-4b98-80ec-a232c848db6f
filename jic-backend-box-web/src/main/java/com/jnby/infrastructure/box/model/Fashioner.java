package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

@TableName("fashioner")
public class Fashioner {

    @TableId
    private String id;

    @TableField("user_id")
    private String userId;

    @TableField("openid")
    private String openid;

    @TableField("name")
    private String name;

    @TableField("sex")
    private Long sex;

    @TableField("phone")
    private String phone;

    @TableField("memo")
    private String memo;

    @TableField("photo")
    private String photo;

    @TableField("status")
    private Long status;

    @TableField("score")
    private String score;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("qrcode")
    private String qrcode;

    @TableField("qrurl")
    private String qrurl;

    @TableField("expert")
    private String expert;

    @TableField("email")
    private String email;

    @TableField("udesk_id")
    private BigDecimal udeskId;

    @TableField("group_id")
    private String groupId;

    @TableField("min_qrcode")
    private String minQrcode;

    @TableField("min_scene")
    private String minScene;

    @TableField("is_sales")
    private Long isSales;

    @TableField("is_promo")
    private Long isPromo;

    @TableField("constellation")
    private String constellation;

    @TableField("evaluation")
    private String evaluation;

    @TableField("tags")
    private String tags;

    @TableField("priority")
    private Long priority;

    @TableField("oa_id")
    private Long oaId;

    @TableField("wechat_url")
    private String wechatUrl;

    @TableField("wechat")
    private String wechat;

    @TableField("printheadimg")
    private String printheadimg;

    @TableField("IF_USE_MINIAPP")
    private Integer ifUseMiniapp;

    @TableField("HR_EMP_ID")
    private String hrEmpId;

    @TableField("C_STORE_ID")
    private String cStoreId;

    @TableField("FASHIONER_TYPE_ID")
    private String fashionerTypeId;


    @ApiModelProperty(value = "门店类型（0指定门店 1排除门店）")
    @TableField("STORE_TYPE")
    private Long storeType;

    @ApiModelProperty(value = "门店ids")
    @TableField("STORE_IDS")
    private String storeIds;

    public Long getStoreType() {
        return storeType;
    }

    public void setStoreType(Long storeType) {
        this.storeType = storeType;
    }

    public String getStoreIds() {
        return storeIds;
    }

    public void setStoreIds(String storeIds) {
        this.storeIds = storeIds;
    }

    public String getFashionerTypeId() {
        return fashionerTypeId;
    }

    public void setFashionerTypeId(String fashionerTypeId) {
        this.fashionerTypeId = fashionerTypeId;
    }

    public Integer getIfUseMiniapp() {
        return ifUseMiniapp;
    }

    public void setIfUseMiniapp(Integer ifUseMiniapp) {
        this.ifUseMiniapp = ifUseMiniapp;
    }

    public String getWechatUrl() {
        return wechatUrl;
    }

    public void setWechatUrl(String wechatUrl) {
        this.wechatUrl = wechatUrl;
    }

    public String getWechat() {
        return wechat;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat;
    }

    public String getPrintheadimg() {
        return printheadimg;
    }

    public void setPrintheadimg(String printheadimg) {
        this.printheadimg = printheadimg;
    }



    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid == null ? null : openid.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public Long getSex() {
        return sex;
    }

    public void setSex(Long sex) {
        this.sex = sex;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo == null ? null : memo.trim();
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo == null ? null : photo.trim();
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score == null ? null : score.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getQrcode() {
        return qrcode;
    }

    public void setQrcode(String qrcode) {
        this.qrcode = qrcode == null ? null : qrcode.trim();
    }

    public String getQrurl() {
        return qrurl;
    }

    public void setQrurl(String qrurl) {
        this.qrurl = qrurl == null ? null : qrurl.trim();
    }

    public String getExpert() {
        return expert;
    }

    public void setExpert(String expert) {
        this.expert = expert == null ? null : expert.trim();
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    public BigDecimal getUdeskId() {
        return udeskId;
    }

    public void setUdeskId(BigDecimal udeskId) {
        this.udeskId = udeskId;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId == null ? null : groupId.trim();
    }

    public String getMinQrcode() {
        return minQrcode;
    }

    public void setMinQrcode(String minQrcode) {
        this.minQrcode = minQrcode == null ? null : minQrcode.trim();
    }

    public String getMinScene() {
        return minScene;
    }

    public void setMinScene(String minScene) {
        this.minScene = minScene == null ? null : minScene.trim();
    }

    public Long getIsSales() {
        return isSales;
    }

    public void setIsSales(Long isSales) {
        this.isSales = isSales;
    }

    public Long getIsPromo() {
        return isPromo;
    }

    public void setIsPromo(Long isPromo) {
        this.isPromo = isPromo;
    }

    public String getConstellation() {
        return constellation;
    }

    public void setConstellation(String constellation) {
        this.constellation = constellation == null ? null : constellation.trim();
    }

    public String getEvaluation() {
        return evaluation;
    }

    public void setEvaluation(String evaluation) {
        this.evaluation = evaluation == null ? null : evaluation.trim();
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags == null ? null : tags.trim();
    }

    public Long getPriority() {
        return priority;
    }

    public void setPriority(Long priority) {
        this.priority = priority;
    }

    public Long getOaId() {
        return oaId;
    }

    public void setOaId(Long oaId) {
        this.oaId = oaId;
    }

    public String getHrEmpId() {
        return hrEmpId;
    }

    public void setHrEmpId(String hrEmpId) {
        this.hrEmpId = hrEmpId;
    }

    public String getcStoreId() {
        return cStoreId;
    }

    public void setcStoreId(String cStoreId) {
        this.cStoreId = cStoreId;
    }
}
