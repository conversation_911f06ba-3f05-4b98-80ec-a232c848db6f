package com.jnby.infrastructure.box.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jnby.infrastructure.box.model.OaUserWorkflow;
import com.jnby.infrastructure.oa.model.CustomerWithBrand;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @Author: lwz
 * @Date: 2025-01-17 15:12:05
 * @Description: Mapper
 */
public interface OaUserWorkflowMapper extends BaseMapper<OaUserWorkflow> {


    List<OaUserWorkflow> selectByRequestIds(@Param("requestIds") List<String> requestIds);

}
