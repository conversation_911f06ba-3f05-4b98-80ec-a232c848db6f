package com.jnby.infrastructure.box.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jnby.infrastructure.box.model.BSubscribeInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @Author: lwz
 * @Date: 2023-09-07 16:55:46
 * @Description: Mapper
 */
public interface BSubscribeInfoMapper extends BaseMapper<BSubscribeInfo> {


    /**
     * 获取一段时间的增量数据
     *
     * @param fromDate 2024-06-12 13:00:00
     * @param toDate   2024-06-12 14:00:00
     * @return 一段时间的增量数据
     */
    Integer selectIncrementCountByTime(@Param("fromDate") String fromDate, @Param("toDate") String toDate);

    List<BSubscribeInfo> selectIncrementListByTime(@Param("fromDate") String fromDate, @Param("toDate") String toDate,
                                                   @Param("start") Integer start, @Param("end") Integer end);
}
