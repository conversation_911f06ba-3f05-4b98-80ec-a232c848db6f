package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@TableName("express")
@Accessors(chain = true)
@ApiModel(value="express对象", description="")
public class Express implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "id" ,type = IdType.AUTO)
    private String id;
    @TableField("source_id")
    private String sourceId;
    @TableField("express_docno")
    private String expressDocno;
    @TableField("express_name")
    private String expressName;
    @TableField("express_no")
    private String expressNo;
    @TableField("type")
    private Long type;
    @TableField("status")
    @ApiModelProperty(value = "物流状态(10:待取件;20:待签收;30:已签收;40:取消)")
    private Long status;
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;
    @TableField("box_id")
    private String boxId;
    @TableField("orig_id")
    private String origId;
    @TableField("extend_json")
    private String extendJson;
    @TableField("express_order_id")
    private String expressOrderId;
    @TableField("logistics_id")
    private String logisticsId;
    @TableField("logistics_snapshot_id")
    private String logisticsSnapshotId;

    public String getLogisticsSnapshotId() {
        return logisticsSnapshotId;
    }

    public void setLogisticsSnapshotId(String logisticsSnapshotId) {
        this.logisticsSnapshotId = logisticsSnapshotId;
    }

    @TableField("EMP_CODE")
    private String empCode;
    @TableField("EMP_PHONE")
    private String empPhone;


    @TableField("EXPRESS_COMPANY_CODE")
    private String expressCompanyCode;

    public String getExpressCompanyCode() {
        return expressCompanyCode;
    }

    public void setExpressCompanyCode(String expressCompanyCode) {
        this.expressCompanyCode = expressCompanyCode;
    }

    public String getExpressOrderId() {
        return expressOrderId;
    }

    public void setExpressOrderId(String expressOrderId) {
        this.expressOrderId = expressOrderId;
    }

    public String getLogisticsId() {
        return logisticsId;
    }

    public void setLogisticsId(String logisticsId) {
        this.logisticsId = logisticsId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId == null ? null : sourceId.trim();
    }

    public String getExpressDocno() {
        return expressDocno;
    }

    public void setExpressDocno(String expressDocno) {
        this.expressDocno = expressDocno == null ? null : expressDocno.trim();
    }

    public String getExpressName() {
        return expressName;
    }

    public void setExpressName(String expressName) {
        this.expressName = expressName == null ? null : expressName.trim();
    }

    public String getExpressNo() {
        return expressNo;
    }

    public void setExpressNo(String expressNo) {
        this.expressNo = expressNo == null ? null : expressNo.trim();
    }

    public Long getType() {
        return type;
    }

    public void setType(Long type) {
        this.type = type;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getBoxId() {
        return boxId;
    }

    public void setBoxId(String boxId) {
        this.boxId = boxId;
    }

    public String getOrigId() {
        return origId;
    }

    public void setOrigId(String origId) {
        this.origId = origId;
    }

    public String getExtendJson() {
        return extendJson;
    }

    public void setExtendJson(String extendJson) {
        this.extendJson = extendJson;
    }

    public String getEmpCode() {
        return empCode;
    }

    public void setEmpCode(String empCode) {
        this.empCode = empCode;
    }

    public String getEmpPhone() {
        return empPhone;
    }

    public void setEmpPhone(String empPhone) {
        this.empPhone = empPhone;
    }
}
