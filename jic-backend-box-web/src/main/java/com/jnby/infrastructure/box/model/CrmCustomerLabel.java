package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

@Data
public class CrmCustomerLabel {

    @TableField("ID")
    private  String id;

    @TableField("LABEL_NAME")
    private  String labelName;

    @TableField("CRM_CUSTOMER_MAIN_ID")
    private  String crmCustomerMainId;

    @TableField("IS_DEL")
    private  Integer isDel;

    @TableField("CREATE_TIME")
    private Date createTime;

    @TableField("UPDATE_TIME")
    private Date updateTime;


}
