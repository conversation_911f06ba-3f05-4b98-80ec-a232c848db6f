package com.jnby.infrastructure.box.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jnby.infrastructure.box.model.BoxDetailIds;
import com.jnby.infrastructure.box.model.BoxDetails;
import com.jnby.infrastructure.box.model.BoxDetailsWithBLOBs;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BoxDetailsMapper extends BaseMapper<BoxDetailsWithBLOBs> {

    List<BoxDetailsWithBLOBs> selectListByBoxId(String boxId);
    int batchUpdateById(@Param("list") List<BoxDetails> list);

}
