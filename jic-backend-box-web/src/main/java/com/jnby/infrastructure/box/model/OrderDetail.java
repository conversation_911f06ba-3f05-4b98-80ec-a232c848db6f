package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@TableName("order_detail")
public class OrderDetail implements Serializable {
    @TableId("id")
    private String id;
    @TableField("ORDER_ID")
    private String orderId;
    @TableField("PRODUCT_ID")
    private String productId;
    @TableField("PRODUCT_NAME")
    private String productName;
    @TableField("PRODUCT_PRICE")
    private String productPrice;
    @TableField("PRODUCT_FAVORABLE_PRICE")
    private String productFavorablePrice;
    @TableField("VIP_PRICE")
    private String vipPrice;
    @TableField("PRODUCT_SIZE")
    private String productSize;
    @TableField("PRODUCT_QUANTITY")
    private String productQuantity;
    @TableField("PRODUCT_COLOR_NO")
    private String productColorNo;
    @TableField("PRODUCT_COLOR")
    private String productColor;
    @TableField("PRODUCT_BRAND")
    private String productBrand;
    @TableField("STATUS")
    private Long status;
    @TableField("REASON")
    private String reason;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("UPDATE_TIME")
    private Date updateTime;
    @TableField("SKU")
    private String sku;
    @TableField("PRODUCT_CODE")
    private String productCode;
    @TableField("OUT_ID")
    private String outId;
    @TableField("BOX_DETAIL_ID")
    private String boxDetailId;
    @TableField("BIG_SEASON")
    private String bigSeason;
    @TableField("YEAR")
    private String year;
    @TableField("PAID_AMOUNT")
    private Double paidAmount;
    @TableField("USE_VOU")
    private Long useVou;
    @TableField("PRICEACTUAL")
    private String priceactual;
    @TableField("IS_REFUND")
    private Long isRefund;
    @TableField("REFUND_QTY")
    private String refundQty;
    @TableField("EB_NUM")
    private Long ebNum;
    @TableField("EXPRESS_ID")
    private String expressId;
    @TableField("IMG_URL")
    private String imgUrl;

    @TableField("VOUCHER_AMOUNT")
    private BigDecimal voucherAmount;

    // 储值卡
    @TableField("BALANCE_AMT")
    private BigDecimal balanceAmt;

    // 商场代金券金额
    @TableField("SHOP_VOU_AMT")
    private BigDecimal shopVouAmt;

    public BigDecimal getShopVouAmt() {
        return shopVouAmt;
    }

    public void setShopVouAmt(BigDecimal shopVouAmt) {
        this.shopVouAmt = shopVouAmt;
    }

    public BigDecimal getBalanceAmt() {
        return balanceAmt;
    }

    public void setBalanceAmt(BigDecimal balanceAmt) {
        this.balanceAmt = balanceAmt;
    }

    public BigDecimal getVoucherAmount() {
        return voucherAmount;
    }

    public void setVoucherAmount(BigDecimal voucherAmount) {
        this.voucherAmount = voucherAmount;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId == null ? null : productId.trim();
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    public String getProductPrice() {
        return productPrice;
    }

    public void setProductPrice(String productPrice) {
        this.productPrice = productPrice == null ? null : productPrice.trim();
    }

    public String getProductFavorablePrice() {
        return productFavorablePrice;
    }

    public void setProductFavorablePrice(String productFavorablePrice) {
        this.productFavorablePrice = productFavorablePrice == null ? null : productFavorablePrice.trim();
    }

    public String getVipPrice() {
        return vipPrice;
    }

    public void setVipPrice(String vipPrice) {
        this.vipPrice = vipPrice == null ? null : vipPrice.trim();
    }

    public String getProductSize() {
        return productSize;
    }

    public void setProductSize(String productSize) {
        this.productSize = productSize == null ? null : productSize.trim();
    }

    public String getProductQuantity() {
        return productQuantity;
    }

    public void setProductQuantity(String productQuantity) {
        this.productQuantity = productQuantity == null ? null : productQuantity.trim();
    }

    public String getProductColorNo() {
        return productColorNo;
    }

    public void setProductColorNo(String productColorNo) {
        this.productColorNo = productColorNo == null ? null : productColorNo.trim();
    }

    public String getProductColor() {
        return productColor;
    }

    public void setProductColor(String productColor) {
        this.productColor = productColor == null ? null : productColor.trim();
    }

    public String getProductBrand() {
        return productBrand;
    }

    public void setProductBrand(String productBrand) {
        this.productBrand = productBrand == null ? null : productBrand.trim();
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason == null ? null : reason.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku == null ? null : sku.trim();
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode == null ? null : productCode.trim();
    }

    public String getOutId() {
        return outId;
    }

    public void setOutId(String outId) {
        this.outId = outId == null ? null : outId.trim();
    }

    public String getBoxDetailId() {
        return boxDetailId;
    }

    public void setBoxDetailId(String boxDetailId) {
        this.boxDetailId = boxDetailId == null ? null : boxDetailId.trim();
    }

    public String getBigSeason() {
        return bigSeason;
    }

    public void setBigSeason(String bigSeason) {
        this.bigSeason = bigSeason == null ? null : bigSeason.trim();
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year == null ? null : year.trim();
    }

    public Double getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(Double paidAmount) {
        this.paidAmount = paidAmount;
    }

    public Long getUseVou() {
        return useVou;
    }

    public void setUseVou(Long useVou) {
        this.useVou = useVou;
    }

    public String getPriceactual() {
        return priceactual;
    }

    public void setPriceactual(String priceactual) {
        this.priceactual = priceactual == null ? null : priceactual.trim();
    }

    public Long getIsRefund() {
        return isRefund;
    }

    public void setIsRefund(Long isRefund) {
        this.isRefund = isRefund;
    }

    public String getRefundQty() {
        return refundQty;
    }

    public void setRefundQty(String refundQty) {
        this.refundQty = refundQty == null ? null : refundQty.trim();
    }

    public Long getEbNum() {
        return ebNum;
    }

    public void setEbNum(Long ebNum) {
        this.ebNum = ebNum;
    }

    public String getExpressId() {
        return expressId;
    }

    public void setExpressId(String expressId) {
        this.expressId = expressId == null ? null : expressId.trim();
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl == null ? null : imgUrl.trim();
    }
}
