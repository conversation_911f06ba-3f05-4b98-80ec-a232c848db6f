package com.jnby.infrastructure.box.model;

public class BoxWithBLOBs extends Box {
    private String matchMemo;

    private String memo;

    public String getMatchMemo() {
        return matchMemo;
    }

    public void setMatchMemo(String matchMemo) {
        this.matchMemo = matchMemo == null ? null : matchMemo.trim();
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo == null ? null : memo.trim();
    }
}