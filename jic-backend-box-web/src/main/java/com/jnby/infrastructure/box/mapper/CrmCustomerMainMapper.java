package com.jnby.infrastructure.box.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jnby.dto.CrmCustomerListReq;
import com.jnby.dto.ListCountReq;
import com.jnby.infrastructure.box.model.CrmCustomerMain;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【CRM_CUSTOMER_MAIN】的数据库操作Mapper
* @createDate 2024-12-30 14:09:35
* @Entity generator.domain.CrmCustomerMain
*/
public interface CrmCustomerMainMapper extends BaseMapper<CrmCustomerMain> {

    List<CrmCustomerMain> selectCanBindingCustomerMain();

    List<String> selectByParams(CrmCustomerListReq requestData);


    Integer selectCountByParams(ListCountReq requestData);
}




