package com.jnby.infrastructure.box.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jnby.infrastructure.box.model.CrmCustomerRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【CRM_CUSTOMER_RELATION】的数据库操作Mapper
* @createDate 2024-12-30 14:09:35
* @Entity generator.domain.CrmCustomerRelation
*/
public interface CrmCustomerRelationMapper extends BaseMapper<CrmCustomerRelation> {

    List<CrmCustomerRelation> selectByCrmCustomerMainId(@Param("crmCustomerMainId") String oldCrmCustomerMainId);

    void updateByCrmCustomerMainId(@Param("oldCrmCustomerMainId") String oldCrmCustomerMainId, @Param("newCrmCustomerMainId") String newCrmCustomerMainId);

    List<CrmCustomerRelation> selectByCrmCustomerMainIds(@Param("crmCustomerMainIds") List<String> crmCustomerMainIds);
}




