package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName SHORT_LINK_TOOLS
 */
@TableName(value = "SHORT_LINK_TOOLS")
@Data
public class ShortLinkTools implements Serializable {
    /**
     * 主键
     */
    @TableField(value = "ID")
    private String id;

    /**
     * 品牌weid   box定义为  10000  其他则weid
     */
    @TableField(value = "WEID")
    private String weid;

    /**
     * 类型  0  小程序路径   1 小程序带h5链接
     */
    @TableField(value = "PATH_TYPE")
    private String pathType;

    /**
     * 页面名称
     */
    @TableField(value = "PAGE_NAME")
    private String pageName;

    /**
     * 参数设置   0 应用指定渠道并记录落表   1 无需特殊处理
     */
    @TableField(value = "PARAMS_SETTING_TYPE")
    private String paramsSettingType;

    /**
     * 参数设置内容  仅在 应用指定渠道并记录落表  存储值
     */
    @TableField(value = "PARAMS_SETTING_CONTENT")
    private String paramsSettingContent;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 0 未删除  1 已删除
     */
    @TableField(value = "IS_DEL")
    private Integer isDel;

    /**
     * 创建人
     */
    @TableField(value = "CREATE_BY")
    private String createBy;

    /**
     * 更新人
     */
    @TableField(value = "UPDATE_BY")
    private String updateBy;

    /**
     * 所属部门
     */
    @TableField(value = "SYS_ORG_CODE")
    private String sysOrgCode;

    /**
     * 有效类型   0 长期有效  1短期有效
     */
    @TableField(value = "EFFECT_TYPE")
    private Integer effectType;

    /**
     * 有效天数  仅对短期有效有用  
     */
    @TableField(value = "EFFECT_DAYS")
    private Integer effectDays;

    /**
     * 备注
     */
    @TableField(value = "REMARK")
    private String remark;

    /**
     * 小程序路径
     */
    @TableField(value = "PATH")
    private String path;

    /**
     * 非永久的和永久的都是此字段
     */
    @TableField(value = "SHORT_LINK")
    private String shortLink;

    /**
     * 因为永久的需要重新搞一个短链接，每次不是重新生成， 做一个生成时间
     */
    @TableField(value = "PERMANENT_SHORT_LINK")
    private String permanentShortLink;

    /**
     *  永久的  生成时间  如果小于等于29天  那么 直接使用 permanentShortLink  如果大于29天  重新生成一下
     */
    @TableField(value = "PERMANENT_DATE")
    private Date permanentDate;

    @ApiModelProperty(value = "小程序太阳码")
    @TableField(value = "IMG_URL")
    private String imgUrl;

    @ApiModelProperty(value = "h5地址")
    @TableField(value = "H5_URL_LINK")
    private String h5UrlLink;

    @ApiModelProperty(value = "页面类型   0 自研页面  1 saas页面")
    @TableField(value = "PAGE_TYPE")
    private Integer  pageType;

    @ApiModelProperty(value = "系统加参   -1 代表不加参数   数值代表增加的小时数")
    @TableField(value = "SYSTEM_ADD_PARAM")
    private String systemAddParam;


    @ApiModelProperty(value = "0 小时   1 天")
    @TableField(value = "SYSTEM_ADD_HOUR_OR_DAY")
    private String systemAddHourOrDay;

    @ApiModelProperty(value = "生成短连接的路径和参数拼接")
    @TableField(value = "CREATE_SHORT_LINK_PATH_PARAM")
    private String createShortLinkPathParam;


}