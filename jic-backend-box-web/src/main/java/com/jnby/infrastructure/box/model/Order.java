package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@TableName("order_")
public class Order implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(value = "id")
    private String id;

    @TableField("box_sn")
    private String boxSn;

    @TableField("order_sn")
    private String orderSn;

    @TableField("service_sn")
    private String serviceSn;
    @TableField("serial_number")
    private String serialNumber;
    @TableField("daystr")
    private String daystr;
    @TableField("customer_id")
    private String customerId;
    @TableField("trade_no")
    private String tradeNo;
    @TableField("wechat_transaction_id")
    private String wechatTransactionId;
    @TableField("order_status")
    private Long orderStatus;
    @TableField("paid_amount")
    private Double paidAmount;
    @TableField("product_total_price")
    private Double productTotalPrice;
    @TableField("discount_amount")
    private Double discountAmount;
    @TableField("product_discount")
    private Double productDiscount;
    @TableField("vip_discount")
    private Double vipDiscount;
    @TableField("product_total_quantity")
    private String productTotalQuantity;
    @TableField("payment_type")
    private String paymentType;
    @TableField("coin")
    private Double coin;
    @TableField("tracking_number")
    private String trackingNumber;
    @TableField("create_time")
    private Date createTime;
    @TableField("update_time")
    private Date updateTime;
    @TableField("vouchers_no")
    private String vouchersNo;
    @TableField("vou_dis")
    private Double vouDis;
    @TableField("is_yd")
    private Long isYd;
    @TableField("send_time")
    private Date sendTime;
    @TableField("integral")
    private Long integral;
    @TableField("is_ds")
    private Long isDs;
    @TableField("appid")
    private String appid;
    @TableField("isretail")
    private String isretail;
    @TableField("type")
    private Long type;
    @TableField("is_eb")
    private String isEb;
    @TableField("flag")
    private Long flag;
    @TableField("if_have_un_refund_order")
    private Integer ifHaveUnRefundOrder;
    @TableField("eb_express_sync_finish")
    private Integer ebExpressSyncFinish;

    @ApiModelProperty(value = "支付渠道")
    @TableField("pay_channel")
    private String payChannel;

    @ApiModelProperty(value = "收钱吧sn")
    @TableField("sqb_sn")
    private String sqbSn;

    @ApiModelProperty(value = "微盟单号")
    @TableField("weimob_order_sn")
    private String weimobOrderSn;

    @ApiModelProperty(value = "appId")
    @TableField("app_id")
    private String appId;

    @ApiModelProperty(value = "计算id")
    @TableField("calc_id")
    private String calcId;

    // 储值卡金额
    @TableField("balance_amt")
    private BigDecimal balanceAmt;

    // 商场代金券金额
    @TableField("shop_vou_amt")
    private BigDecimal shopVouAmt;

    @ApiModelProperty(value = "联域收款商户号")
    @TableField("merchant_code")
    private String merchantCode;


    public String getMerchantCode() {
        return merchantCode;
    }

    public void setMerchantCode(String merchantCode) {
        this.merchantCode = merchantCode;
    }

    public BigDecimal getShopVouAmt() {
        return shopVouAmt;
    }

    public void setShopVouAmt(BigDecimal shopVouAmt) {
        this.shopVouAmt = shopVouAmt;
    }

    public BigDecimal getBalanceAmt() {
        return balanceAmt;
    }

    public void setBalanceAmt(BigDecimal balanceAmt) {
        this.balanceAmt = balanceAmt;
    }

    public Integer getEbExpressSyncFinish() {
        return ebExpressSyncFinish;
    }

    public void setEbExpressSyncFinish(Integer ebExpressSyncFinish) {
        this.ebExpressSyncFinish = ebExpressSyncFinish;
    }

    public Integer getIfHaveUnRefundOrder() {
        return ifHaveUnRefundOrder;
    }

    public void setIfHaveUnRefundOrder(Integer ifHaveUnRefundOrder) {
        this.ifHaveUnRefundOrder = ifHaveUnRefundOrder;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getBoxSn() {
        return boxSn;
    }

    public void setBoxSn(String boxSn) {
        this.boxSn = boxSn == null ? null : boxSn.trim();
    }

    public String getOrderSn() {
        return orderSn;
    }

    public void setOrderSn(String orderSn) {
        this.orderSn = orderSn == null ? null : orderSn.trim();
    }

    public String getServiceSn() {
        return serviceSn;
    }

    public void setServiceSn(String serviceSn) {
        this.serviceSn = serviceSn == null ? null : serviceSn.trim();
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber == null ? null : serialNumber.trim();
    }

    public String getDaystr() {
        return daystr;
    }

    public void setDaystr(String daystr) {
        this.daystr = daystr == null ? null : daystr.trim();
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId == null ? null : customerId.trim();
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo == null ? null : tradeNo.trim();
    }

    public String getWechatTransactionId() {
        return wechatTransactionId;
    }

    public void setWechatTransactionId(String wechatTransactionId) {
        this.wechatTransactionId = wechatTransactionId == null ? null : wechatTransactionId.trim();
    }

    public Long getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Long orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Double getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(Double paidAmount) {
        this.paidAmount = paidAmount;
    }

    public Double getProductTotalPrice() {
        return productTotalPrice;
    }

    public void setProductTotalPrice(Double productTotalPrice) {
        this.productTotalPrice = productTotalPrice;
    }

    public Double getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(Double discountAmount) {
        this.discountAmount = discountAmount;
    }

    public Double getProductDiscount() {
        return productDiscount;
    }

    public void setProductDiscount(Double productDiscount) {
        this.productDiscount = productDiscount;
    }

    public Double getVipDiscount() {
        return vipDiscount;
    }

    public void setVipDiscount(Double vipDiscount) {
        this.vipDiscount = vipDiscount;
    }

    public String getProductTotalQuantity() {
        return productTotalQuantity;
    }

    public void setProductTotalQuantity(String productTotalQuantity) {
        this.productTotalQuantity = productTotalQuantity == null ? null : productTotalQuantity.trim();
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType == null ? null : paymentType.trim();
    }

    public Double getCoin() {
        return coin;
    }

    public void setCoin(Double coin) {
        this.coin = coin;
    }

    public String getTrackingNumber() {
        return trackingNumber;
    }

    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber == null ? null : trackingNumber.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getVouchersNo() {
        return vouchersNo;
    }

    public void setVouchersNo(String vouchersNo) {
        this.vouchersNo = vouchersNo == null ? null : vouchersNo.trim();
    }

    public Double getVouDis() {
        return vouDis;
    }

    public void setVouDis(Double vouDis) {
        this.vouDis = vouDis;
    }

    public Long getIsYd() {
        return isYd;
    }

    public void setIsYd(Long isYd) {
        this.isYd = isYd;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    public Long getIntegral() {
        return integral;
    }

    public void setIntegral(Long integral) {
        this.integral = integral;
    }

    public Long getIsDs() {
        return isDs;
    }

    public void setIsDs(Long isDs) {
        this.isDs = isDs;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid == null ? null : appid.trim();
    }

    public String getIsretail() {
        return isretail;
    }

    public void setIsretail(String isretail) {
        this.isretail = isretail == null ? null : isretail.trim();
    }

    public Long getType() {
        return type;
    }

    public void setType(Long type) {
        this.type = type;
    }

    public String getIsEb() {
        return isEb;
    }

    public void setIsEb(String isEb) {
        this.isEb = isEb == null ? null : isEb.trim();
    }

    public Long getFlag() {
        return flag;
    }

    public void setFlag(Long flag) {
        this.flag = flag;
    }

    public String getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(String payChannel) {
        this.payChannel = payChannel;
    }

    public String getSqbSn() {
        return sqbSn;
    }

    public void setSqbSn(String sqbSn) {
        this.sqbSn = sqbSn;
    }

    public String getWeimobOrderSn() {
        return weimobOrderSn;
    }

    public void setWeimobOrderSn(String weimobOrderSn) {
        this.weimobOrderSn = weimobOrderSn;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }


    public String getCalcId() {
        return calcId;
    }

    public void setCalcId(String calcId) {
        this.calcId = calcId;
    }

    @Override
    public String toString() {
        return "Order{" +
                "id='" + id + '\'' +
                ", boxSn='" + boxSn + '\'' +
                ", orderSn='" + orderSn + '\'' +
                ", serviceSn='" + serviceSn + '\'' +
                ", serialNumber='" + serialNumber + '\'' +
                ", daystr='" + daystr + '\'' +
                ", customerId='" + customerId + '\'' +
                ", tradeNo='" + tradeNo + '\'' +
                ", wechatTransactionId='" + wechatTransactionId + '\'' +
                ", orderStatus=" + orderStatus +
                ", paidAmount=" + paidAmount +
                ", productTotalPrice=" + productTotalPrice +
                ", discountAmount=" + discountAmount +
                ", productDiscount=" + productDiscount +
                ", vipDiscount=" + vipDiscount +
                ", productTotalQuantity='" + productTotalQuantity + '\'' +
                ", paymentType='" + paymentType + '\'' +
                ", coin=" + coin +
                ", trackingNumber='" + trackingNumber + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", vouchersNo='" + vouchersNo + '\'' +
                ", vouDis=" + vouDis +
                ", isYd=" + isYd +
                ", sendTime=" + sendTime +
                ", integral=" + integral +
                ", isDs=" + isDs +
                ", appid='" + appid + '\'' +
                ", isretail='" + isretail + '\'' +
                ", type=" + type +
                ", isEb='" + isEb + '\'' +
                ", flag=" + flag +
                ", ifHaveUnRefundOrder=" + ifHaveUnRefundOrder +
                ", ebExpressSyncFinish=" + ebExpressSyncFinish +
                ", payChannel='" + payChannel + '\'' +
                ", sqbSn='" + sqbSn + '\'' +
                ", weimobOrderSn='" + weimobOrderSn + '\'' +
                ", appId='" + appId + '\'' +
                ", calcId='" + calcId + '\'' +
                '}';
    }
}
