package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.experimental.Accessors;

/**
 * @Author: lwz
 * @Date: 2025-01-17 17:17:45
 * @Description:
 */
@TableName("OA_WORKFLOW")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@ApiModel(value="OaWorkflow对象", description="")
public class OaWorkflow implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private String id;
    @TableField("REQUEST_ID")
    private String requestId;
    @TableField("REQUEST_NAME")
    private String requestName;
    @TableField("CURRENT_NODE_TYPE")
    private String currentNodeType;
    @TableField("WORK_FLOW_ID")
    private String workFlowId;
    @TableField("CREATE_ID")
    private String createId;
    @TableField("CREATE_TYPE")
    private String createType;

    @TableField("WORK_FLOW_NAME")
    private String workFlowName;
    @TableField("MAIN_ID")
    private String mainId;

    /**
     * 0合同管理
     * 1财务管理
     * 2销售管理
     * 3店仓管理
     * 4装修管理
     * 5解约管理
     * 6准入管理
     */
    @TableField("TAB_TYPE")
    private Long tabType;


    @TableField("DEL_FLAG")
    private Long delFlag;

    @TableField("SYN_TIME")
    private Date synTime;
    @TableField("CREATE_TIME")
    private Date createTime;
    @TableField("CREATE_BY")
    private String createBy;
    @TableField("UPDATE_BY")
    private String updateBy;
    @TableField("UPDATE_TIME")
    private Date updateTime;




    public String getId() {
        return id;
    }

    public OaWorkflow setId(String id) {
        this.id = id;
        return this;
    }

    public String getRequestId() {
        return requestId;
    }

    public OaWorkflow setRequestId(String requestId) {
        this.requestId = requestId;
        return this;
    }

    public String getRequestName() {
        return requestName;
    }

    public OaWorkflow setRequestName(String requestName) {
        this.requestName = requestName;
        return this;
    }

    public String getCurrentNodeType() {
        return currentNodeType;
    }

    public OaWorkflow setCurrentNodeType(String currentNodeType) {
        this.currentNodeType = currentNodeType;
        return this;
    }

    public String getWorkFlowId() {
        return workFlowId;
    }

    public OaWorkflow setWorkFlowId(String workFlowId) {
        this.workFlowId = workFlowId;
        return this;
    }

    public String getCreateId() {
        return createId;
    }

    public OaWorkflow setCreateId(String createId) {
        this.createId = createId;
        return this;
    }

    public String getCreateType() {
        return createType;
    }

    public OaWorkflow setCreateType(String createType) {
        this.createType = createType;
        return this;
    }

    public String getWorkFlowName() {
        return workFlowName;
    }

    public OaWorkflow setWorkFlowName(String workFlowName) {
        this.workFlowName = workFlowName;
        return this;
    }

    public String getMainId() {
        return mainId;
    }

    public OaWorkflow setMainId(String mainId) {
        this.mainId = mainId;
        return this;
    }

    public Date getSynTime() {
        return synTime;
    }

    public OaWorkflow setSynTime(Date synTime) {
        this.synTime = synTime;
        return this;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public OaWorkflow setCreateTime(Date createTime) {
        this.createTime = createTime;
        return this;
    }

    public String getCreateBy() {
        return createBy;
    }

    public OaWorkflow setCreateBy(String createBy) {
        this.createBy = createBy;
        return this;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public OaWorkflow setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
        return this;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public OaWorkflow setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public Long getDelFlag() {
        return delFlag;
    }

    public OaWorkflow setDelFlag(Long delFlag) {
        this.delFlag = delFlag;
        return this;
    }

    public Long getTabType() {
        return tabType;
    }

    public void setTabType(Long tabType) {
        this.tabType = tabType;
    }

    @Override
    public String toString() {
        return "OaWorkflow{" +
                "id='" + id + '\'' +
                ", requestId='" + requestId + '\'' +
                ", requestName='" + requestName + '\'' +
                ", currentNodeType='" + currentNodeType + '\'' +
                ", workFlowId='" + workFlowId + '\'' +
                ", createId='" + createId + '\'' +
                ", createType='" + createType + '\'' +
                ", workFlowName='" + workFlowName + '\'' +
                ", mainId='" + mainId + '\'' +
                ", tabType=" + tabType +
                ", delFlag=" + delFlag +
                ", synTime=" + synTime +
                ", createTime=" + createTime +
                ", createBy='" + createBy + '\'' +
                ", updateBy='" + updateBy + '\'' +
                ", updateTime=" + updateTime +
                '}';
    }
}
