package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

public class CustomerAskBox {
    private String id;

    @TableField("UNIONID")
    private String unionid;

    @TableField("LOGISTICS_ID")
    private String logisticsId;

    @TableField("CREATE_FAS_ID")
    private String createFasId;

    @TableField("SCENE_TAG")
    private String sceneTag;

    @TableField("GOODS_TAG")
    private String goodsTag;

    @TableField("WEIGHT")
    private String weight;

    @TableField("CENNECT_PHONE")
    private String cennectPhone;

    @TableField("WECHAT_NUMBER")
    private String wechatNumber;

    @TableField("IMGS")
    private String imgs;

    @TableField("MSG")
    private String msg;

    @TableField("BOX_ID")
    private String boxId;

    @ApiModelProperty("状态:0待搭配,1已搭配,2已取消")
    @TableField("STATUS")
    private Short status;

    @TableField("CREATE_TIME")
    private Date createTime;

    @TableField("UPDATE_TIME")
    private Date updateTime;

    @TableField("HEIGHT")
    private String height;

    @TableField("BOX_SN")
    private String boxSn;

    @TableField("CUS_GENDER")
    private Short cusGender;

    @TableField("ATTR_VALUE_IDS")
    private String attrValueIds;

    @TableField("CONTACT_TYPE")
    private Short contactType;

    @TableField("SKIP")
    private Short skip;

    @TableField("PRODUCT_SIZE")
    private String productSize;

    @TableField("THEME_ATTR_ID")
    private String themeAttrId;

    @TableField("TOT_BUY_AMOUNT")
    private BigDecimal totBuyAmount;

    @TableField("REMARK")
    private String remark;

    @TableField("VERSION")
    private Integer version;

    @TableField("LAST_FASHIONER_ID")
    private String lastFashionerId;

    @TableField("LAST_FASHIONER_NAME")
    private String lastFashionerName;

    @TableField("IS_SALES")
    private Integer isSales;

    @TableField("IS_CONNECT")
    private Integer isConnect;

    @TableField("CONNECT_TIME")
    private Date connectTime;

    @TableField("CHANNEL_ID")
    private String channelId;

    @TableField("NO_PERFORMANCE")
    private Integer noPerformance;

    @TableField("SALES_PHONE")
    private String salesPhone;

    @TableField("SUBMIT_SALES_FASHIONER_ID")
    private String submitSalesFashionerId;

    @TableField("ACTIVITY_ID")
    private String activityId;

    @ApiModelProperty(value = "是否联系成功   0 未成功  1 成功")
    @TableField("CONNECT_SUCCESS")
    private Integer connectSuccess;

    @TableField("SUB_ID")
    private String subId;

    @TableField("SUB_PLAN_ID")
    private String subPlanId;

    @TableField("ASK_TYPE")
    @ApiModelProperty(value = "1订阅盒子 2单次盒子")
    private Long askType;

    @TableField("CREATE_BY")
    private Long createBy;

     public Long getCreateBy() {
          return createBy;
     }

     public void setCreateBy(Long createBy) {
          this.createBy = createBy;
     }

     public String getSubId() {
        return subId;
    }

    public void setSubId(String subId) {
        this.subId = subId;
    }

    public String getSubPlanId() {
        return subPlanId;
    }

    public void setSubPlanId(String subPlanId) {
        this.subPlanId = subPlanId;
    }

    public Long getAskType() {
        return askType;
    }

    public void setAskType(Long askType) {
        this.askType = askType;
    }

    public Integer getConnectSuccess() {
        return connectSuccess;
    }

    public void setConnectSuccess(Integer connectSuccess) {
        this.connectSuccess = connectSuccess;
    }

    public String getSubmitSalesFashionerId() {
        return submitSalesFashionerId;
    }

    public void setSubmitSalesFashionerId(String submitSalesFashionerId) {
        this.submitSalesFashionerId = submitSalesFashionerId;
    }

    public String getSalesPhone() {
        return salesPhone;
    }

    public void setSalesPhone(String salesPhone) {
        this.salesPhone = salesPhone;
    }


    public Integer getNoPerformance() {
        return noPerformance;
    }

    public void setNoPerformance(Integer noPerformance) {
        this.noPerformance = noPerformance;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public Date getConnectTime() {
        return connectTime;
    }

    public void setConnectTime(Date connectTime) {
        this.connectTime = connectTime;
    }

    public Integer getIsConnect() {
        return isConnect;
    }

    public void setIsConnect(Integer isConnect) {
        this.isConnect = isConnect;
    }

    public Integer getIsSales() {
        return isSales;
    }

    public void setIsSales(Integer isSales) {
        this.isSales = isSales;
    }

    public String getLastFashionerId() {
        return lastFashionerId;
    }

    public void setLastFashionerId(String lastFashionerId) {
        this.lastFashionerId = lastFashionerId;
    }

    public String getLastFashionerName() {
        return lastFashionerName;
    }

    public void setLastFashionerName(String lastFashionerName) {
        this.lastFashionerName = lastFashionerName;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid == null ? null : unionid.trim();
    }

    public String getLogisticsId() {
        return logisticsId;
    }

    public void setLogisticsId(String logisticsId) {
        this.logisticsId = logisticsId == null ? null : logisticsId.trim();
    }

    public String getCreateFasId() {
        return createFasId;
    }

    public void setCreateFasId(String createFasId) {
        this.createFasId = createFasId == null ? null : createFasId.trim();
    }

    public String getSceneTag() {
        return sceneTag;
    }

    public void setSceneTag(String sceneTag) {
        this.sceneTag = sceneTag == null ? null : sceneTag.trim();
    }

    public String getGoodsTag() {
        return goodsTag;
    }

    public void setGoodsTag(String goodsTag) {
        this.goodsTag = goodsTag == null ? null : goodsTag.trim();
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight == null ? null : weight.trim();
    }

    public String getCennectPhone() {
        return cennectPhone;
    }

    public void setCennectPhone(String cennectPhone) {
        this.cennectPhone = cennectPhone == null ? null : cennectPhone.trim();
    }

    public String getWechatNumber() {
        return wechatNumber;
    }

    public void setWechatNumber(String wechatNumber) {
        this.wechatNumber = wechatNumber == null ? null : wechatNumber.trim();
    }

    public String getImgs() {
        return imgs;
    }

    public void setImgs(String imgs) {
        this.imgs = imgs == null ? null : imgs.trim();
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg == null ? null : msg.trim();
    }

    public String getBoxId() {
        return boxId;
    }

    public void setBoxId(String boxId) {
        this.boxId = boxId == null ? null : boxId.trim();
    }

    public Short getStatus() {
        return status;
    }

    public void setStatus(Short status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height == null ? null : height.trim();
    }

    public String getBoxSn() {
        return boxSn;
    }

    public void setBoxSn(String boxSn) {
        this.boxSn = boxSn == null ? null : boxSn.trim();
    }

    public Short getCusGender() {
        return cusGender;
    }

    public void setCusGender(Short cusGender) {
        this.cusGender = cusGender;
    }

    public String getAttrValueIds() {
        return attrValueIds;
    }

    public void setAttrValueIds(String attrValueIds) {
        this.attrValueIds = attrValueIds == null ? null : attrValueIds.trim();
    }

    public Short getContactType() {
        return contactType;
    }

    public void setContactType(Short contactType) {
        this.contactType = contactType;
    }

    public Short getSkip() {
        return skip;
    }

    public void setSkip(Short skip) {
        this.skip = skip;
    }

    public String getProductSize() {
        return productSize;
    }

    public void setProductSize(String productSize) {
        this.productSize = productSize;
    }

    public String getThemeAttrId() {
        return themeAttrId;
    }

    public void setThemeAttrId(String themeAttrId) {
        this.themeAttrId = themeAttrId;
    }

    public BigDecimal getTotBuyAmount() {
        return totBuyAmount;
    }

    public void setTotBuyAmount(BigDecimal totBuyAmount) {
        this.totBuyAmount = totBuyAmount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }
}
