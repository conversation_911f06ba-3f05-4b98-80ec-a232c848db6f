package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: lwz
 * @Date: 2023-09-07 17:01:45
 * @Description:
 */
@TableName("B_SUBSCRIBE_PLAN")
@Data
@ApiModel(value = "BSubscribePlan对象", description = "")
public class BSubscribePlan implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private String id;

    @ApiModelProperty(value = "应用id")
    @TableField("APP_ID")
    private String appId;

    @ApiModelProperty(value = "应用名字")
    @TableField("APP_NAME")
    private String appName;

    @TableField("CUST_ID")
    private String custId;

    @TableField("UNIONID")
    private String unionid;

    @ApiModelProperty(value = "订阅id")
    @TableField("SUB_ID")
    private String subId;

    @TableField("PLAN_MONTH_STR")
    private Long planMonthStr;

    @TableField("PLAN_MONTH")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    private Date planMonth;


    @TableField("PLAN_END_MONTH")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    private Date planEndMonth;

    /**
     *0待要盒  1要盒未搭盒  2已搭盒  3已完成  4已失效
     */
    @ApiModelProperty(value = "0待要盒  1要盒未搭盒  2已搭盒  3已完成  4已失效")
    @TableField("STATUS")
    private Long status;

    @ApiModelProperty(value = "使用数量")
    @TableField("USED_NUM")
    private Long usedNum;


    @ApiModelProperty(value = "BOX_ID")
    @TableField("BOX_ID")
    private String boxId;


    @ApiModelProperty(value = "ASK_BOX_ID")
    @TableField("ASK_BOX_ID")
    private String askBoxId;


    @ApiModelProperty(value = "消费金额")
    @TableField("PAY_PRICE")
    private BigDecimal payPrice;


    @ApiModelProperty(value = "节省金额")
    @TableField("SAVE_PRICE")
    private BigDecimal savePrice;


    @ApiModelProperty(value = "获得积分")
    @TableField("OBTAIN_INTEGRAL")
    private Integer obtainIntegral;


    @ApiModelProperty(value = "备注")
    @TableField("REMARK")
    private String remark;


    @ApiModelProperty(value = "是否发送消息（0否 1是）")
    @TableField("IS_SEND")
    private Integer isSend;


    @ApiModelProperty(value = "是否联系（0否 1是)")
    @TableField("IS_CONTACT")
    private Integer isContact;


    @ApiModelProperty(value = "版本号")
    @TableField("VERSION_COUNT")
    private Integer versionCount;

    @TableField("CREATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    @TableField("CREATE_BY")
    private String createBy;

    @TableField("UPDATE_BY")
    private String updateBy;

    @TableField("UPDATE_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "删除（0否 1是）")
    @TableField("DEL_FLAG")
    private Long delFlag;


}
