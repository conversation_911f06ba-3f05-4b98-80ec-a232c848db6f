package com.jnby.infrastructure.box.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jnby.infrastructure.box.model.CrmGenCode;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CrmGenCodeMapper extends BaseMapper<CrmGenCode> {

    List<CrmGenCode> selectByProviceCityDistrictName(@Param("companyProvince") String companyProvince,
                                                     @Param("companyCity") String companyCity,
                                                     @Param("companyDistrict") String companyDistrict);
}
