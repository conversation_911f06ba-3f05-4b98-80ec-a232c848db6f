package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Table;
import java.util.Date;


public class CrmCustomerRelation {

    @TableField("ID")
    private  String id;

    @TableField("CUSTOMER_CODE")
    private  String customerCode;

    @TableField("CRM_CUSTOMER_MAIN_ID")
    private  String crmCustomerMainId;

    @TableField("IS_DEL")
    private  Integer isDel;

    @TableField("CREATE_TIME")
    private  Date createTime;

    @TableField("UPDATE_TIME")
    private Date updateTime;


    @TableField("C_ARCBRAND_ID")
    private String cArcbrandId;

    @TableField("CUSTOMER_ID")
    private String customerId;

    @TableField("IS_STOP")
    private String isStop;


    @TableField("CUSTOMER_LEVEL")
    private String customerLevel;

    @TableField("CUSTOMER_EVALUATE")
    private String customerEvaluate;


    @ApiModelProperty(value = "法人名称")
    @TableField("LEGAL_PERSON")
    private String legalPerson;

    @ApiModelProperty(value = "法人联系电话")
    @TableField("LEGAL_PERSON_PHONE")
    private String legalPersonPhone;


    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    public String getLegalPersonPhone() {
        return legalPersonPhone;
    }

    public void setLegalPersonPhone(String legalPersonPhone) {
        this.legalPersonPhone = legalPersonPhone;
    }

    public String getCustomerLevel() {
        return customerLevel;
    }

    public void setCustomerLevel(String customerLevel) {
        this.customerLevel = customerLevel;
    }

    public String getCustomerEvaluate() {
        return customerEvaluate;
    }

    public void setCustomerEvaluate(String customerEvaluate) {
        this.customerEvaluate = customerEvaluate;
    }

    public String getIsStop() {
        return isStop;
    }

    public void setIsStop(String isStop) {
        this.isStop = isStop;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCrmCustomerMainId() {
        return crmCustomerMainId;
    }

    public void setCrmCustomerMainId(String crmCustomerMainId) {
        this.crmCustomerMainId = crmCustomerMainId;
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getcArcbrandId() {
        return cArcbrandId;
    }

    public void setcArcbrandId(String cArcbrandId) {
        this.cArcbrandId = cArcbrandId;
    }
}
