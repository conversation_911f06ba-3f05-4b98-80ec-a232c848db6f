package com.jnby.infrastructure.box.model;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

public class BoxRefundDetails {
    private String id;

    private String boxRefundId;

    private String orderDetailId;

    private String refundQty;

    private Long status;

    private Date createTime;

    private Date updateTime;

    private String expressid;

    private Double refundAmount;

    @ApiModelProperty(value = "主表中的remark")
    private String refundRemark;


    @ApiModelProperty(value = "储值卡退款金额")
    private BigDecimal refundBalance;

    @ApiModelProperty(value = "零售单详情id")
    private String retailItemId;

    public String getRetailItemId() {
        return retailItemId;
    }

    public void setRetailItemId(String retailItemId) {
        this.retailItemId = retailItemId;
    }

    public BigDecimal getRefundBalance() {
        return refundBalance;
    }

    public void setRefundBalance(BigDecimal refundBalance) {
        this.refundBalance = refundBalance;
    }

    public String getRefundRemark() {
        return refundRemark;
    }

    public void setRefundRemark(String refundRemark) {
        this.refundRemark = refundRemark;
    }

    public Double getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(Double refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getBoxRefundId() {
        return boxRefundId;
    }

    public void setBoxRefundId(String boxRefundId) {
        this.boxRefundId = boxRefundId == null ? null : boxRefundId.trim();
    }

    public String getOrderDetailId() {
        return orderDetailId;
    }

    public void setOrderDetailId(String orderDetailId) {
        this.orderDetailId = orderDetailId == null ? null : orderDetailId.trim();
    }

    public String getRefundQty() {
        return refundQty;
    }

    public void setRefundQty(String refundQty) {
        this.refundQty = refundQty == null ? null : refundQty.trim();
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getExpressid() {
        return expressid;
    }

    public void setExpressid(String expressid) {
        this.expressid = expressid == null ? null : expressid.trim();
    }
}