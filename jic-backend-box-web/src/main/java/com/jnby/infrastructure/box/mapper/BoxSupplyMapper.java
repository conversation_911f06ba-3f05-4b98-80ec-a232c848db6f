package com.jnby.infrastructure.box.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jnby.infrastructure.box.model.BoxSupply;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BoxSupplyMapper extends BaseMapper<BoxSupply> {

    List<BoxSupply> findByBoxId(@Param("boxId") String boxId);

}
