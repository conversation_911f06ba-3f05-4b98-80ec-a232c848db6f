package com.jnby.infrastructure.box.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class Box implements Serializable {
    private String id;

    private String boxSn;

    private String serialNumber;

    private String daystr;

    private String unionid;

    private String askId;

    private String matchId;

    private String orderId;

    private String logisticsId;

    private Long status;

    private String score;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private String createFasId;

    private Date updateTime;

    private String trackNumber;

    private Long hasLogisticsNotice;

    private Long hasReceiveNotice;

    private Long isWarn;

    private Long isWarnNotice;

    private Date warnTime;

    private Long isEval;

    private Double outId;

    private Long isRead;

    private Long stockType;

    private Long isYd;

    private Long boxType;

    private String customerMemo;

    private String calcBillno;

    private Long isShopvip;

    private BigDecimal shopDiscount;

    private Long type;

    private String evaluationCoupon;

    private Date sendTime;

    private Date signinTime;

    private Date evalTime;

    private Date finishTime;

    private Date buyTime;

    private Date returnTime;

    private String ebyhNo;

    private Long read;

    private Long salesrepId;

    private String fashioner;

    private String sales;

    private Long placleOrder;

    private String pocketTelId;

    private String mergeId;

    private String cancelReason;

    private Short ifFeedback;

    private String salesEval;

    private Short contacts;

    private Date lastContactTime;

    private String extend;

    private String brCodeUrl;

    private Date tryOutTime;

    private String noPayReason;

    private Integer platform;

    private String explicitBrand;

    private Integer isEb;

    private Integer ifWms;

    private String channelId;

    private Integer ifVirDelivery;

    private String appId;


    private String hrEmpId;

    private String cStoreId;

    private String sourceCode;

    private String themeActivityIds;

    private Long closeRefund;

    private String sourceId;

    private Long sourceType;

    private String creditId;

    public String getCreditId() {
        return creditId;
    }

    public void setCreditId(String creditId) {
        this.creditId = creditId;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public Long getSourceType() {
        return sourceType;
    }

    public void setSourceType(Long sourceType) {
        this.sourceType = sourceType;
    }

    public Long getCloseRefund() {
        return closeRefund;
    }

    public void setCloseRefund(Long closeRefund) {
        this.closeRefund = closeRefund;
    }


    public Integer getIfVirDelivery() {
        return ifVirDelivery;
    }

    public void setIfVirDelivery(Integer ifVirDelivery) {
        this.ifVirDelivery = ifVirDelivery;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public Integer getIfWms() {
        return ifWms;
    }

    public void setIfWms(Integer ifWms) {
        this.ifWms = ifWms;
    }

    public String getExplicitBrand() {
        return explicitBrand;
    }

    public void setExplicitBrand(String explicitBrand) {
        this.explicitBrand = explicitBrand;
    }

    public Integer getIsEb() {
        return isEb;
    }

    public void setIsEb(Integer isEb) {
        this.isEb = isEb;
    }


    public Integer getPlatform() {
        return platform;
    }

    public void setPlatform(Integer platform) {
        this.platform = platform;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }


    public String getBrCodeUrl() {
        return brCodeUrl;
    }

    public void setBrCodeUrl(String brCodeUrl) {
        this.brCodeUrl = brCodeUrl;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getBoxSn() {
        return boxSn;
    }

    public void setBoxSn(String boxSn) {
        this.boxSn = boxSn == null ? null :  boxSn.trim();
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber == null ? null : serialNumber.trim();
    }

    public String getDaystr() {
        return daystr;
    }

    public void setDaystr(String daystr) {
        this.daystr = daystr == null ? null : daystr.trim();
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid == null ? null : unionid.trim();
    }

    public String getAskId() {
        return askId;
    }

    public void setAskId(String askId) {
        this.askId = askId == null ? null : askId.trim();
    }

    public String getMatchId() {
        return matchId;
    }

    public void setMatchId(String matchId) {
        this.matchId = matchId == null ? null : matchId.trim();
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    public String getLogisticsId() {
        return logisticsId;
    }

    public void setLogisticsId(String logisticsId) {
        this.logisticsId = logisticsId == null ? null : logisticsId.trim();
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score == null ? null : score.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateFasId() {
        return createFasId;
    }

    public void setCreateFasId(String createFasId) {
        this.createFasId = createFasId == null ? null : createFasId.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getTrackNumber() {
        return trackNumber;
    }

    public void setTrackNumber(String trackNumber) {
        this.trackNumber = trackNumber == null ? null : trackNumber.trim();
    }

    public Long getHasLogisticsNotice() {
        return hasLogisticsNotice;
    }

    public void setHasLogisticsNotice(Long hasLogisticsNotice) {
        this.hasLogisticsNotice = hasLogisticsNotice;
    }

    public Long getHasReceiveNotice() {
        return hasReceiveNotice;
    }

    public void setHasReceiveNotice(Long hasReceiveNotice) {
        this.hasReceiveNotice = hasReceiveNotice;
    }

    public Long getIsWarn() {
        return isWarn;
    }

    public void setIsWarn(Long isWarn) {
        this.isWarn = isWarn;
    }

    public Long getIsWarnNotice() {
        return isWarnNotice;
    }

    public void setIsWarnNotice(Long isWarnNotice) {
        this.isWarnNotice = isWarnNotice;
    }

    public Date getWarnTime() {
        return warnTime;
    }

    public void setWarnTime(Date warnTime) {
        this.warnTime = warnTime;
    }

    public Long getIsEval() {
        return isEval;
    }

    public void setIsEval(Long isEval) {
        this.isEval = isEval;
    }

    public Double getOutId() {
        return outId;
    }

    public void setOutId(Double outId) {
        this.outId = outId;
    }

    public Long getIsRead() {
        return isRead;
    }

    public void setIsRead(Long isRead) {
        this.isRead = isRead;
    }

    public Long getStockType() {
        return stockType;
    }

    public void setStockType(Long stockType) {
        this.stockType = stockType;
    }

    public Long getIsYd() {
        return isYd;
    }

    public void setIsYd(Long isYd) {
        this.isYd = isYd;
    }

    public Long getBoxType() {
        return boxType;
    }

    public void setBoxType(Long boxType) {
        this.boxType = boxType;
    }

    public String getCustomerMemo() {
        return customerMemo;
    }

    public void setCustomerMemo(String customerMemo) {
        this.customerMemo = customerMemo == null ? null : customerMemo.trim();
    }

    public String getCalcBillno() {
        return calcBillno;
    }

    public void setCalcBillno(String calcBillno) {
        this.calcBillno = calcBillno == null ? null : calcBillno.trim();
    }

    public Long getIsShopvip() {
        return isShopvip;
    }

    public void setIsShopvip(Long isShopvip) {
        this.isShopvip = isShopvip;
    }

    public BigDecimal getShopDiscount() {
        return shopDiscount;
    }

    public void setShopDiscount(BigDecimal shopDiscount) {
        this.shopDiscount = shopDiscount;
    }

    public Long getType() {
        return type;
    }

    public void setType(Long type) {
        this.type = type;
    }

    public String getEvaluationCoupon() {
        return evaluationCoupon;
    }

    public void setEvaluationCoupon(String evaluationCoupon) {
        this.evaluationCoupon = evaluationCoupon == null ? null : evaluationCoupon.trim();
    }

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    public Date getSigninTime() {
        return signinTime;
    }

    public void setSigninTime(Date signinTime) {
        this.signinTime = signinTime;
    }

    public Date getEvalTime() {
        return evalTime;
    }

    public void setEvalTime(Date evalTime) {
        this.evalTime = evalTime;
    }

    public Date getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    public Date getBuyTime() {
        return buyTime;
    }

    public void setBuyTime(Date buyTime) {
        this.buyTime = buyTime;
    }

    public Date getReturnTime() {
        return returnTime;
    }

    public void setReturnTime(Date returnTime) {
        this.returnTime = returnTime;
    }

    public String getEbyhNo() {
        return ebyhNo;
    }

    public void setEbyhNo(String ebyhNo) {
        this.ebyhNo = ebyhNo == null ? null : ebyhNo.trim();
    }

    public Long getRead() {
        return read;
    }

    public void setRead(Long read) {
        this.read = read;
    }

    public Long getSalesrepId() {
        return salesrepId;
    }

    public void setSalesrepId(Long salesrepId) {
        this.salesrepId = salesrepId;
    }

    public String getFashioner() {
        return fashioner;
    }

    public void setFashioner(String fashioner) {
        this.fashioner = fashioner == null ? null : fashioner.trim();
    }

    public String getSales() {
        return sales;
    }

    public void setSales(String sales) {
        this.sales = sales == null ? null : sales.trim();
    }

    public Long getPlacleOrder() {
        return placleOrder;
    }

    public void setPlacleOrder(Long placleOrder) {
        this.placleOrder = placleOrder;
    }

    public String getPocketTelId() {
        return pocketTelId;
    }

    public void setPocketTelId(String pocketTelId) {
        this.pocketTelId = pocketTelId == null ? null : pocketTelId.trim();
    }

    public String getMergeId() {
        return mergeId;
    }

    public void setMergeId(String mergeId) {
        this.mergeId = mergeId == null ? null : mergeId.trim();
    }

    public String getCancelReason() {
        return cancelReason;
    }

    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason == null ? null : cancelReason.trim();
    }

    public Short getIfFeedback() {
        return ifFeedback;
    }

    public void setIfFeedback(Short ifFeedback) {
        this.ifFeedback = ifFeedback;
    }

    public String getSalesEval() {
        return salesEval;
    }

    public void setSalesEval(String salesEval) {
        this.salesEval = salesEval == null ? null : salesEval.trim();
    }

    public Short getContacts() {
        return contacts;
    }

    public void setContacts(Short contacts) {
        this.contacts = contacts;
    }

    public Date getLastContactTime() {
        return lastContactTime;
    }

    public void setLastContactTime(Date lastContactTime) {
        this.lastContactTime = lastContactTime;
    }

    public Date getTryOutTime() {
        return tryOutTime;
    }

    public void setTryOutTime(Date tryOutTime) {
        this.tryOutTime = tryOutTime;
    }

    public String getNoPayReason() {
        return noPayReason;
    }

    public void setNoPayReason(String noPayReason) {
        this.noPayReason = noPayReason;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public String getThemeActivityIds() {
        return themeActivityIds;
    }

    public void setThemeActivityIds(String themeActivityIds) {
        this.themeActivityIds = themeActivityIds;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getHrEmpId() {
        return hrEmpId;
    }

    public void setHrEmpId(String hrEmpId) {
        this.hrEmpId = hrEmpId;
    }

    public String getcStoreId() {
        return cStoreId;
    }

    public void setcStoreId(String cStoreId) {
        this.cStoreId = cStoreId;
    }
}
