package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;


import java.io.Serializable;
import java.util.Date;


/**
 * @Author: lwz
 * @Date: 2025-01-17 17:38:19
 * @Description:
 */
@TableName("OA_USER_WORKFLOW")
@ApiModel(value = "OaUserWorkflow对象", description = "")
public class OaUserWorkflow implements Serializable {

    private static final long serialVersionUID = 1L;
    @TableId(value = "ID")
    private String id;

    @TableField("REQUEST_ID")
    private String requestId;

    @TableField("MAIN_ID")
    private String mainId;

    @TableField("DETAIL_ID")
    private String detailId;

    @TableField("CUSTOMER_ID")
    private String customerId;

    @TableField("CUSTOMER_NAME")
    private String customerName;
    @TableField("BRAND_ID")
    private String brandId;
    @TableField("BRAND_NAME")
    private String brandName;
    @TableField("HT_START_TIME")
    private String htStartTime;

    @TableField("CREATE_TIME")
    private Date createTime;

    @TableField("SYN_TIME")
    private Date synTime;
    @TableField("CREATE_BY")
    private String createBy;
    @TableField("UPDATE_BY")
    private String updateBy;
    @TableField("UPDATE_TIME")
    private Date updateTime;
    @TableField("DEL_FLAG")
    private Long delFlag;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getMainId() {
        return mainId;
    }

    public void setMainId(String mainId) {
        this.mainId = mainId;
    }

    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getBrandId() {
        return brandId;
    }

    public void setBrandId(String brandId) {
        this.brandId = brandId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Long delFlag) {
        this.delFlag = delFlag;
    }

    public String getHtStartTime() {
        return htStartTime;
    }

    public void setHtStartTime(String htStartTime) {
        this.htStartTime = htStartTime;
    }

    public Date getSynTime() {
        return synTime;
    }

    public void setSynTime(Date synTime) {
        this.synTime = synTime;
    }

    @Override
    public String toString() {
        return "OaUserWorkflow{" +
                "id='" + id + '\'' +
                ", requestId='" + requestId + '\'' +
                ", mainId='" + mainId + '\'' +
                ", detailId='" + detailId + '\'' +
                ", customerId='" + customerId + '\'' +
                ", customerName='" + customerName + '\'' +
                ", brandId='" + brandId + '\'' +
                ", brandName='" + brandName + '\'' +
                ", htStartTime='" + htStartTime + '\'' +
                ", createTime=" + createTime +
                ", synTime=" + synTime +
                ", createBy='" + createBy + '\'' +
                ", updateBy='" + updateBy + '\'' +
                ", updateTime=" + updateTime +
                ", delFlag=" + delFlag +
                '}';
    }


}
