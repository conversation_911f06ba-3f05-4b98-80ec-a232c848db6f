package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.experimental.Accessors;

@TableName("box_details")
@Accessors(chain = true)
@ApiModel(value="boxDetails对象", description="")
public class BoxDetailsWithBLOBs extends BoxDetails {

    @TableField("img_url")
    private String imgUrl;

    @TableField("sales_free_tags")
    private String salesFreeTags;

    @TableField(exist = false)
    @ApiModelProperty(value = "售后单详情状态 0 无售后状态 2 待退款 3 已成功退款 4 申请驳回  5 取消退款  ")
    private Long boxRefundDetailsStatus = 0L;

    @TableField(exist = false)
    @ApiModelProperty(value = "售后单ID")
    private String boxRefundId;

    @TableField(exist = false)
    @ApiModelProperty(value = "售后单备注")
    private String boxRefundRemark;

    @TableField(exist = false)
    @ApiModelProperty(value = "售后单SN")
    private String boxRefundSn;


    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl == null ? null : imgUrl.trim();
    }

    public String getSalesFreeTags() {
        return salesFreeTags;
    }

    public void setSalesFreeTags(String salesFreeTags) {
        this.salesFreeTags = salesFreeTags == null ? null : salesFreeTags.trim();
    }

    public Long getBoxRefundDetailsStatus() {
        return boxRefundDetailsStatus;
    }

    public void setBoxRefundDetailsStatus(Long boxRefundDetailsStatus) {
        this.boxRefundDetailsStatus = boxRefundDetailsStatus;
    }

    public String getBoxRefundId() {
        return boxRefundId;
    }

    public void setBoxRefundId(String boxRefundId) {
        this.boxRefundId = boxRefundId;
    }

    public String getBoxRefundRemark() {
        return boxRefundRemark;
    }

    public void setBoxRefundRemark(String boxRefundRemark) {
        this.boxRefundRemark = boxRefundRemark;
    }

    public String getBoxRefundSn() {
        return boxRefundSn;
    }

    public void setBoxRefundSn(String boxRefundSn) {
        this.boxRefundSn = boxRefundSn;
    }
}
