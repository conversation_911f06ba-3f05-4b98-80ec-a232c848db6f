package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

@TableName("customer_details")
public class CustomerDetails {
    @TableId
    private String id;

    @TableField("unionid")
    private String unionid;
    @TableField("out_id")
    private Long outId;
    @TableField("openid")
    private String openid;
    @TableField("nick_name")
    private String nickName;
    @TableField("head_url")
    private String headUrl;
    @TableField("phone")
    private String phone;
    @TableField("coin")
    private String coin;
    @TableField("weight")
    private String weight;
    @TableField("height")
    private String height;
    @TableField("skin")
    private String skin;
    @TableField("body")
    private String body;
    @TableField("gender")
    private Long gender;
    @TableField("trousers")
    private String trousers;
    @TableField("shoes")
    private String shoes;
    @TableField("coat")
    private String coat;
    @TableField("shirt")
    private String shirt;
    @TableField("underwear")
    private String underwear;
    @TableField("brand")
    private String brand;
    @TableField("top_clothing")
    private String topClothing;
    @TableField("under_clothing")
    private String underClothing;
    @TableField("sub_expire_time")
    private Date subExpireTime;
    @TableField("fashioner_id")
    private String fashionerId;
    @TableField("survey_id")
    private String surveyId;
    @TableField("create_time")
    private Date createTime;
    @TableField("update_time")
    private Date updateTime;
    @TableField("has_answer")
    private Long hasAnswer;
    @TableField("show_addr_notice")
    private Long showAddrNotice;
    @TableField("reg_from")
    private String regFrom;
    @TableField("reg_type")
    private Long regType;
    @TableField("category_id")
    private String categoryId;
    @TableField("push_memo")
    private String pushMemo;
    @TableField("udesk_id")
    private BigDecimal udeskId;
    @TableField("shoulder_width")
    private String shoulderWidth;
    @TableField("bust")
    private String bust;
    @TableField("hipline")
    private String hipline;
    @TableField("waistline")
    private String waistline;
    @TableField("invite_code")
    private String inviteCode;
    @TableField("min_qrcode")
    private String minQrcode;
    @TableField("credit_status")
    private Long creditStatus;
    @TableField("body_feature")
    private String bodyFeature;
    @TableField("base_color")
    private String baseColor;
    @TableField("color")
    private String color;
    @TableField("fabric")
    private String fabric;
    @TableField("constellation")
    private String constellation;
    @TableField("birthday")
    private String birthday;
    @TableField("customer_type_id")
    private String customerTypeId;
    @TableField("equity_type")
    private Long equityType;
    @TableField("referrer_id")
    private String referrerId;
    @TableField("cash")
    private Double cash;
    @TableField("partner_id")
    private String partnerId;
    @TableField("equity_id")
    private String equityId;
    @TableField("is_tips")
    private Long isTips;
    @TableField("reg_brand")
    private String regBrand;
    @TableField("endtype")
    private Long endtype;
    @TableField("activity_subscribe")
    private Short activitySubscribe;
    @TableField("head")
    private String head;
    @TableField("gb")
    private String gb;
    @TableField("yao")
    private String yao;
    @TableField("styles")
    private String styles;
    @TableField("leg")
    private String leg;
    @TableField("touch_time")
    private Date touchTime;
    @TableField("touch_num")
    private Short touchNum;
    @TableField("rm_num")
    private Short rmNum;
    @TableField("viptype_id")
    private Short viptypeId;
    @TableField("viptype_name")
    private String viptypeName;
    @TableField("last_buy_brand")
    private String lastBuyBrand;
    @TableField("style")
    private String style;
    @TableField("jnby_cardno")
    private String jnbyCardNo;
    @TableField("bg_remark")
    private String bgRemark;

    @ApiModelProperty(value = "累计消费金额")
    @TableField("tot_actual_amount")
    private String totActualAmount;


    @ApiModelProperty(value = "渠道id")
    @TableField("CHANNEL_ID")
    private String channelId;
    @ApiModelProperty(value = "是否首次主动要盒")
    @TableField("FIRST_ASK_BOX")
    private Long firstAskBox;

    public Long getFirstAskBox() {
        return firstAskBox;
    }

    public void setFirstAskBox(Long firstAskBox) {
        this.firstAskBox = firstAskBox;
    }

    public String getTotActualAmount() {
        return totActualAmount;
    }

    public void setTotActualAmount(String totActualAmount) {
        this.totActualAmount = totActualAmount;
    }

    public String getBgRemark() {
        return bgRemark;
    }

    public void setBgRemark(String bgRemark) {
        this.bgRemark = bgRemark;
    }

    public String getJnbyCardNo() {
        return jnbyCardNo;
    }

    public void setJnbyCardNo(String jnbyCardNo) {
        this.jnbyCardNo = jnbyCardNo;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getUnionid() {
        return unionid;
    }

    public void setUnionid(String unionid) {
        this.unionid = unionid == null ? null : unionid.trim();
    }

    public Long getOutId() {
        return outId;
    }

    public void setOutId(Long outId) {
        this.outId = outId;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid == null ? null : openid.trim();
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName == null ? null : nickName.trim();
    }

    public String getHeadUrl() {
        return headUrl;
    }

    public void setHeadUrl(String headUrl) {
        this.headUrl = headUrl == null ? null : headUrl.trim();
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    public String getCoin() {
        return coin;
    }

    public void setCoin(String coin) {
        this.coin = coin == null ? null : coin.trim();
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight == null ? null : weight.trim();
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height == null ? null : height.trim();
    }

    public String getSkin() {
        return skin;
    }

    public void setSkin(String skin) {
        this.skin = skin == null ? null : skin.trim();
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body == null ? null : body.trim();
    }

    public Long getGender() {
        return gender;
    }

    public void setGender(Long gender) {
        this.gender = gender;
    }

    public String getTrousers() {
        return trousers;
    }

    public void setTrousers(String trousers) {
        this.trousers = trousers == null ? null : trousers.trim();
    }

    public String getShoes() {
        return shoes;
    }

    public void setShoes(String shoes) {
        this.shoes = shoes == null ? null : shoes.trim();
    }

    public String getCoat() {
        return coat;
    }

    public void setCoat(String coat) {
        this.coat = coat == null ? null : coat.trim();
    }

    public String getShirt() {
        return shirt;
    }

    public void setShirt(String shirt) {
        this.shirt = shirt == null ? null : shirt.trim();
    }

    public String getUnderwear() {
        return underwear;
    }

    public void setUnderwear(String underwear) {
        this.underwear = underwear == null ? null : underwear.trim();
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand == null ? null : brand.trim();
    }

    public String getTopClothing() {
        return topClothing;
    }

    public void setTopClothing(String topClothing) {
        this.topClothing = topClothing == null ? null : topClothing.trim();
    }

    public String getUnderClothing() {
        return underClothing;
    }

    public void setUnderClothing(String underClothing) {
        this.underClothing = underClothing == null ? null : underClothing.trim();
    }

    public Date getSubExpireTime() {
        return subExpireTime;
    }

    public void setSubExpireTime(Date subExpireTime) {
        this.subExpireTime = subExpireTime;
    }

    public String getFashionerId() {
        return fashionerId;
    }

    public void setFashionerId(String fashionerId) {
        this.fashionerId = fashionerId == null ? null : fashionerId.trim();
    }

    public String getSurveyId() {
        return surveyId;
    }

    public void setSurveyId(String surveyId) {
        this.surveyId = surveyId == null ? null : surveyId.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getHasAnswer() {
        return hasAnswer;
    }

    public void setHasAnswer(Long hasAnswer) {
        this.hasAnswer = hasAnswer;
    }

    public Long getShowAddrNotice() {
        return showAddrNotice;
    }

    public void setShowAddrNotice(Long showAddrNotice) {
        this.showAddrNotice = showAddrNotice;
    }

    public String getRegFrom() {
        return regFrom;
    }

    public void setRegFrom(String regFrom) {
        this.regFrom = regFrom == null ? null : regFrom.trim();
    }

    public Long getRegType() {
        return regType;
    }

    public void setRegType(Long regType) {
        this.regType = regType;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId == null ? null : categoryId.trim();
    }

    public String getPushMemo() {
        return pushMemo;
    }

    public void setPushMemo(String pushMemo) {
        this.pushMemo = pushMemo == null ? null : pushMemo.trim();
    }

    public BigDecimal getUdeskId() {
        return udeskId;
    }

    public void setUdeskId(BigDecimal udeskId) {
        this.udeskId = udeskId;
    }

    public String getShoulderWidth() {
        return shoulderWidth;
    }

    public void setShoulderWidth(String shoulderWidth) {
        this.shoulderWidth = shoulderWidth == null ? null : shoulderWidth.trim();
    }

    public String getBust() {
        return bust;
    }

    public void setBust(String bust) {
        this.bust = bust == null ? null : bust.trim();
    }

    public String getHipline() {
        return hipline;
    }

    public void setHipline(String hipline) {
        this.hipline = hipline == null ? null : hipline.trim();
    }

    public String getWaistline() {
        return waistline;
    }

    public void setWaistline(String waistline) {
        this.waistline = waistline == null ? null : waistline.trim();
    }

    public String getInviteCode() {
        return inviteCode;
    }

    public void setInviteCode(String inviteCode) {
        this.inviteCode = inviteCode == null ? null : inviteCode.trim();
    }

    public String getMinQrcode() {
        return minQrcode;
    }

    public void setMinQrcode(String minQrcode) {
        this.minQrcode = minQrcode == null ? null : minQrcode.trim();
    }

    public Long getCreditStatus() {
        return creditStatus;
    }

    public void setCreditStatus(Long creditStatus) {
        this.creditStatus = creditStatus;
    }

    public String getBodyFeature() {
        return bodyFeature;
    }

    public void setBodyFeature(String bodyFeature) {
        this.bodyFeature = bodyFeature == null ? null : bodyFeature.trim();
    }

    public String getBaseColor() {
        return baseColor;
    }

    public void setBaseColor(String baseColor) {
        this.baseColor = baseColor == null ? null : baseColor.trim();
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color == null ? null : color.trim();
    }

    public String getFabric() {
        return fabric;
    }

    public void setFabric(String fabric) {
        this.fabric = fabric == null ? null : fabric.trim();
    }

    public String getConstellation() {
        return constellation;
    }

    public void setConstellation(String constellation) {
        this.constellation = constellation == null ? null : constellation.trim();
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday == null ? null : birthday.trim();
    }

    public String getCustomerTypeId() {
        return customerTypeId;
    }

    public void setCustomerTypeId(String customerTypeId) {
        this.customerTypeId = customerTypeId == null ? null : customerTypeId.trim();
    }

    public Long getEquityType() {
        return equityType;
    }

    public void setEquityType(Long equityType) {
        this.equityType = equityType;
    }

    public String getReferrerId() {
        return referrerId;
    }

    public void setReferrerId(String referrerId) {
        this.referrerId = referrerId == null ? null : referrerId.trim();
    }

    public Double getCash() {
        return cash;
    }

    public void setCash(Double cash) {
        this.cash = cash;
    }

    public String getPartnerId() {
        return partnerId;
    }

    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId == null ? null : partnerId.trim();
    }

    public String getEquityId() {
        return equityId;
    }

    public void setEquityId(String equityId) {
        this.equityId = equityId == null ? null : equityId.trim();
    }

    public Long getIsTips() {
        return isTips;
    }

    public void setIsTips(Long isTips) {
        this.isTips = isTips;
    }

    public String getRegBrand() {
        return regBrand;
    }

    public void setRegBrand(String regBrand) {
        this.regBrand = regBrand == null ? null : regBrand.trim();
    }

    public Long getEndtype() {
        return endtype;
    }

    public void setEndtype(Long endtype) {
        this.endtype = endtype;
    }

    public Short getActivitySubscribe() {
        return activitySubscribe;
    }

    public void setActivitySubscribe(Short activitySubscribe) {
        this.activitySubscribe = activitySubscribe;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head == null ? null : head.trim();
    }

    public String getGb() {
        return gb;
    }

    public void setGb(String gb) {
        this.gb = gb == null ? null : gb.trim();
    }

    public String getYao() {
        return yao;
    }

    public void setYao(String yao) {
        this.yao = yao == null ? null : yao.trim();
    }

    public String getStyles() {
        return styles;
    }

    public void setStyles(String styles) {
        this.styles = styles == null ? null : styles.trim();
    }

    public String getLeg() {
        return leg;
    }

    public void setLeg(String leg) {
        this.leg = leg == null ? null : leg.trim();
    }

    public Date getTouchTime() {
        return touchTime;
    }

    public void setTouchTime(Date touchTime) {
        this.touchTime = touchTime;
    }

    public Short getTouchNum() {
        return touchNum;
    }

    public void setTouchNum(Short touchNum) {
        this.touchNum = touchNum;
    }

    public Short getRmNum() {
        return rmNum;
    }

    public void setRmNum(Short rmNum) {
        this.rmNum = rmNum;
    }

    public Short getViptypeId() {
        return viptypeId;
    }

    public void setViptypeId(Short viptypeId) {
        this.viptypeId = viptypeId;
    }

    public String getViptypeName() {
        return viptypeName;
    }

    public void setViptypeName(String viptypeName) {
        this.viptypeName = viptypeName == null ? null : viptypeName.trim();
    }

    public String getLastBuyBrand() {
        return lastBuyBrand;
    }

    public void setLastBuyBrand(String lastBuyBrand) {
        this.lastBuyBrand = lastBuyBrand == null ? null : lastBuyBrand.trim();
    }

    public String getStyle() {
        return style;
    }

    public void setStyle(String style) {
        this.style = style == null ? null : style.trim();
    }


    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    @Override
    public String toString() {
        return "CustomerDetails{" +
                "id='" + id + '\'' +
                ", unionid='" + unionid + '\'' +
                ", outId=" + outId +
                ", openid='" + openid + '\'' +
                ", nickName='" + nickName + '\'' +
                ", headUrl='" + headUrl + '\'' +
                ", phone='" + phone + '\'' +
                ", coin='" + coin + '\'' +
                ", weight='" + weight + '\'' +
                ", height='" + height + '\'' +
                ", skin='" + skin + '\'' +
                ", body='" + body + '\'' +
                ", gender=" + gender +
                ", trousers='" + trousers + '\'' +
                ", shoes='" + shoes + '\'' +
                ", coat='" + coat + '\'' +
                ", shirt='" + shirt + '\'' +
                ", underwear='" + underwear + '\'' +
                ", brand='" + brand + '\'' +
                ", topClothing='" + topClothing + '\'' +
                ", underClothing='" + underClothing + '\'' +
                ", subExpireTime=" + subExpireTime +
                ", fashionerId='" + fashionerId + '\'' +
                ", surveyId='" + surveyId + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", hasAnswer=" + hasAnswer +
                ", showAddrNotice=" + showAddrNotice +
                ", regFrom='" + regFrom + '\'' +
                ", regType=" + regType +
                ", categoryId='" + categoryId + '\'' +
                ", pushMemo='" + pushMemo + '\'' +
                ", udeskId=" + udeskId +
                ", shoulderWidth='" + shoulderWidth + '\'' +
                ", bust='" + bust + '\'' +
                ", hipline='" + hipline + '\'' +
                ", waistline='" + waistline + '\'' +
                ", inviteCode='" + inviteCode + '\'' +
                ", minQrcode='" + minQrcode + '\'' +
                ", creditStatus=" + creditStatus +
                ", bodyFeature='" + bodyFeature + '\'' +
                ", baseColor='" + baseColor + '\'' +
                ", color='" + color + '\'' +
                ", fabric='" + fabric + '\'' +
                ", constellation='" + constellation + '\'' +
                ", birthday='" + birthday + '\'' +
                ", customerTypeId='" + customerTypeId + '\'' +
                ", equityType=" + equityType +
                ", referrerId='" + referrerId + '\'' +
                ", cash=" + cash +
                ", partnerId='" + partnerId + '\'' +
                ", equityId='" + equityId + '\'' +
                ", isTips=" + isTips +
                ", regBrand='" + regBrand + '\'' +
                ", endtype=" + endtype +
                ", activitySubscribe=" + activitySubscribe +
                ", head='" + head + '\'' +
                ", gb='" + gb + '\'' +
                ", yao='" + yao + '\'' +
                ", styles='" + styles + '\'' +
                ", leg='" + leg + '\'' +
                ", touchTime=" + touchTime +
                ", touchNum=" + touchNum +
                ", rmNum=" + rmNum +
                ", viptypeId=" + viptypeId +
                ", viptypeName='" + viptypeName + '\'' +
                ", lastBuyBrand='" + lastBuyBrand + '\'' +
                ", style='" + style + '\'' +
                ", jnbyCardNo='" + jnbyCardNo + '\'' +
                ", bgRemark='" + bgRemark + '\'' +
                ", totActualAmount='" + totActualAmount + '\'' +
                ", channelId='" + channelId + '\'' +
                '}';
    }
}
