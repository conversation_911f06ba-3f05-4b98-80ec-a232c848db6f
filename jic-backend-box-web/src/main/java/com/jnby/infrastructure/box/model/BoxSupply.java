package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("box_supply")
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(value="boxSupply对象", description="")
public class BoxSupply {
    @TableId(value = "id")
    private String id;

    @TableField("box_id")
    private String boxId;

    @TableField("supply_sn")
    private String supplySn;

    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    @TableField("status")
    private Long status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @TableField("update_time")
    private Date updateTime;

    @TableField("track_number")
    private String trackNumber;

    @TableField("supply_count")
    private Long supplyCount;

    @TableField("supply_price")
    private String supplyPrice;

    @TableField("supply_reason")
    private String supplyReason;

    @TableField("logistics_id")
    private String logisticsId;

    @TableField("customer_id")
    private String customerId;

    @TableField("if_vir_delivery")
    private Integer ifVirDelivery;

    public Integer getIfVirDelivery() {
        return ifVirDelivery;
    }

    public void setIfVirDelivery(Integer ifVirDelivery) {
        this.ifVirDelivery = ifVirDelivery;
    }

    @TableField("sign_in_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date signInTime;

    public Date getSignInTime() {
        return signInTime;
    }

    public void setSignInTime(Date signInTime) {
        this.signInTime = signInTime;
    }

    public Long getSupplyCount() {
        return supplyCount;
    }

    public void setSupplyCount(Long supplyCount) {
        this.supplyCount = supplyCount;
    }

    public String getSupplyPrice() {
        return supplyPrice;
    }

    public void setSupplyPrice(String supplyPrice) {
        this.supplyPrice = supplyPrice;
    }

    public String getSupplyReason() {
        return supplyReason;
    }

    public void setSupplyReason(String supplyReason) {
        this.supplyReason = supplyReason;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getBoxId() {
        return boxId;
    }

    public void setBoxId(String boxId) {
        this.boxId = boxId == null ? null : boxId.trim();
    }

    public String getSupplySn() {
        return supplySn;
    }

    public void setSupplySn(String supplySn) {
        this.supplySn = supplySn == null ? null : supplySn.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getTrackNumber() {
        return trackNumber;
    }

    public void setTrackNumber(String trackNumber) {
        this.trackNumber = trackNumber == null ? null : trackNumber.trim();
    }

    public String getLogisticsId() {
        return logisticsId;
    }

    public void setLogisticsId(String logisticsId) {
        this.logisticsId = logisticsId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
}
