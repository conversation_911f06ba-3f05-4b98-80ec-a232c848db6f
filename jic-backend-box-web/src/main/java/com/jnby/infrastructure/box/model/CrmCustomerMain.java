package com.jnby.infrastructure.box.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 
 * @TableName CRM_CUSTOMER_MAIN
 */
@TableName(value ="CRM_CUSTOMER_MAIN")
@Data
public class CrmCustomerMain implements Serializable {
    /**
     * 主键
     */
    @TableField("ID")
    private String id;

    /**
     * CRMID 经销客户id 创建的年份均为当年
     */
    @TableField("CRM_ID")
    private String crmId;

    /**
     * 客户名称
     */
    @TableField("CUSTOMER_NAME")
    private String customerName;



    @ApiModelProperty(value = "unionid")
    @TableField("UNIONID")
    private String unionid;

    /**
     * 是否注册公司  1  是  0 否
     */
    @TableField("COMPANY_FLAG")
    private String companyFlag;

    /**
     * 公司名称
     */
    @TableField("COMPANY_NAME")
    private String companyName;

    /**
     * 省名称
     */
    @TableField("COMPANY_PROVINCE")
    private String companyProvince;

    /**
     * 省id
     */
    @TableField("COMPANY_PROVINCE_ID")
    private String companyProvinceId;

    /**
     * 市名称
     */
    @TableField("COMPANY_CITY")
    private String companyCity;

    /**
     * 市id
     */
    @TableField("COMPANY_CITY_ID")
    private String companyCityId;

    /**
     * 区名称
     */
    @TableField("COMPANY_DISTRICT")
    private String companyDistrict;

    /**
     * 区id
     */
    @TableField("COMPANY_DISTRICT_ID")
    private String companyDistrictId;

    /**
     * 详细地址
     */
    @TableField("COMPANY_ADDRESS")
    private String companyAddress;

    /**
     * 公司法人名称
     */
    @TableField("COMPANY_LEGAL_PERSON")
    private String companyLegalPerson;

    /**
     * 公司联系电话
     */
    @TableField("COMPANY_PHONE")
    private String companyPhone;

    /**
     * 公司邮箱地址
     */
    @TableField("COMPANY_EMAIL")
    private String companyEmail;

    /**
     * 联系人名称
     */
    @TableField("CONNECT_NAME")
    private String connectName;

    /**
     * 联系人电话
     */
    @TableField("CONNECT_PHONE")
    private String connectPhone;

    /**
     * 联系人邮箱
     */
    @TableField("CONNECT_EMAIL")
    private String connectEmail;

    /**
     * ERP系统
     */
    @TableField("ERP_SYSTEM")
    private String erpSystem;

    /**
     * 创建人
     */
    @TableField("CREATE_BY")
    private String createBy;

    /**
     * 更新人
     */
    @TableField("UPDATE_BY")
    private String updateBy;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("UPDATE_TIME")
    private Date updateTime;

    /**
     * 0 未删除  1 已删除
     */
    @TableField("IS_DEL")
    private Integer isDel;

    /**
     * 状态    1 考察期  2 准入  3 合作  4 解约  5 淘汰
     */
    @TableField("STATUS")
    private Integer status;

    /**
     * 考察人
     */
    @TableField("INVESTIGATE_PERSON")
    private String investigatePerson;

    /**
     * 考察人id
     */
    @TableField("INVESTIGATE_PERSON_ID")
    private String investigatePersonId;

    /**
     * 总分数
     */
    @TableField("TOTAL_SCORE")
    private String totalScore;

    /**
     * 店铺评分
     */
    @TableField("STORE_SCORE")
    private String storeScore;

    /**
     * 面试评分
     */
    @TableField("INTERVIEW_SCORE")
    private String interviewScore;

    /**
     * 独立性调查报告url
     */
    @TableField("INDEPENDENCE_INVESTIGATION_URL")
    private String independenceInvestigationUrl;

    /**
     * 征信调查报告url
     */
    @TableField("CREDIT_URL")
    private String creditUrl;

    /**
     * 评分表url
     */
    @TableField("SCORE_URL")
    private String scoreUrl;

    /**
     * 调查表url
     */
    @TableField("INVESTIGATION_URL")
    private String investigationUrl;

    /**
     * 解约原因
     */
    @TableField("TERMINATE_REASON")
    private String terminateReason;

    /**
     * 首次合作时间
     */
    @TableField("FIRST_FRANCHISE_TIME")
    private String firstFranchiseTime;

    /**
     * 首次合作品牌   c_ARC_BRANDID
     */
    @TableField("FIRST_FRANCHISE_BRAND")
    private String firstFranchiseBrand;

    /**
     * 首次合作区域经理
     */
    @TableField("FIRST_AREA_MANAGER")
    private String firstAreaManager;

    /**
     * 首次合作销售区域
     */
    @TableField("SELL_AREA")
    private String sellArea;

    @TableField("BOJUN_OVER")
    private Integer bojunOver;

    @TableField("BOJUN_CW")
    private Integer bojunCw;

    @TableField("NC_CW")
    private Integer ncCw;

    @TableField("OA_CUSTOMER_OVER")
    private Integer oaCustomerOver;

    @TableField("INTERVIEW_BRAND_ID")
    private String interviewBrandId;


    @TableField("CUSTOMER_CONTROL_CITYS")
    private String customerControlCitys;

    @TableField("CUSTOMER_OPERATE_BRANDS")
    private String customerOperateBrands;

    @TableField("OPERATE_STORE")
    private String operateStore;

    @TableField("YEAR_SCALE")
    private String yearScale;

    @TableField("CLOTH_OPERATE_YEARS")
    private String clothOperateYears;

    @ApiModelProperty(value = "是否直营转 0 否  1 是")
    @TableField("IS_DIRECT_TRANSFER")
    private String isDirectTransfer;

    @ApiModelProperty(value = "主客户等级")
    @TableField("MAIN_CUSTOMER_LEVEL")
    private String mainCustomerLevel;

    @ApiModelProperty(value = "准入等级")
    @TableField("ACCESS_LEVEL")
    private String accessLevel;

    @ApiModelProperty(value = "江南占比")
    @TableField("JNBY_ACCOUNT_FOR")
    private String jnbyAccountFor;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}