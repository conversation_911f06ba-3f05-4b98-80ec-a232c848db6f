package com.jnby.infrastructure.oa.mapper;


import com.jnby.infrastructure.oa.model.CustomerWithBrand;
import com.jnby.infrastructure.oa.model.Fm853WithMain413;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 准入
 */
public interface ZrMapper {


    /**
     * 准入-组装客户信息
     * @param requestIds
     * @return
     */
    List<CustomerWithBrand> getZrCustomerWithBrand(@Param("requestIds")List<String> requestIds);


    /**
     * 获取品牌与requestId关系
     * @param requestIds
     * @return
     */
    List<Fm853WithMain413> getBrandWithRequestId(@Param("requestIds")List<String> requestIds);
}
