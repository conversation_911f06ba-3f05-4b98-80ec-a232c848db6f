package com.jnby.infrastructure.oa.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class WorkCommonEntity {
    private String id;

    private String requestId;


    private String requestName;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String currentNodeType;


    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private String createDate;

    private String createTime;


    private String workFlowId;


    private String workFlowName;

    /**
     *创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;


    private String createType;




    @ApiModelProperty(value = "创建人id")
    private String createId;


}
