//package com.jnby.infrastructure.oa.mapper;
//
//import com.baomidou.dynamic.datasource.annotation.DS;
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//
//import java.util.List;
//import java.util.Map;
//
///**
// * <p>
// * Mapper 接口 OA数据库
// * </p>
// * 存储过程调用：https://www.codeleading.com/article/52452498932/
// *
// * <AUTHOR>
// * @since 2022-03-22
// */
//public interface FormtableMain53Mapper extends BaseMapper<FormtableMain53Vo> {
//
//    /**
//     * 经销类型 获取OA数据库的批量获取数据
//     *
//     * @param flowDataParam 参数
//     * @return 结果集
//     */
//    List<FlowDataDTO> getOaBatchDataUseFlow(FlowDataParam flowDataParam);
//}
