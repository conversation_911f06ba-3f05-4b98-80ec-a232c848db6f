package com.jnby.infrastructure.oa.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 钉钉消息发送表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("DINGTALK_SEND_MSG")
@ApiModel(value = "DingtalkSendMsgVo对象", description = "钉钉消息发送表")
public class DingtalkSendMsgVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("ID")
    private Long id;

    @ApiModelProperty(value = "钉钉人员表synaccounttodd.JOBNUMBER")
    @TableField("LINK_ID")
    private String linkId;

    @ApiModelProperty(value = "消息标题")
    @TableField("MSG_TITLE")
    private String msgTitle;

    @ApiModelProperty(value = "消息主体")
    @TableField("MSG_CONTENT")
    private String msgContent;

    @ApiModelProperty(value = "是否发送成功，0未成功，1成功")
    @TableField("FLAG")
    private Long flag;

    @ApiModelProperty(value = "期望发送时间")
    @TableField("SEND_DATE")
    private Date sendDate;

    @ApiModelProperty(value = "消息类型")
    @TableField("MSG_TYPE")
    private String msgType;

    @ApiModelProperty(value = "链接地址")
    @TableField("MSG_URL")
    private String msgUrl;

    @ApiModelProperty(value = "消息发送情况")
    @TableField("MESSAGE")
    private String message;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_DATE")
    private Date createDate;

    @ApiModelProperty(value = "更新时间")
    @TableField("UPDATE_DATE")
    private Date updateDate;

    @ApiModelProperty(value = "发送消息时钉钉返回的任务ID")
    @TableField("MSG_TASK_ID")
    private String msgTaskId;


}
