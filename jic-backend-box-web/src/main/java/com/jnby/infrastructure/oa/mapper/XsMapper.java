package com.jnby.infrastructure.oa.mapper;

import com.jnby.dto.oa.FormTableMain261Dto;
import com.jnby.dto.oa.FormTableMain53Dto;
import com.jnby.infrastructure.oa.model.CustomerWithBrand;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface XsMapper {


    /**
     * 查询销售
     * @param requestIds
     * @return
     */
    List<CustomerWithBrand> getXsCustomerWithBrand(@Param("requestIds")List<String> requestIds);



    List<CustomerWithBrand> getXsCustomerWithBrand469And443And790(@Param("requestIds")List<String> requestIds);



    List<CustomerWithBrand> getXsCustomerWithBrand457(@Param("requestIds")List<String> requestIds);



    List<CustomerWithBrand> getXsCustomerWithBrand422And423(@Param("requestIds")List<String> requestIds);



    List<CustomerWithBrand> getXsCustomerWithBrand545(@Param("requestIds")List<String> requestIds);

}
