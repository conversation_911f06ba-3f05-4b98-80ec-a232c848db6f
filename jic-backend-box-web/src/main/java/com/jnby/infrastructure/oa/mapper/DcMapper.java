package com.jnby.infrastructure.oa.mapper;

import com.jnby.dto.oa.FormTableMain261Dto;
import com.jnby.dto.oa.FormTableMain53Dto;
import com.jnby.infrastructure.oa.model.CustomerWithBrand;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DcMapper {
    /**
     * 查询店仓 JXGL-06、（客户）经销商-撤店申请流程  suoshubjjxs --伯俊经销商ID
     */
    List<FormTableMain261Dto> select261ByRequestId(@Param("requestId") String requestId);


    List<FormTableMain53Dto> select53ByRequestId(@Param("requestId") String requestId);


    /**
     * 查询店仓
     * @param requestIds
     * @return
     */
    List<CustomerWithBrand> getDcCustomerWithBrand(@Param("requestIds")List<String> requestIds);

}
