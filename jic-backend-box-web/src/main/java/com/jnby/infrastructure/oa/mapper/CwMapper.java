package com.jnby.infrastructure.oa.mapper;


import com.jnby.infrastructure.oa.model.CustomerWithBrand;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CwMapper {


    /**
     *  查询财务-封装客户信息
     * @param requestIds
     * @return
     */
    List<CustomerWithBrand> getCwCustomerWithBrand(@Param("requestIds")List<String> requestIds);


    List<CustomerWithBrand> getCwCustomerWithBrand25(@Param("requestIds")List<String> requestIds);



    List<CustomerWithBrand> getCwCustomerWithBrand471(@Param("requestIds")List<String> requestIds);



}
