package com.jnby.infrastructure.oa.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jnby.infrastructure.oa.model.DingtalkSendMsgVo;


/**
 * <p>
 * 钉钉消息发送表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
public interface DingtalkSendMsgMapper extends BaseMapper<DingtalkSendMsgVo> {

    /**
     * 获取数据库自增序列
     *
     * @param tableName 序列名称
     * @return 结果集
     */
    Long getIdSequences(String tableName);
}
