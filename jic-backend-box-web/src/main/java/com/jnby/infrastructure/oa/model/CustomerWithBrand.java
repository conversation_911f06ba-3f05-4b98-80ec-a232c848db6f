package com.jnby.infrastructure.oa.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CustomerWithBrand {
    private String id;
    /**
     *
     */
    private String requestId;


    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;


    @ApiModelProperty(value = "客户id")
    private String customerId;

    @ApiModelProperty(value = "品牌架构id")
    private String arcBrandId;

}
