package com.jnby.infrastructure.oa.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class WorkFlowEntity {
    /**
     *
     */
    private String requestId;

    /**
     * 流程标题
     */
    @ApiModelProperty(value = "流程标题")
    private String title;


    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type;


    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;


    @ApiModelProperty(value = "客户id")
    private String customerId;



    /**
     * 品牌架构
     */
    @ApiModelProperty(value = "品牌架构id")
    private String arcBrandId;


    /**
     *创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;


    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private String createDate;


    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String currentNodeType;
}
