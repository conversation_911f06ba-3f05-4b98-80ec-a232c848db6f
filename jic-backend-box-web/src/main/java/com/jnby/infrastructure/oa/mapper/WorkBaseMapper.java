package com.jnby.infrastructure.oa.mapper;

import com.jnby.dto.oa.GetHtListReqDto;
import com.jnby.infrastructure.oa.model.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WorkBaseMapper {

    /**
     * 查询合同
     * @return
     */
    List<WorkFlowEntity> selectHtList(GetHtListReqDto getHtListReqDto);


    /**
     * 查询财务
     * @return
     */
    List<WorkFlowEntity> selectCwList(GetHtListReqDto getHtListReqDto);


    /**
     * 查询解约
     * @param getHtListReqDto
     * @return
     */
    List<WorkFlowEntity> selectJyList(GetHtListReqDto getHtListReqDto);


    /**
     * 解约列表
     * @param requestIds
     * @return
     */
    List<CustomerWithBrand> getJyCustomerWithBrand(@Param("requestIds")List<String> requestIds);


    /**
     * 查询店仓  此次对应关系直接取 bojunjxs--伯俊经销商ID  历史的 裘单芳 会刷
     * @param getHtListReqDto
     * @return
     */
    List<WorkFlowEntity> selectDcList(GetHtListReqDto getHtListReqDto);


    /**
     * 装修管理
     * @param getHtListReqDto
     * @return
     */
    List<WorkFlowEntity> selectZxList(GetHtListReqDto getHtListReqDto);;


    /**
     * 装修-组装客户信息
     * @param requestIds
     * @return
     */
    List<CustomerWithBrand> getZxCustomerWithBrand(@Param("requestIds")List<String> requestIds);


    /**
     * 获取类型集合
     * @param workFlowNameList
     * @return
     */
   List<WorkflowBase> getWorkFlowBaseList(@Param("workFlowNameList")List<String> workFlowNameList);


    /**
     * 获取类型集合
     * @param wfIdentKeyList
     * @return
     */
    List<WorkflowBase> getWorkFlowBaseListByWfIdentKey(@Param("wfIdentKeyList")List<String> wfIdentKeyList);
    /**
     * 根据formId获取类型集合
     * @param formIds
     * @return
     */

    List<WorkflowBase> getWorkFlowBaseListByFormIds(@Param("formIds")List<String> formIds);


    /**
     * 查询准入 流程
     * @param getHtListReqDto
     * @return
     */
   List<WorkFlowEntity> selectZrList(GetHtListReqDto getHtListReqDto);


    /**
     *  查询财务-封装客户信息
     * @param requestIds
     * @return
     */
    List<CustomerWithBrand> getCw1CustomerWithBrand(@Param("requestIds")List<String> requestIds);


    /**
     * 查询销售管理 列表
     * @param getHtListReqDto
     * @return
     */
    List<WorkFlowEntity> selectXsList(GetHtListReqDto getHtListReqDto);




    List<CustomerWithBrand> getXsCustomerWithBrand(@Param("requestIds")List<String> requestIds);


    /**
     * 查询formId
     * @param id
     * @return
     */
    String  selectFromIdByRequestId(@Param("id")String id);


    List<WorkCommonEntity> selectNeedHtList(@Param("id")String id);




    List<WorkCommonEntity> selectNeedZrList(@Param("id")String id);


    /**
     * 查询店仓
     * @param id
     * @return
     */
    List<WorkCommonEntity> selectNeedDcList(@Param("id")String id);

    /**
     * 查询解约
     * @param id
     * @return
     */
    List<WorkCommonEntity> selectNeedJyList(@Param("id")String id);


    /**
     * 查询装修
     * @param id
     * @return
     */
    List<WorkCommonEntity> selectNeedZxList(@Param("id")String id);


    /**
     * 查询销售
     * @param id
     * @return
     */
    List<WorkCommonEntity> selectNeedXsList(@Param("id")String id);




    /**
     * 查询财务
     * @return
     */
    List<WorkCommonEntity> selectNeedCwList(@Param("id")String id);


    /**
     * 根据天数 和formId 查询数据
     * @param formIds
     * @param days
     * @return
     */
    List<WorkBaseWithFormEntity> selectNeedFormList(@Param("formIds")List<String> formIds, @Param("days")List<String> days);
}
