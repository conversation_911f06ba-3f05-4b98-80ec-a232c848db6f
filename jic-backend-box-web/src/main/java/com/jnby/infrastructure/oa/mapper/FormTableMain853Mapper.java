package com.jnby.infrastructure.oa.mapper;

import com.jnby.dto.FlowDataDTO;
import com.jnby.dto.FlowDataParam;
import com.jnby.dto.oa.FormTableMain853Dto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface FormTableMain853Mapper {
    /**
     * @param requestId
     * @return
     */
    List<FormTableMain853Dto> selectByRequestId(@Param("requestId") String requestId);

    List<FlowDataDTO> getOaBatchDataUseFlow(@Param("flowDataParam") FlowDataParam flowDataParam);

    List<FlowDataDTO> selectByBjjxsid(@Param("bjjxsid") Long bjjxsid);


    String selectNameByProviceId(@Param("id") Long id);

    String selectNameByCityId(@Param("id") Long id);

    String selectNameByReigonID(@Param("id") Long id);
}
