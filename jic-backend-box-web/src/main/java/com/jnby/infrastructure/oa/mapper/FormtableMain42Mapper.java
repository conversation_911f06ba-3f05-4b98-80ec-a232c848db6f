package com.jnby.infrastructure.oa.mapper;

import com.jnby.dto.oa.FormattableMain42Dto;
import com.jnby.infrastructure.oa.model.HtCustomerWithBrand;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FormtableMain42Mapper {

    List<FormattableMain42Dto> selectByCustomerIds(@Param("customerIds") List<String> customerIds);


    List<FormattableMain42Dto> selectByConsSalesCustomerIds(@Param("customerIds") List<String> customerIds);


    List<FormattableMain42Dto> selectByRequestId(@Param("requestId") String requestId);



    /**
     * 获取合同-组装客户信息
     * @param requestIds
     * @return
     */
    List<HtCustomerWithBrand>  getHtCustomerWithBrand(@Param("requestIds")List<String> requestIds);


    String selectUfCustomerNameById(@Param("id") String id);

}
