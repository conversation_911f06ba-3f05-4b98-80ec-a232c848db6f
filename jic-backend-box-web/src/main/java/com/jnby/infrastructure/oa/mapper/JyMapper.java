package com.jnby.infrastructure.oa.mapper;

import com.jnby.dto.oa.FormTableMain48Dto;
import com.jnby.dto.oa.FormTableMain88Dto;
import com.jnby.infrastructure.oa.model.CustomerWithBrand;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface JyMapper {
    /**
     * formtable_main_48
     * @return
     */
    List<FormTableMain48Dto> select48ByRequestId(@Param("requestId") String requestId);


    /**
     * XXZX-06、加密狗申请、退回流程
     * @param requestId
     * @return
     */
    List<FormTableMain88Dto>  select88ByRequestId(@Param("requestId") String requestId);

    List<CustomerWithBrand> getJyCustomerWithBrand(@Param("requestIds")List<String> requestIds);

}
