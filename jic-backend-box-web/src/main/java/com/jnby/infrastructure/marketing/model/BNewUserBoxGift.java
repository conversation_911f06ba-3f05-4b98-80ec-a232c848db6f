package com.jnby.infrastructure.marketing.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("b_new_user_box_gift")
public class BNewUserBoxGift implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableField("ID")
    private String id;
    @ApiModelProperty(value = "用户unionid")
    @TableField("UNIONID")
    private String unionid;
    @ApiModelProperty(value = "权益状态：1正常，0禁用，2暂停,3已过期，4已用完")
    @TableField("STATUS")
    private Integer status;
    @ApiModelProperty(value = "第几个盒子发送快递：1、3、6")
    @TableField("SEND_NUM")
    private Integer sendNum;
    @ApiModelProperty(value = "商品sku")
    @TableField("SKU")
    private String sku;
    @ApiModelProperty(value = "礼物名称")
    @TableField("BOX_GIFT_NAME")
    private String boxGiftName;
    @ApiModelProperty(value = "创建时间")
    @TableField("CREATE_TIME")
    private Date createTime;
    @ApiModelProperty(value = "更新时间")
    @TableField("UPDATE_TIME")
    private Date updateTime;
    @ApiModelProperty(value = "是否删除：0否，1是")
    @TableField("IS_DEL")
    private Long isDel;


}