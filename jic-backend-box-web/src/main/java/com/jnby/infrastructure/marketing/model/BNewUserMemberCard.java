package com.jnby.infrastructure.marketing.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
@Data
@TableName("b_new_user_member_card")
public class BNewUserMemberCard {
    @TableField("ID")
    private String id;
    @ApiModelProperty(value = "用户unionid")
    @TableField("UNIONID")
    private String unionid;
    @ApiModelProperty(value = "outNo")
    @TableField("out_no")
    private String outNo;
    @ApiModelProperty(value = "订阅计划表ID")
    @TableField("subscribe_id")
    private String subscribeId;
}
