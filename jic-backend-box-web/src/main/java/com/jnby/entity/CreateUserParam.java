package com.jnby.entity;

import com.jnby.dto.FlowDataDTO;
import com.jnby.infrastructure.bojun.model.BrandRuleContrast;
import com.jnby.infrastructure.bojun.model.CCustomerVo;
import com.jnby.infrastructure.bojun.model.CStoreVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateUserParam {
    /**
     * 流程数据
     */
    private FlowDataDTO flowDataDTO;
    /**
     * 经销商
     */
    private CCustomerVo cCustomerVo;
    /**
     * 店仓
     */
    private CStoreVo cStoreVo;
    /**
     * 1 经销 2 直营
     */
    private Integer type;

    /**
     * 参考数据规则
     */
    private BrandRuleContrast ruleContrastVo;

    /**
     * 总仓账号：赋值为空  1
     * 道具账号：赋值为空  2
     * 分仓账号，赋值为：分销管理,数据查询,在线店务,内淘宝,零售 管理   3
     */
    private Integer status;
}
