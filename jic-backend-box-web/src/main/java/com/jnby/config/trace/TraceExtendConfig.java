package com.jnby.config.trace;
import brave.Tracer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

@Configuration
public class TraceExtendConfig {

    @Resource
    private Tracer tracer;

    @Bean
    XxlJobTraceAspect createXxlJobTraceAspect() {
        return new XxlJobTraceAspect(tracer);
    }


}
