package com.jnby.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Component
@RefreshScope
@Data
public class CommonProperties {
    /**
     * BOX小程序appId
     * 测试：wx2b785e2a311f5e84
     * 正式：wx1ae075edfc487414
     */
    @Value(value = "${jic.cdp.box_app_id}")
    private String boxAppId;
    /**
     * BOX公众号appId
     * 测试：wx155358306fea248f
     * 正式：wxe45fe9816be8bb42
     */
    @Value(value = "${jic.cdp.mp_app_id}")
    private String mpAppId;
}
