package com.jnby.config;

import brave.Tracer;
import com.jnby.common.ResponseResult;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import javax.annotation.Resource;

@ControllerAdvice
public class TraceIdResponseBodyAdvice implements ResponseBodyAdvice<ResponseResult> {

    @Resource
    private Tracer tracer;

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        return ResponseResult.class.isAssignableFrom(returnType.getParameterType());
    }

    @Override
    public ResponseResult beforeBodyWrite(ResponseResult responseResult, MethodParameter methodParameter, MediaType mediaType, Class<? extends HttpMessageConverter<?>> aClass, ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse) {
        responseResult.setTraceId(tracer.currentSpan().context().traceIdString());
        return responseResult;
    }

}

