package com.jnby.config;
import com.alibaba.druid.pool.DruidDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;


/**
 * @description :OA数据源
 * <AUTHOR> brian
 * @date : 2021/3/11
 */
@MapperScan(basePackages = "com.jnby.infrastructure.oa.mapper", sqlSessionFactoryRef = "oaSqlSessionFactoryBean")
@Configuration
public class DataSourceOaDbConfig {

    @Autowired
    private DataSourceBaseProperties dataSourceBaseProperties;


    @Bean("oaDataSourceProperties")
    public DataSourceProperties getDataSourceProperties(){
        DataSourceProperties properties = new DataSourceProperties();
        properties.setUrl(dataSourceBaseProperties.getOaJdbc());
        properties.setUsername(dataSourceBaseProperties.getOaUserName());
        properties.setPassword(dataSourceBaseProperties.getOaPassword());
        properties.setType(DruidDataSource.class);
        return properties;
    }

    @Bean(value = "oaDruidDataSource", initMethod = "init", destroyMethod = "close")
    public DruidDataSource druidDataSource(@Qualifier("oaDataSourceProperties") DataSourceProperties dataSourceProperties){
        DruidDataSource druidDataSource = (DruidDataSource) dataSourceProperties.initializeDataSourceBuilder().build();
        druidDataSource.setInitialSize(dataSourceBaseProperties.getInitialSize());
        druidDataSource.setMaxActive(dataSourceBaseProperties.getMaxActive());
        druidDataSource.setMaxWait(dataSourceBaseProperties.getMaxWait());
        druidDataSource.setMinIdle(dataSourceBaseProperties.getMinIdle());
        //druidDataSource.setRemoveAbandoned(true);
        druidDataSource.setRemoveAbandonedTimeoutMillis(dataSourceBaseProperties.getRemoveAbandonedTimeoutMillis());
        //druidDataSource.setLogAbandoned(true);
        //druidDataSource.setTestOnBorrow(true);
        druidDataSource.setTestWhileIdle(true);
        druidDataSource.setTimeBetweenEvictionRunsMillis(dataSourceBaseProperties.getTimeBetweenEvictionRunsMillis());
        return druidDataSource;
    }

    @Bean(name = "oaSqlSessionFactoryBean")
    public SqlSessionFactory sqlSessionFactoryBean(@Qualifier("oaDruidDataSource") DruidDataSource dataSource) throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        // 声明mapper配置文件路径
        sessionFactory.setMapperLocations(resolver.getResources("classpath:/persistence/oa/*.xml"));
        sessionFactory.setConfigLocation(resolver.getResource("classpath:/mybatis-config.xml"));
        return sessionFactory.getObject();
    }

    @Bean(name = "oaDataSourceTransactionManager")
    public DataSourceTransactionManager transactionManager(@Qualifier("oaDruidDataSource") DruidDataSource druidDataSource) {
        DataSourceTransactionManager transactionManager = new DataSourceTransactionManager();
        transactionManager.setDataSource(druidDataSource);
        return transactionManager;
    }

    @Bean(name = "oaTransactionTemplate")
    public TransactionTemplate boxTransactionTemplate(@Qualifier("oaDataSourceTransactionManager") DataSourceTransactionManager dataSourceTransactionManager) {
        return new TransactionTemplate(dataSourceTransactionManager);
    }
}
