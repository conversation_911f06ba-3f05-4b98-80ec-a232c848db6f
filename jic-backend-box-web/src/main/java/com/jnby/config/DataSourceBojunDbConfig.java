package com.jnby.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties;
import com.baomidou.mybatisplus.autoconfigure.SpringBootVFS;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.MybatisXMLLanguageDriver;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 3/5/21 2:49 PM
 */
@MapperScan(basePackages = "com.jnby.infrastructure.bojun.mapper", sqlSessionFactoryRef = "bojunSqlSessionFactoryBean")
@Configuration
public class DataSourceBojunDbConfig {

    @Autowired
    private DataSourceBaseProperties dataSourceBaseProperties;

    @Autowired
    private MybatisPlusProperties properties;


    @Autowired(required = false)
    private Interceptor[] interceptors;

    @Autowired(required = false)
    private DatabaseIdProvider databaseIdProvider;

    @Autowired
    ResourceLoader resourceLoader;

    @Bean("bojunDataSourceProperties")
    public DataSourceProperties getDataSourceProperties(){
        DataSourceProperties properties = new DataSourceProperties();
        properties.setUrl(dataSourceBaseProperties.getBojunJdbc());
        properties.setUsername(dataSourceBaseProperties.getBojunUserName());
        properties.setPassword(dataSourceBaseProperties.getBojunPassword());
        properties.setType(DruidDataSource.class);
        return properties;
    }

    @Bean(value = "bojunDruidDataSource", initMethod = "init", destroyMethod = "close")
    public DruidDataSource druidDataSource(@Qualifier("bojunDataSourceProperties") DataSourceProperties dataSourceProperties){
        DruidDataSource druidDataSource = (DruidDataSource) dataSourceProperties.initializeDataSourceBuilder().build();
        druidDataSource.setInitialSize(dataSourceBaseProperties.getInitialSize());
        druidDataSource.setMaxActive(dataSourceBaseProperties.getMaxActive());
        druidDataSource.setMaxWait(dataSourceBaseProperties.getMaxWait());
        druidDataSource.setMinIdle(dataSourceBaseProperties.getMinIdle());
        // 每次获得连接时都会记录当前堆栈信息，造成不必要的空间消耗，并且该操作具有较长的时间消耗，线上去掉
        //druidDataSource.setRemoveAbandoned(true);
        druidDataSource.setRemoveAbandonedTimeoutMillis(dataSourceBaseProperties.getRemoveAbandonedTimeoutMillis());
        //druidDataSource.setLogAbandoned(true);
        // 每次获取连接前校验连接的可用性，需要消耗性能，线上去掉
        // druidDataSource.setTestOnBorrow(true);

        // 不使用PSCache
        druidDataSource.setMaxPoolPreparedStatementPerConnectionSize(0);
        druidDataSource.setPoolPreparedStatements(false);

        // 获取到不可用连接，由应用处理异常
        druidDataSource.setTestWhileIdle(true);
        druidDataSource.setTimeBetweenEvictionRunsMillis(dataSourceBaseProperties.getTimeBetweenEvictionRunsMillis());
        return druidDataSource;
    }
    @Bean(name = "bojunSqlSessionFactoryBean")
    public SqlSessionFactory sqlSessionFactoryBean(@Qualifier("bojunDruidDataSource") DruidDataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean mybatisPlus = new MybatisSqlSessionFactoryBean();
        mybatisPlus.setDataSource(dataSource);
        mybatisPlus.setVfs(SpringBootVFS.class);

        String configLocation = this.properties.getConfigLocation();
        if (StringUtils.isNotBlank(configLocation)) {
            mybatisPlus.setConfigLocation(this.resourceLoader.getResource(configLocation));
        }
        mybatisPlus.setConfiguration(properties.getConfiguration());
        mybatisPlus.setPlugins(this.interceptors);
        MybatisConfiguration mc = new MybatisConfiguration();
        mc.setDefaultScriptingLanguage(MybatisXMLLanguageDriver.class);
        mc.setMapUnderscoreToCamelCase(false);// 数据库和java都是驼峰，就不需要
        mybatisPlus.setConfiguration(mc);
        if (this.databaseIdProvider != null) {
            mybatisPlus.setDatabaseIdProvider(this.databaseIdProvider);
        }
        mybatisPlus.setTypeAliasesPackage(this.properties.getTypeAliasesPackage());
        mybatisPlus.setTypeHandlersPackage(this.properties.getTypeHandlersPackage());
        mybatisPlus.setMapperLocations(this.properties.resolveMapperLocations());
        // 设置mapper.xml文件的路径
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource[] resource = resolver.getResources("classpath:/persistence/bojun/*.xml");
        mybatisPlus.setMapperLocations(resource);
        return mybatisPlus.getObject();
    }

    @Bean(name = "bojunDataSourceTransactionManager")
    public DataSourceTransactionManager transactionManager(@Qualifier("bojunDruidDataSource") DruidDataSource druidDataSource) {
        DataSourceTransactionManager transactionManager = new DataSourceTransactionManager();
        transactionManager.setDataSource(druidDataSource);
        return transactionManager;
    }

    @Bean(name = "bojunTransactionTemplate")
    public TransactionTemplate bojunTransactionTemplate(@Qualifier("bojunDataSourceTransactionManager") DataSourceTransactionManager dataSourceTransactionManager) {
        return new TransactionTemplate(dataSourceTransactionManager);
    }
}
