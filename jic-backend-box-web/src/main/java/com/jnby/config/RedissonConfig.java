package com.jnby.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @Date:2023/2/20 9:41
 */
@Configuration
public class RedissonConfig {

    @Value("${redis.host}")
    private String host;

    @Value("${redis.port}")
    private String port;

    @Value("${redis.password:#{null}}")
    private String password;


    @Value("${redis.database}")
    private  String database;

    @Bean
    public RedissonClient getRedisson(){
        Config config = new Config();
        config.useSingleServer().setAddress("redis://" + host + ":" + port).setDatabase(Integer.valueOf(database));
        if (!StringUtils.isEmpty(password)){
            config.useSingleServer().setPassword(password);
        }
        return Redisson.create(config);
    }

}
