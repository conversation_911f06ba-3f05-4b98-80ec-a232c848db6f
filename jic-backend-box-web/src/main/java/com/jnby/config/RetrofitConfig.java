package com.jnby.config;

import brave.http.HttpTracing;
import brave.okhttp3.TracingInterceptor;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.jnby.config.typeAdapter.DoubleDefaultAdapter;
import com.jnby.config.typeAdapter.IntegerDefaultAdapter;
import com.jnby.config.typeAdapter.LongDefaultAdapter;
import com.jnby.module.remote.ICdpRemoteHttpApi;
import com.jnby.module.remote.IJICHttpApi;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Configuration
public class RetrofitConfig {
    /**
     * cdp服务
     */
    @Value("${jic.cdp.url}")
    private String cdpUrl;

    @Value("${jic.express.url}")
    private String jicExpressUrl;

    @Bean
    public ICdpRemoteHttpApi createICdpRemoteHttpApi(){
        return getApi(cdpUrl, ICdpRemoteHttpApi.class);
    }

    /**
     * 增加请求返回""和"null"的处理
     * 1.Integer=>null
     * 2.Double=>null
     * 3.Long=>null
     */
    private static Gson gson = new GsonBuilder()
            .registerTypeAdapter(Integer.class, new IntegerDefaultAdapter())
            .registerTypeAdapter(Double.class, new DoubleDefaultAdapter())
            .registerTypeAdapter(Long.class, new LongDefaultAdapter()).create();


    /**
     * 定义Retrofit Map集合
     */
    private static Map<String, Retrofit> retrofitMap = new ConcurrentHashMap<>();

    /**
     * 注入托管 tracing
     */
    @Resource
    private HttpTracing httpTracing;

    /**
     * 获取Bean
     *
     * @param url
     * @return
     */
    public Retrofit getRetrofitBean(String url) {
        if (retrofitMap.containsKey(url)) {
            return retrofitMap.get(url);
        }

        //声明日志类
        HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor();
        //设定日志级别
        httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);


        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(url)
                .client(
                        new OkHttpClient.Builder()
                                // 设置完整调用的默认超时。
                                .callTimeout(3, TimeUnit.SECONDS)
//                                .connectTimeout(10, TimeUnit.SECONDS)
//                                .readTimeout(10, TimeUnit.SECONDS)
//                                .writeTimeout(10, TimeUnit.SECONDS)
                                // 不重试
                                .retryOnConnectionFailure(false)
                                // 每个url默认50个连接 3分钟过期
                                .connectionPool(new ConnectionPool(100, 3, TimeUnit.MINUTES))
                                .addInterceptor(TracingInterceptor.create(httpTracing))
                                .addInterceptor(httpLoggingInterceptor)
                                .build())
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build();
        retrofitMap.put(url, retrofit);
        return retrofit;
    }



    public Retrofit getNoCacheRetrofitBean(String url, Integer connectCount, Integer callTimeOut) {
        //声明日志类
        HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor();
        //设定日志级别
        httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(url)
                .client(
                        new OkHttpClient.Builder()
                                // 设置完整调用的默认超时。
                                .callTimeout(callTimeOut, TimeUnit.SECONDS)
                                // 不重试
                                .retryOnConnectionFailure(false)
                                // 连接池 最大空闲个数和保活时间
                                .connectionPool(new ConnectionPool(connectCount, 3, TimeUnit.MINUTES))
                                .addInterceptor(TracingInterceptor.create(httpTracing))
                                .addInterceptor(httpLoggingInterceptor)
                                .build())
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build();
        return retrofit;
    }



    /**
     * 特殊处理refund
     * @param url
     * @return
     */
    public Retrofit getRetrofitBeanForRefund(String url) {
        //声明日志类
        HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor();
        //设定日志级别
        httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);


        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(url)
                .client(
                        new OkHttpClient.Builder()
                                // 设置完整调用的默认超时。
                                .callTimeout(10, TimeUnit.SECONDS)
//                                .connectTimeout(10, TimeUnit.SECONDS)
//                                .readTimeout(10, TimeUnit.SECONDS)
//                                .writeTimeout(10, TimeUnit.SECONDS)
                                // 不重试
                                .retryOnConnectionFailure(false)
                                .addInterceptor(httpLoggingInterceptor)
                                .build())
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build();
        return retrofit;
    }


    /**
     * 获取API
     *
     * @param url     地址
     * @param service
     * @param <T>
     * @return
     */
    public <T> T getApi(String url, Class<T> service) {
        return getRetrofitBean(url).create(service);
    }

    public <T> T getApiForRefund(String url, Class<T> service) {
        return getRetrofitBeanForRefund(url).create(service);
    }


    @Bean
    public IJICHttpApi createIJICHttpApi() {
        return getApiForRefund(jicExpressUrl, IJICHttpApi.class);
    }

}
