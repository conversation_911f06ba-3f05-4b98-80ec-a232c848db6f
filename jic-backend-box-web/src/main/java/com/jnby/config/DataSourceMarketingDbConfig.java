package com.jnby.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties;
import com.baomidou.mybatisplus.autoconfigure.SpringBootVFS;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.MybatisXMLLanguageDriver;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.mapping.DatabaseIdProvider;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Properties;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 3/5/21 2:49 PM
 */
@MapperScan(basePackages = "com.jnby.infrastructure.marketing.mapper", sqlSessionFactoryRef = "marketingSqlSessionFactoryBean")
@Configuration
public class DataSourceMarketingDbConfig {

    @Autowired
    private DataSourceBaseProperties dataSourceBaseProperties;

    @Autowired
    private MybatisPlusProperties properties;


//    @Autowired(required = false)
//    private Interceptor[] interceptors;

    @Autowired(required = false)
    private DatabaseIdProvider databaseIdProvider;

    @Autowired
    ResourceLoader resourceLoader;




    @Primary
    @Bean("marketingDataSourceProperties")
    public DataSourceProperties getDataSourceProperties(){
        DataSourceProperties properties = new DataSourceProperties();
        properties.setUrl(dataSourceBaseProperties.getMarketJdbc());
        properties.setUsername(dataSourceBaseProperties.getMarketUserName());
        properties.setPassword(dataSourceBaseProperties.getMarketPassword());
        properties.setType(DruidDataSource.class);
        return properties;
    }

    @Primary
    @Bean(value = "marketingDruidDataSource", initMethod = "init", destroyMethod = "close")
    public DruidDataSource druidDataSource(@Qualifier("marketingDataSourceProperties") DataSourceProperties dataSourceProperties){
        DruidDataSource druidDataSource = (DruidDataSource) dataSourceProperties.initializeDataSourceBuilder().build();
        druidDataSource.setInitialSize(dataSourceBaseProperties.getInitialSize());
        druidDataSource.setMaxActive(dataSourceBaseProperties.getMaxActive());
        druidDataSource.setMaxWait(dataSourceBaseProperties.getMaxWait());
        druidDataSource.setMinIdle(dataSourceBaseProperties.getMinIdle());
        druidDataSource.setRemoveAbandoned(true);
        druidDataSource.setRemoveAbandonedTimeoutMillis(dataSourceBaseProperties.getRemoveAbandonedTimeoutMillis());
        druidDataSource.setTestOnBorrow(true);
        druidDataSource.setTimeBetweenEvictionRunsMillis(dataSourceBaseProperties.getTimeBetweenEvictionRunsMillis());
        return druidDataSource;
    }
    @Primary
    @Bean(name = "marketingSqlSessionFactoryBean")
    public SqlSessionFactory sqlSessionFactoryBean(@Qualifier("marketingDruidDataSource") DruidDataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean mybatisPlus = new MybatisSqlSessionFactoryBean();
        mybatisPlus.setDataSource(dataSource);
        mybatisPlus.setVfs(SpringBootVFS.class);

//        String configLocation = this.properties.getConfigLocation();
//        if (StringUtils.isNotBlank(configLocation)) {
//            mybatisPlus.setConfigLocation(this.resourceLoader.getResource(configLocation));
//        }
//        mybatisPlus.setConfigLocation(new ClassPathResource("mybatis-mysql-config.xml"));
        String configLocation = this.properties.getConfigLocation();
        if (StringUtils.isNotBlank(configLocation)) {
            mybatisPlus.setConfigLocation(this.resourceLoader.getResource(configLocation));
        }

        // 分页插件
        PageHelper pageInterceptor = new PageHelper();
        Properties properties = new Properties();
        properties.setProperty("helperDialect", "mysql");
        pageInterceptor.setProperties(properties);  // 由此可进入源码，
        mybatisPlus.setPlugins(pageInterceptor);

        MybatisConfiguration mc = new MybatisConfiguration();
        mc.setDefaultScriptingLanguage(MybatisXMLLanguageDriver.class);
        mc.setMapUnderscoreToCamelCase(true);// 数据库和java都是驼峰，就不需要
        mybatisPlus.setConfiguration(mc);
        if (this.databaseIdProvider != null) {
            mybatisPlus.setDatabaseIdProvider(this.databaseIdProvider);
        }
        mybatisPlus.setTypeAliasesPackage(this.properties.getTypeAliasesPackage());
        mybatisPlus.setTypeHandlersPackage(this.properties.getTypeHandlersPackage());
        mybatisPlus.setMapperLocations(this.properties.resolveMapperLocations());
        // 设置mapper.xml文件的路径
        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        Resource[] resource = resolver.getResources("classpath:/persistence/marketing/*.xml");
        mybatisPlus.setMapperLocations(resource);
        return mybatisPlus.getObject();
    }
    @Primary
    @Bean(name = "marketingDataSourceTransactionManager")
    public DataSourceTransactionManager transactionManager(@Qualifier("marketingDruidDataSource") DruidDataSource druidDataSource) {
        DataSourceTransactionManager transactionManager = new DataSourceTransactionManager();
        transactionManager.setDataSource(druidDataSource);
        return transactionManager;
    }
    @Primary
    @Bean(name = "marketingTransactionTemplate")
    public TransactionTemplate bojunTransactionTemplate(@Qualifier("marketingDataSourceTransactionManager") DataSourceTransactionManager dataSourceTransactionManager) {
        return new TransactionTemplate(dataSourceTransactionManager);
    }
}
