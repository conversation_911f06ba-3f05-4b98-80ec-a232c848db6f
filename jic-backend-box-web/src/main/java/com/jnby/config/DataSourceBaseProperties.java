package com.jnby.config;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 1/22/21 4:57 PM
 */
@Data
@Component
@RefreshScope
public class DataSourceBaseProperties {
    //box datasource
    @Value(value = "${boxJdbc}")
    private String boxJdbc;

    @Value(value = "${boxUserName}")
    private String boxUserName;

    @Value(value = "${boxPassword}")
    private String boxPassword;

    @Value(value = "${bojunJdbc}")
    private String bojunJdbc;

    @Value(value = "${bojunUserName}")
    private String bojunUserName;

    @Value(value = "${bojunPassword}")
    private String bojunPassword;


    @Value(value = "${oaJdbc}")
    private String oaJdbc;

    @Value(value = "${oaUserName}")
    private String oaUserName;

    @Value(value = "${oaPassword}")
    private String oaPassword;

    @Value(value = "${marketingJdbc}")
    private String marketJdbc;

    @Value(value = "${marketingUserName}")
    private String marketUserName;

    @Value(value = "${marketingPassword}")
    private String marketPassword;

    // common config value
    private Integer initialSize = 2;
    private Integer maxActive = 5;
    private long maxWait = 3000L;
    private Integer minIdle = 2;
    private long timeBetweenEvictionRunsMillis = 90000L;
    private long removeAbandonedTimeoutMillis = 12*1000L;
    private boolean removeAbandoned = true;
    private boolean testOnBorrow = true;

}
