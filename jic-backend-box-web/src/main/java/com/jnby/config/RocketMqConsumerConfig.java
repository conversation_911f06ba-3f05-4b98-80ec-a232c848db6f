package com.jnby.config;

import com.jnby.listener.IMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/7/6 3:44 下午
 * @Version 1.0
 */
@Configuration
@Slf4j
public class RocketMqConsumerConfig {

    @Value("${mq.namesrv}")
    private String namesrv;

    @Value("${mq.consumer.group}")
    private String consumerGroup;

    private final Map<String, String> subscribe = new HashMap<>();

    @Autowired
    private List<IMessageListener> messageListeners;

    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public DefaultMQPushConsumer consumer(){
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(consumerGroup);
        consumer.setNamesrvAddr(namesrv);
        Map<String, IMessageListener> subscribeTable = subscribeTable();
        messageListeners.forEach(messageListeners -> {
            try {
                log.info("注册消费者 topic：{}  tags:{}", messageListeners.getTopic(), messageListeners.getTags());
                consumer.subscribe(messageListeners.getTopic(), messageListeners.getTags());
            } catch (MQClientException e) {
                log.error("消息启动订阅异常");
            }
        });
        consumer.setConsumeThreadMax(4);
        consumer.setConsumeThreadMin(4);
        consumer.registerMessageListener(new MessageListenerConcurrently() {
            @Override
            public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> list, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
                Iterator<Map.Entry<String, IMessageListener>> it = subscribeTable.entrySet().iterator();
                for(MessageExt msg: list){
                    try {
                        String topic = msg.getTopic();
                        Message message = new Message(topic, msg.getTags(), msg.getKeys(),msg.getBody());
                        while (it.hasNext()){
                            Map.Entry<String, IMessageListener> entry = it.next();
                            if (topic.equals(entry.getKey())){
                                return entry.getValue().consume(message);
                            }
                        }
                    } catch (Exception e) {
                        //MQ发送失败重试机制，1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
                        return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                    }
                }
                return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
            }
        });
        log.info("顺序消息消费者启动成功，namesrvAddr: {}", namesrv);
        return consumer;
    }

    private Map<String, IMessageListener> subscribeTable(){
        Map<String, IMessageListener> subscribeTable = new HashMap<>();
        messageListeners.forEach(item -> {
            subscribeTable.put(item.getTopic(), item);
            subscribe.put(item.getTopic(), "*");
        });
        return subscribeTable;
    }
}
