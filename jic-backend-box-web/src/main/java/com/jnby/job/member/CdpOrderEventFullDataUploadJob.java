package com.jnby.job.member;

import com.jnby.module.member.IMemberDataUploadService;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 任务：cdp历史事件上报
 *
 * 交易订单表 ORDER_
 */

@Component
@Slf4j
public class CdpOrderEventFullDataUploadJob extends IJobHandler {
    @Autowired
    private IMemberDataUploadService memberDataUploadService;

    @XxlJob("CdpOrderEventFullDataUploadJob")
    @Override
    public void execute() throws Exception {
        log.info("CdpOrderEventFullDataUploadJob 开始");
        memberDataUploadService.cdpOrderEventFullDataUploadJob();
        log.info("CdpOrderEventFullDataUploadJob 完成");
    }
}

