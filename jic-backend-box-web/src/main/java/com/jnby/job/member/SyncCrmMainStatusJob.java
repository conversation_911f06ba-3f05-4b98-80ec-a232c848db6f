package com.jnby.job.member;

import com.jnby.module.crm.CrmService;
import com.xxl.job.core.handler.IJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SyncCrmMainStatusJob  extends IJobHandler {
    @Autowired
    private CrmService crmService;

    @Override
    public void execute() throws Exception {
        log.info("定时更新crm结束状态任务开始");
        crmService.syncStopStatus();
        log.info("定时更新crm结束状态任务结束");
    }
}
