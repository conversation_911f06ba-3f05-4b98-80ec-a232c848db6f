package com.jnby.job.member;

import com.jnby.module.member.IMemberDataUploadService;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 任务：cdp历史事件上报
 *
 * 服务订单表 BOX
 */

@Component
@Slf4j
public class CdpBoxEventFullDataUploadJob extends IJobHandler {
    @Autowired
    private IMemberDataUploadService memberDataUploadService;

    @XxlJob("CdpBoxEventFullDataUploadJob")
    @Override
    public void execute() throws Exception {
        log.info("CdpBoxEventFullDataUploadJob 开始");
        memberDataUploadService.cdpBoxEventFullDataUploadJob();
        log.info("CdpBoxEventFullDataUploadJob 完成");
    }
}

