package com.jnby.job.member;

import com.jnby.config.trace.XxlJobTaskLog;
import com.jnby.module.oa.ICommonRequestBase;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Slf4j
@Component
public class OaPullFlowJob extends IJobHandler {

    @Resource
    private ICommonRequestBase iCommonRequestBase;

    /**
     * oa 拉取流程数据
     * @throws Exception
     */
    @Override
    @XxlJob("OaPullFlowJob")
    public void execute() throws Exception {
        XxlJobTaskLog.traceLog("oa拉取流程数据开始");
        iCommonRequestBase.handle2Days();
        XxlJobTaskLog.traceLog("oa拉取流程数据结束");
    }
}
