package com.jnby.job.member;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jnby.dto.JobParamDto;
import com.jnby.module.member.IMemberDataUploadService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 任务：增量上报会员数据
 *
 * <p>
 * 实现：
 * 分别查询5个表记录【创建时间create_time 或 (更新时间update_time 且 更新时间不为空)】在游标区间的增量数据
 * 用户注册表 CUSTOMER_DETAILS
 * 订阅数据表 B_SUBSCRIBE_INFO
 * 主动要盒数据表 CUSTOMER_ASK_BOX
 * 服务订单表 BOX
 * 交易订单表 ORDER_
 */

@Component
@Slf4j
public class MemberDataIncrementUpload2CDPJob extends IJobHandler {
    @Autowired
    private IMemberDataUploadService memberDataUploadService;

    @XxlJob("MemberDataIncrementUpload2CDPJob")
    @Override
    public void execute() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("增量上报CDP需要的会员数据 MemberDataIncrementUpload2CDPJob 开始,入参：{}", jobParam);
        XxlJobHelper.log("增量上报CDP需要的会员数据 MemberDataIncrementUpload2CDPJob 开始,入参：{}", jobParam);


        List<Pair<Date, Date>> pointDatePairList = Lists.newArrayList();
        Date toDate = new Date();
        Date fromDate = DateUtil.addDays(toDate, -1);
        Pair<Date, Date> pair = new Pair<>(fromDate, toDate);
        pointDatePairList.add(pair);
        JobParamDto jobParamDto = new JobParamDto();
        if (StringUtils.isNotBlank(jobParam)) {
            jobParamDto = JobParamDto.build(jobParam);
            Date paramFromDate = jobParamDto.getFromDate();
            Date paramToDate = jobParamDto.getToDate();
            if (paramFromDate == null || paramToDate == null) {
                XxlJobHelper.handleFail("日期范围指定时 fromDate 和 toDate 都需要有值。");
                return;
            }

            // 按照日期切割
            pointDatePairList.clear();
            while (paramFromDate.before(paramToDate) || paramFromDate.equals(paramToDate)) {
                Date oneDayAfter = DateUtil.addDays(paramFromDate, 1);
                Pair<Date, Date> paramPair = new Pair<>(paramFromDate, oneDayAfter);
                pointDatePairList.add(paramPair);
                paramFromDate = oneDayAfter;
            }

        }

        for (Pair<Date, Date> datePair : pointDatePairList) {
            XxlJobHelper.log("执行前的最终入参,fromDate[{}],toDate[{}]", datePair.getKey(), datePair.getValue());
            memberDataUploadService.incrementalUpload(datePair.getKey(), datePair.getValue(), jobParamDto);
        }

        XxlJobHelper.log("增量上报CDP需要的会员数据 MemberDataIncrementUpload2CDPJob 完成，执行时间范围[{}]-[{}]",
                DateUtil.formatDateTime(fromDate), DateUtil.formatDateTime(toDate));
        log.info("增量上报CDP需要的会员数据 MemberDataIncrementUpload2CDPJob 完成");
    }
}

