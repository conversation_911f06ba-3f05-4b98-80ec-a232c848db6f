package com.jnby.dto;

import com.jnby.infrastructure.bojun.model.CCustomerVo;
import com.jnby.infrastructure.bojun.model.NcCustomer;
import com.jnby.infrastructure.bojun.model.NcCustomerCompare;
import com.jnby.infrastructure.bojun.model.NcCustomerCompareZg;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CustomerFinanceDingMsgParam {

    private FlowDataDTO flowDataDTO;

    // 货品
    private CCustomerVo docCCustomer;
    private NcCustomer docNcCustomer;
    private NcCustomerCompare docNcCustomerCompare;
    private NcCustomerCompareZg docNcCustomerCompareZg;

    // 道具
    private CCustomerVo propCCustomer;
    private NcCustomerCompare propNcCustomerCompare;
    private NcCustomerCompareZg propNcCustomerCompareZg;

}
