package com.jnby.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class UpdateBoxStatusReq implements Serializable {
    @ApiModelProperty(value = "box服务单序号。2选1必填")
    private String boxSn;

    @ApiModelProperty(value = "box服务单修改之前的状态")
    private Long beforeStatus;

    @ApiModelProperty(value = "box服务单修改之后的状态")
    private Long afterStatus;
}
