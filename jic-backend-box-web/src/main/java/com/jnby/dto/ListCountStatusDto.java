package com.jnby.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ListCountStatusDto {

    @ApiModelProperty(value = " 意向 个数")
    private Integer interviewStatusNum = 0;
    @ApiModelProperty(value = " 考察 个数")
    private Integer investigateStatusNum = 0;
    @ApiModelProperty(value = " 准入 个数")
    private Integer accessStatusNum = 0;
    @ApiModelProperty(value = " 合作 个数")
    private Integer coopreateStatusNum = 0;
    @ApiModelProperty(value = " 解约 个数")
    private Integer terminateStatusNum = 0 ;
    @ApiModelProperty(value = " 淘汰 个数")
    private Integer outStatusNum = 0;

}
