package com.jnby.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FlowDataParam {
    /**
     * requestId流程唯一编号
     */
    private Long requestId;
    /**
     * requestId流程唯一编号
     */
    private List<Long> requestIdList;

    public FlowDataParam(List<Long> requestIdList) {
        this.requestIdList = requestIdList;
    }

    public FlowDataParam(Long requestId) {
        this.requestId = requestId;
    }
}
