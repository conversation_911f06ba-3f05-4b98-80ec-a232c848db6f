package com.jnby.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
public class CrmCreateDto {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键  仅在编辑时候需要填写  创建无需填写")
    private String id;

    /**
     * CRMID 经销客户id 创建的年份均为当年
     */
    @ApiModelProperty(value = "CRMID 经销客户id 创建的年份均为当年  不填写")
    private String crmId;


    @ApiModelProperty(value = "unionid")
    private String unionid;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /**
     * 是否注册公司  1  是  0 否
     */
    @ApiModelProperty(value = "是否注册公司  1  是  0 否")
    private String companyFlag;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 省名称
     */
    @ApiModelProperty(value = "省名称")
    private String companyProvince;

    /**
     * 省id
     */
    @ApiModelProperty(value = "省id")
    private String companyProvinceId;

    /**
     * 市名称
     */
    @ApiModelProperty(value = "市名称")
    private String companyCity;

    /**
     * 市id
     */
    @ApiModelProperty(value = "市id")
    private String companyCityId;

    /**
     * 区名称
     */
    @ApiModelProperty(value = "区名称")
    private String companyDistrict;

    /**
     * 区id
     */
    @ApiModelProperty(value = "区id")
    private String companyDistrictId;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String companyAddress;

    /**
     * 公司法人名称
     */
    @ApiModelProperty(value = "公司法人名称")
    private String companyLegalPerson;

    /**
     * 公司联系电话
     */
    @ApiModelProperty(value = "公司联系电话")
    private String companyPhone;

    /**
     * 公司邮箱地址
     */
    @ApiModelProperty(value = "公司邮箱地址")
    private String companyEmail;

    /**
     * 联系人名称
     */
    @ApiModelProperty(value = "联系人名称")
    private String connectName;

    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话")
    private String connectPhone;

    /**
     * 联系人邮箱
     */
    @ApiModelProperty(value = "联系人邮箱")
    private String connectEmail;

    /**
     * ERP系统
     */
    @ApiModelProperty(value = "ERP系统")
    private String erpSystem;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

    /**
     * 0 未删除  1 已删除
     */
    @ApiModelProperty(value = "0 未删除  1 已删除")
    private Integer isDel;

    /**
     * 状态  0 意向  1 考察期  2 准入  3 合作  4 解约  5 淘汰
     */
    @ApiModelProperty(value = "状态  0 意向  1 考察期  2 准入  3 合作  4 解约  5 淘汰")
    private Integer status;

    /**
     * 考察人
     */
    @ApiModelProperty(value = "考察人")
    private String investigatePerson;

    /**
     * 考察人id
     */
    @ApiModelProperty(value = "考察人id")
    private String investigatePersonId;

    /**
     * 总分数
     */
    @ApiModelProperty(value = "总分数")
    private String totalScore;

    /**
     * 店铺评分
     */
    @ApiModelProperty(value = "店铺评分")
    private String storeScore;

    /**
     * 面试评分
     */
    @ApiModelProperty(value = "面试评分")
    private String interviewScore;

    /**
     * 独立性调查报告url
     */
    @ApiModelProperty(value = "独立性调查报告url")
    private String independenceInvestigationUrl;

    /**
     * 征信调查报告url
     */
    @ApiModelProperty(value = "征信调查报告url")
    private String creditUrl;

    /**
     * 评分表url
     */
    @ApiModelProperty(value = "评分表url")
    private String scoreUrl;

    /**
     * 调查表url
     */
    @ApiModelProperty(value = "调查表url")
    private String investigationUrl;

    /**
     * 解约原因
     */
    @ApiModelProperty(value = "解约原因")
    private String terminateReason;

    /**
     * 首次合作时间
     */
    @ApiModelProperty(value = "首次合作时间")
    private String firstFranchiseTime;

    /**
     * 首次合作品牌   c_ARC_BRANDID
     */
    @ApiModelProperty(value = "首次合作品牌   c_ARC_BRANDID")
    private String firstFranchiseBrand;

    /**
     * 首次合作区域经理
     */
    @ApiModelProperty(value = "首次合作区域经理")
    private String firstAreaManager;

    @ApiModelProperty(value = "首次合作区域经理")
    private String firstAreaManagerName;

    /**
     * 首次合作销售区域
     */
    @ApiModelProperty(value = "经销地理区域")
    private String sellArea;

    @ApiModelProperty(value = "伯俊是否建档   0 没有  1 建立了")
    private String bojunOver;

    @ApiModelProperty(value = "伯俊财务是否建档  0 没有 ")
    private String bojunCw;

    @ApiModelProperty(value = "NC财务是否建档 0 没有")
    private String ncCw;

    @ApiModelProperty(value = "OA客商是否建档 0 没有")
    private String oaCustomerOver;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "绑定的下属经销商")
    private List<CrmRelationDto> crmRelationDtoList;

    @ApiModelProperty(value = "当前客户标签")
    private List<CrmCustomerLabelDto> crmCustomerLabelDtos;

    @ApiModelProperty(value = "意向合作品牌id")
    private String interviewBrandId;



    @ApiModelProperty(value = "客户操作的城市")
    private String customerControlCitys;

    @ApiModelProperty(value = "客户经营的品牌")
    private String customerOperateBrands;

    @ApiModelProperty(value = "经销的店铺数")
    private String operateStore;

    @ApiModelProperty(value = "年规模")
    private String yearScale;

    @ApiModelProperty(value = "服装操作经验 年份")
    private String clothOperateYears;

    @ApiModelProperty(value = "是否直营转 0 否  1 是 ")
    private String isDirectTransfer;

    private Set<String> arcBrandIds;

    @ApiModelProperty(value = "当进行准入的时候  oaId必传")
    private String oaId;

    @ApiModelProperty(value = "准入等级")
    private String accessLevel;

    @ApiModelProperty(value = "主客户等级")
    private String mainCustomerLevel;

    @ApiModelProperty(value = "江南占比")
    private String jnbyAccountFor;




}
