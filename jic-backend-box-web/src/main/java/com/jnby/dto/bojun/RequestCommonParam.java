package com.jnby.dto.bojun;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询流程指定经销商数据
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RequestCommonParam {

    /**
     * 当前节点
     */
    private Long currentNodeId;
    /**
     * 上一个节点
     */
    private Long lastNodeId;
    /**
     * sql执行语句
     */
    private String sqlStatement;

    public RequestCommonParam(Long currentNodeId, Long lastNodeId) {
        this.currentNodeId = currentNodeId;
        this.lastNodeId = lastNodeId;
    }

    public RequestCommonParam(String sqlStatement) {
        this.sqlStatement = sqlStatement;
    }
}
