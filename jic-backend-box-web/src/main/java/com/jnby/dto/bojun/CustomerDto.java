package com.jnby.dto.bojun;

import com.jnby.infrastructure.box.model.CrmOrderGoods;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


public class CustomerDto {

    private Long id;

    @ApiModelProperty(value = "是否可用 默认Y")
    private String isactive;

    @ApiModelProperty(value = "经销商名称")
    private String name;

    @ApiModelProperty(value = "加盟日期")
    private Long enterdate;

    @ApiModelProperty(value = "解约时间")
    private String jysj2;

    @ApiModelProperty(value = "解约原因")
    private String jyyy;

    @ApiModelProperty(value = "是否结束  Y 是  N否 ")
    private String isstop;

    @ApiModelProperty(value = "联系人")
    private String contacter;

    @ApiModelProperty(value = "电话")
    private String phone;

    @ApiModelProperty(value = "签约公司名称 或者 个人名称")
    private String address;

    @ApiModelProperty(value = "品牌名称")
    private String post;

    @ApiModelProperty(value = "电子邮箱")
    private String email;

    @ApiModelProperty(value = "大区经理")
    private Long areamngId;

    @ApiModelProperty(value = "大区经理 名称")
    private String areamngName;

    @ApiModelProperty(value = "城市id")
    private Long cCityId;

    @ApiModelProperty(value = "经销商编码")
    private String code;

    @ApiModelProperty(value = "销售区域id")
    private Long cAreaId;

    @ApiModelProperty(value = "品牌加购id")
    private Long cArcbrandId;

    @ApiModelProperty(value = "拥有的可用的店铺情况")
    private Long haveStoreNum;

    @ApiModelProperty(value = " 0 是  1 否   是否转直营" )
    private Long sfzzy;

    @ApiModelProperty(value = "订货信息")
    private List<CrmOrderGoods> crmOrderGoodsList;


    @ApiModelProperty(value = "品牌客户评级")
    private String customerLevel;

    @ApiModelProperty(value = "客户评价")
    private String customerEvaluate;

    @ApiModelProperty(value = "编辑品牌客户评级使用此字段当做id")
    private String crmRelationId;


    @ApiModelProperty(value = "是否注册公司   0 是  1 否")
    private String isRegCompany;
    @ApiModelProperty(value = "详细地址")
    private String addressInfo;
    @ApiModelProperty(value = "省")
    private String province;
    @ApiModelProperty(value = "市")
    private String city;
    @ApiModelProperty(value = "区")
    private String region;
    @ApiModelProperty(value = "法人名称")
    private String legalPersonName;
    @ApiModelProperty(value = "法人联系电话")
    private String legalPersonPhone;
    @ApiModelProperty(value = "法人电子邮箱")
    private String legalPersonMail;

    @ApiModelProperty(value = "联系人名称")
    private String connectName;
    @ApiModelProperty(value = "联系人联系电话")
    private String connectPhone;

    @ApiModelProperty(value = "经销地理区域")
    private String clopStore;




    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "公司地址")
    private String companyAddress;


    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyAddress() {
        return companyAddress;
    }

    public void setCompanyAddress(String companyAddress) {
        this.companyAddress = companyAddress;
    }

    public String getClopStore() {
        return clopStore;
    }

    public void setClopStore(String clopStore) {
        this.clopStore = clopStore;
    }

    public String getConnectName() {
        return connectName;
    }

    public void setConnectName(String connectName) {
        this.connectName = connectName;
    }

    public String getConnectPhone() {
        return connectPhone;
    }

    public void setConnectPhone(String connectPhone) {
        this.connectPhone = connectPhone;
    }

    public String getJyyy() {
        return jyyy;
    }

    public void setJyyy(String jyyy) {
        this.jyyy = jyyy;
    }

    public String getIsRegCompany() {
        return isRegCompany;
    }

    public void setIsRegCompany(String isRegCompany) {
        this.isRegCompany = isRegCompany;
    }

    public String getAddressInfo() {
        return addressInfo;
    }

    public void setAddressInfo(String addressInfo) {
        this.addressInfo = addressInfo;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getLegalPersonName() {
        return legalPersonName;
    }

    public void setLegalPersonName(String legalPersonName) {
        this.legalPersonName = legalPersonName;
    }

    public String getLegalPersonPhone() {
        return legalPersonPhone;
    }

    public void setLegalPersonPhone(String legalPersonPhone) {
        this.legalPersonPhone = legalPersonPhone;
    }

    public String getLegalPersonMail() {
        return legalPersonMail;
    }

    public void setLegalPersonMail(String legalPersonMail) {
        this.legalPersonMail = legalPersonMail;
    }

    public String getCustomerEvaluate() {
        return customerEvaluate;
    }

    public void setCustomerEvaluate(String customerEvaluate) {
        this.customerEvaluate = customerEvaluate;
    }

    public String getCrmRelationId() {
        return crmRelationId;
    }

    public void setCrmRelationId(String crmRelationId) {
        this.crmRelationId = crmRelationId;
    }

    public String getCustomerLevel() {
        return customerLevel;
    }

    public void setCustomerLevel(String customerLevel) {
        this.customerLevel = customerLevel;
    }

    public List<CrmOrderGoods> getCrmOrderGoodsList() {
        return crmOrderGoodsList;
    }

    public void setCrmOrderGoodsList(List<CrmOrderGoods> crmOrderGoodsList) {
        this.crmOrderGoodsList = crmOrderGoodsList;
    }

    public Long getSfzzy() {
        return sfzzy;
    }

    public void setSfzzy(Long sfzzy) {
        this.sfzzy = sfzzy;
    }

    public String getAreamngName() {
        return areamngName;
    }

    public void setAreamngName(String areamngName) {
        this.areamngName = areamngName;
    }

    public Long getHaveStoreNum() {
        return haveStoreNum;
    }

    public void setHaveStoreNum(Long haveStoreNum) {
        this.haveStoreNum = haveStoreNum;
    }

    public String getJysj2() {
        return jysj2;
    }

    public void setJysj2(String jysj2) {
        this.jysj2 = jysj2;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getIsactive() {
        return isactive;
    }

    public void setIsactive(String isactive) {
        this.isactive = isactive;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getEnterdate() {
        return enterdate;
    }

    public void setEnterdate(Long enterdate) {
        this.enterdate = enterdate;
    }

    public String getIsstop() {
        return isstop;
    }

    public void setIsstop(String isstop) {
        this.isstop = isstop;
    }

    public String getContacter() {
        return contacter;
    }

    public void setContacter(String contacter) {
        this.contacter = contacter;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPost() {
        return post;
    }

    public void setPost(String post) {
        this.post = post;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Long getAreamngId() {
        return areamngId;
    }

    public void setAreamngId(Long areamngId) {
        this.areamngId = areamngId;
    }

    public Long getcCityId() {
        return cCityId;
    }

    public void setcCityId(Long cCityId) {
        this.cCityId = cCityId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getcAreaId() {
        return cAreaId;
    }

    public void setcAreaId(Long cAreaId) {
        this.cAreaId = cAreaId;
    }

    public Long getcArcbrandId() {
        return cArcbrandId;
    }

    public void setcArcbrandId(Long cArcbrandId) {
        this.cArcbrandId = cArcbrandId;
    }
}
