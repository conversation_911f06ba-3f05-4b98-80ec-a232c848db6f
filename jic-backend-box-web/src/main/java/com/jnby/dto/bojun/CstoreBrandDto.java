package com.jnby.dto.bojun;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CstoreBrandDto {
    @ApiModelProperty(value = "门店ID")
    private String storeId;
    @ApiModelProperty(value = "门店名称")
    private String storeName;
    @ApiModelProperty(value = "集合店ID")
    private String unionStoreId;
    @ApiModelProperty(value = "品牌名称")
    private String brandName;
    @ApiModelProperty(value = "奥莱标记。是=奥莱，其他不是")
    private String outlets;

}
