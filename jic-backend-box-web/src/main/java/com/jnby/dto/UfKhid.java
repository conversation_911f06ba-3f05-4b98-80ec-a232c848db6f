package com.jnby.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@TableName("ECOLOGY.UF_KHID")
@ApiModel(value = "ufKhid对象", description = "ufKhid对象")
public class UfKhid {

    @TableField("ID")
    private Long id;

    @TableField("KHID")
    private String khid;
    @TableField("NAME")
    private String name;


}
