package com.jnby.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class GenerateShortUrlReq {

    @ApiModelProperty(value = "通过 URL Link 进入的小程序页面路径，必须是已经发布的小程序存在的页面，不可携带 query 。path 为空时会跳转小程序主页")
    private String path;

    @ApiModelProperty(value = "通过 URL Link 进入小程序时的query，最大1024个字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~%")
    private String query;

    @ApiModelProperty(value = "默认值0.小程序 URL Link 失效类型，失效时间：0，失效间隔天数：1")
    private Long expire_type = 1L;

    @ApiModelProperty(value = "到期失效的 URL Link 的失效时间，为 Unix 时间戳。生成的到期失效 URL Link 在该时间前有效。最长有效期为30天。expire_type 为 0 必填")
    private Long expire_time;

    @ApiModelProperty(value = "到期失效的URL Link的失效间隔天数。生成的到期失效URL Link在该间隔时间到达前有效。最长间隔天数为30天。expire_type 为 1 必填")
    private Long expire_interval =30L;

    @ApiModelProperty(value = "云开发静态网站自定义 H5 配置参数，可配置中转的云开发 H5 页面。不填默认用官方 H5 页面")
    private CloudBase cloud_base;

    @Data
    public static class CloudBase{

        private String env;

        private String domain;

        private String path;

        private String query;

        private String resource_appid;

    }

}
