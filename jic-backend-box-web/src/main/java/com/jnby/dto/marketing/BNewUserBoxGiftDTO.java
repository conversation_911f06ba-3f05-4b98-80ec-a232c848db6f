package com.jnby.dto.marketing;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class BNewUserBoxGiftDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String id;
    @ApiModelProperty(value = "用户unionid")
    private String unionid;
    @ApiModelProperty(value = "权益状态：1正常，0禁用，2暂停,3已过期，4已用完")
    private Integer status;
    @ApiModelProperty(value = "第几个盒子发送快递：1、3、6")
    private Integer sendNum;
    @ApiModelProperty(value = "商品sku")
    private String sku;
    @ApiModelProperty(value = "礼物名称")
    private String boxGiftName;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    @ApiModelProperty(value = "是否删除：0否，1是")
    private Long isDel;
    @ApiModelProperty(value = "订阅id")
    private String subId;
}