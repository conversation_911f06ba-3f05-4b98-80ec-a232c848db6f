package com.jnby.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

public class CrmRelationDto {

    private  String id;

    @ApiModelProperty(value = "经销商编号")
    private  String customerCode;

    @ApiModelProperty(value = "当前绑定的主客户信息")
    private  String crmCustomerMainId;

    private  Integer isDel;

    private Date createTime;

    private Date updateTime;

    @ApiModelProperty(value = "品牌id")
    private String cArcbrandId;

    private String customerId;


    private String jysj2;

    @ApiModelProperty(value = "客户评级")
    private String customerLevel;
    @ApiModelProperty(value = "客户评价")
    private String customerEvaluate;








    public String getCustomerLevel() {
        return customerLevel;
    }

    public void setCustomerLevel(String customerLevel) {
        this.customerLevel = customerLevel;
    }

    public String getCustomerEvaluate() {
        return customerEvaluate;
    }

    public void setCustomerEvaluate(String customerEvaluate) {
        this.customerEvaluate = customerEvaluate;
    }

    public String getJysj2() {
        return jysj2;
    }

    public void setJysj2(String jysj2) {
        this.jysj2 = jysj2;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    //    @ApiModelProperty(value = "当前客户标签")
//    private List<CrmCustomerLabelDto> crmCustomerLabelDtos;
//
//    public List<CrmCustomerLabelDto> getCrmCustomerLabelDtos() {
//        return crmCustomerLabelDtos;
//    }
//
//    public void setCrmCustomerLabelDtos(List<CrmCustomerLabelDto> crmCustomerLabelDtos) {
//        this.crmCustomerLabelDtos = crmCustomerLabelDtos;
//    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCrmCustomerMainId() {
        return crmCustomerMainId;
    }

    public void setCrmCustomerMainId(String crmCustomerMainId) {
        this.crmCustomerMainId = crmCustomerMainId;
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getcArcbrandId() {
        return cArcbrandId;
    }

    public void setcArcbrandId(String cArcbrandId) {
        this.cArcbrandId = cArcbrandId;
    }
}
