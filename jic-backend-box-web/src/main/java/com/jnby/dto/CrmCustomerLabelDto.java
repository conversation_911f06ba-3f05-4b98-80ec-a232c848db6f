package com.jnby.dto;


import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class CrmCustomerLabelDto {


    private  String id;

    @ApiModelProperty(value = "标签名")
    private  String labelName;

    private  String crmCustomerMainId;

    private  Integer isDel;

    private Date createTime;

    private Date updateTime;

}
