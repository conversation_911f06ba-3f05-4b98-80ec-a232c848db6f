package com.jnby.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class StoreCustomerReq {

    @ApiModelProperty(value = "模糊查询 id 店仓名称  经销商名称")
    private String search;

    @ApiModelProperty(value = "店铺归属区域id")
    private String area;

    @ApiModelProperty(value = "品牌id")
    private List<String> brandIds;

    @ApiModelProperty(value = "状态 Y可用 N不可用")
    private String status;

}
