package com.jnby.dto;

import com.beust.jcommander.internal.Maps;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 全局工具类
 *
 * <AUTHOR>
 */
public class GlobalCommonVariable {

    public static final String STR = "D";
    public static final String YES = "是";
    public static final String A = "A";

    /**
     * 标定判断依据
     */
    public static final Integer NUM = 3;

    /**
     * 创建总仓的类型
     */
    public static final Set<String> ALL_STORE_TYPE_SET = new HashSet<>(2);

    static {
        ALL_STORE_TYPE_SET.add("1");
        ALL_STORE_TYPE_SET.add("3");
    }

    /**
     * 指定自增次数
     */
    public static final Integer INDEX = 5;


    public static final String DEFAULT_PASSWORD = "Jnby1994";

    public static final Map<String, String> OA_BRAND_NAME_BOJUN = Maps.newHashMap();

    static {
        OA_BRAND_NAME_BOJUN.put("速写", "CROQUIS");
    }

}
