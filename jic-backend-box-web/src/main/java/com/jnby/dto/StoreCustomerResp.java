package com.jnby.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class StoreCustomerResp {

    @ApiModelProperty(value = "店仓编号")
    private String storeCode;

    @ApiModelProperty(value = "店仓名称")
    private String storeName;

    @ApiModelProperty(value = "归属经客户 名称")
    private String customerName;

    @ApiModelProperty(value = "归属区域  store的归属区域")
    private String storeArea;

    @ApiModelProperty(value = "品牌id")
    private String brandId;

    @ApiModelProperty(value = "状态  Y 可用  N不可用")
    private String status;

    @ApiModelProperty(value = "是否允许零售  Y 允许 N 不允许")
    private String isretail;

    @ApiModelProperty(value = "  开关店是否统计   Y 是  N 否")
    private String cStoreattrib37Id;

    @ApiModelProperty(value = "甲方体系")
    private String default01;

    @ApiModelProperty(value = "开业时间")
    private Long opendate;

    @ApiModelProperty(value = "仓位性质   非道具仓")
    private String default07;




}
