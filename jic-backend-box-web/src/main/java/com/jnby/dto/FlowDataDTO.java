package com.jnby.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class FlowDataDTO {
    /**
     * 城市对照表城市编码
     */
    private String generatorCode;
    /**
     * 城市对照表城市编码
     */
    private String generatorCodeName;
    /**
     * 主表流程ID
     */
    private Long requestId;
    /**
     * jwd 经纬度
     */
    private String jwd;
    /**
     * 所属经销商	所属经销商 外键关联：C_CUSTOMER
     */
    @TableField("C_CUSTOMER_ID")
    private Long cCustomerId;
    /**
     * 店仓编号 手动生成
     */
    @TableField("CODE")
    private String code;
    /**
     * 店铺名称	店仓名称 文本
     */
    @TableField("NAME")
    private String name;
    /**
     * 名称描述	DESCRIPTION 等同店仓名称
     */
    @TableField("DESCRIPTION")
    private String description;
    /**
     * 原店仓	Y_STORE 等同店仓名称
     */
    @TableField("Y_STORE")
    private String yStore;
    /**
     * 品牌架构	品牌架构 外键关联：C_ARCBRAND
     */
    @TableField("C_ARCBRAND_ID")
    private String cArcbrandId;
    /**
     * 新开店仓销售区域	销售区域 外键关联：C_AREA
     */
    @TableField("C_AREA_ID")
    private String cAreaId;
    /**
     * 销售区域ID是C_AREA表中字段ID	销售区域 外键关联：C_AREA
     */
    @TableField("C_AREA_ID")
    private String newAreaId;
    /**
     * 省市区	省份  外键关联：C_PROVINCE
     * 城市         外键关联：C_CITY
     * 区县        	 外键关联：C_DISTRICT
     */
    @TableField("C_PROVINCE_ID")
    private Long cProvinceId;
    @TableField("C_CITY_ID")
    private Long cCityId;
    @TableField("C_DISTRICT_ID")
    private Long cDistrictId;
    /**
     * 经纬度
     */
    @TableField("LONGITUDE")
    private String longitude;
    @TableField("LATITUDE")
    private String latitude;
    /**
     * 是否启用流水码 下拉框(是、否，10：Y，20：N)，默认选：是
     */
    @TableField("IS_UNIQUE")
    private String isUnique;
    /**
     * 地址
     */
    @TableField("ADDRESS")
    private String address;
    /**
     * 建筑面积  数值
     */
    @TableField("PROPORTION")
    private String proportion;
    /**
     * 实用面积  数值
     */
    @TableField("MONTHFEE")
    private String monthfee;
    /**
     * 店铺性质  外键关联：C_STOREATTRIBVALUE
     * 店铺分类  外键关联：C_STOREATTRIBVALUE
     * 店铺管理模式  外键关联：C_STOREATTRIBVALUE
     * 楼层 C_STOREATTRIB15_ID
     * 电话
     * 预估开业时间
     */
    @TableField("C_STOREATTRIB7_ID")
    private String cStoreattrib7Id;
    @TableField("C_STOREATTRIB8_ID")
    private String cStoreattrib8Id;
    @TableField("C_STOREATTRIB9_ID")
    private String cStoreattrib9Id;
    @TableField("C_STOREATTRIB15_ID")
    private String cStoreattrib15Id;
    @TableField("PHONE")
    private String phone;

    /**
     * 对应伯俊建筑面积/实用面积
     * 建筑面积  PROPORTION
     * 实用面积  MONTHFEE
     */
    private String dpmj;
    /**
     * 预估开业时间
     */
    @TableField("PREOPENDATE")
    private String preopendate;
    /**
     * OA中系统建档类别：
     * 0	仅新建店仓
     * 1	既新建经销商又新建店仓
     */
    @TableField("SYSTEM_CREATE_TYPE")
    private String systemCreateType = "1";

    /**
     * 0 建立道具分仓 1 建立道具总仓 2 不需要建立
     * OA流程中道具仓建档类别
     */
    private String storePropCreateType;

    /**
     * ===========================
     * 建筑面积平方米
     */
    @TableField("JZMJ")
    private String jzmj;
    /**
     * 实际面积平方米
     */
    @TableField("MJLC")
    private String mjlc;
    /**
     * 人员编制人
     */
    @TableField("RYBZ")
    private String rybz;
    /**
     * 租赁合同开始日
     */
    @TableField("ZLHTKSR")
    private String zlhtksr;
    /**
     * 租赁合同到期日
     */
    @TableField("ZLHTDQR")
    private String zlhtdqr;
    /**
     * 租赁合同期限
     */
    @TableField("ZLHTQX")
    private String zlhtqx;
    /**
     * 装修费支出标准金额
     */
    @TableField("ZXFZCJE")
    private String zxfzcje;
    /**
     * 装修完工后开始使用月份
     */
    @TableField("ZXWGHKSSYYF")
    private String zxwghkssyyf;
    /**
     * 提前解约是否有赔偿要求
     */
    @TableField("SELECTNAME")
    private String selectname;
    /**
     * 店铺所属公司
     */
    @TableField("YWSTMC")
    private String ywstmc;
    /**
     * 新开店仓销售区域 传的是C_AREA的ID
     */
    @TableField("XKDCXSQYX")
    private String xkdcxsqyx;
    /**
     * 新开店仓放量区域
     */
    @TableField("XKDCFLQYX")
    private String xkdcflqyx;
    /**
     * 甲方体系
     */
    private String jftx;
    /**
     * 细化城市
     */
    private String xhcs;
    /**
     * 细化区县
     */
    private String xhq;
    /**
     * 商场名
     */
    private String scm;
    /**
     * 是否正价 0=是 1=否
     */
    private String sfzj;
    /**
     * OA流程中城市级别/传递给店仓城市级别
     */
    private String bojuncsjb;

    /**
     * 对接OA直营是否发券
     */
    private String sffq;

    /**
     * 是否奥莱(直营) 0是 1否
     */
    private String sfal;

    /**
     * 地理区域
     */
    private String dlqy1;

    /**
     * NC经销商名称
     */
    private String jxsssgsqc;

    /**
     * 大区经理姓名
     */
    private String dqjlName;

    /**
     * 区域/城市经理姓名
     */
    private String qycsjlName;

    /**
     * 区域/销售主管姓名
     */
    private String qyxszgName;


    @ApiModelProperty(value = "客户id  对应UF_KHID 表主键id  可以通过这个拿到crmId")
    private String khid;

    @ApiModelProperty(value = "大区经理")
    private String dqjl;


    private String zrztxxdz;

    private Integer sfwgs;
}
