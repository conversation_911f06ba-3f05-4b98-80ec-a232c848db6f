package com.jnby.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Data
public class CrmCustomerListReq {

    @ApiModelProperty(value = " 请输入ID、经销客户名称、标签查询  模糊客户基本信息地址查询  模糊查询")
    private String search;

    @ApiModelProperty(value = "省查询")
    private String province;

    @ApiModelProperty(value = "市查询")
    private String city;

    @ApiModelProperty(value = "合作区域")
    private String area;

    @ApiModelProperty(value = "code模糊搜索")
    private String customerCode;

    @ApiModelProperty(value = " 模糊 客户名称搜索")
    private String customerName;

    @ApiModelProperty(value = "合作品牌brandid")
    private String coopBrand;

    private String unionid;

    @ApiModelProperty(value = "" +
            "    public static final Integer INTERVIEW_STATUS = 0 ; // 意向\n" +
            "    public static  final Integer INVESTIGATE_STATUS = 1;  // 考察\n" +
            "    public static  final Integer ACCESS_STATUS = 2;  // 准入\n" +
            "\n" +
            "    public static  final Integer COOPREATE_STATUS = 3; // 合作\n" +
            "    public static  final Integer TERMINATE_STATUS = 4; // 解约\n" +
            "    public static  final Integer OUT_STATUS = 5; // 淘汰")
    private Integer status;

    private String id;

    @ApiModelProperty(value = "考察人 Id")
    private String  investigatePersonId;

    @ApiModelProperty(value = "批量 多个  仅限制  0 1 2 5 状态的  其他两个不限制")
    private List<String> interviewBrandIds;

    @ApiModelProperty(value = "经销商ids")
    private List<String> customerIds;

    String component;
    String requestPath;
    String username;

}
