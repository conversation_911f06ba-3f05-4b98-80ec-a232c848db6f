package com.jnby.dto;

import com.google.common.base.Preconditions;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @description: 后台服务单列表管理请求参数
 * @date 2021/10/27
 */
@Data
public class BoxDetailReq implements Serializable {

    @ApiModelProperty(value = "box服务单的主键id。2选1必填")
    private String boxId;

    @ApiModelProperty(value = "box服务单序号。2选1必填")
    private String boxSn;

    public void check() {
        Preconditions.checkArgument(StringUtils.isNotEmpty(boxId) || StringUtils.isNotEmpty(boxSn), "boxId和boxSn二选一必填");
    }
}
