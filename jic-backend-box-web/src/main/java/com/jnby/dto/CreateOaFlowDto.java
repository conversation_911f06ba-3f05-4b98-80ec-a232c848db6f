package com.jnby.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CreateOaFlowDto {

    @ApiModelProperty(value = "主键  CRM主表主键")
    private String id;

    @ApiModelProperty(value = "当进行准入的时候  oaId必传")
    private String oaId;

    @ApiModelProperty(value = "员工名称+工号")
    private String loginId;

    @ApiModelProperty(value = "传递 code  0 1 2 ")
    private String lclx;

    @ApiModelProperty(value = "传递中文  新建准入主体及建档 已有主体仅建档 现有经销商主体变更")
    private String lclxStr;

}
