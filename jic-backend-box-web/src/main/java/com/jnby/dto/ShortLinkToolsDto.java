package com.jnby.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ShortLinkToolsDto {

    /**
     * 主键
     */
    private String id;

    /**
     * 品牌weid   box定义为  10000  其他则weid
     */
    @ApiModelProperty(value = "品牌weid   box定义为  10000  其他则weid")
    private String weid;

    /**
     * 类型  0  小程序路径   1 小程序带h5链接
     */
    @ApiModelProperty(value = "类型  0  小程序路径   1 小程序带h5链接")
    private String pathType;

    /**
     * 页面名称
     */
    @ApiModelProperty(value = "页面名称")
    private String pageName;

    /**
     * 参数设置   0 应用指定渠道并记录落表   1 无需特殊处理
     */
    @ApiModelProperty(value = "参数设置   0 应用指定渠道并记录落表   1 无需特殊处理")
    private String paramsSettingType = "1";

    /**
     * 参数设置内容  仅在 应用指定渠道并记录落表  存储值
     */
    @ApiModelProperty(value = "参数设置内容  仅在 应用指定渠道并记录落表  存储值")
    private String paramsSettingContent;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 0 未删除  1 已删除
     */
    @TableField(value = "IS_DEL")
    private Integer isDel;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门  不用传递")
    private String sysOrgCode;

    /**
     * 有效类型   0 长期有效  1短期有效
     */
    @ApiModelProperty(value = "有效类型   0 长期有效  1短期有效")
    private Integer effectType;

    /**
     * 有效天数  仅对短期有效有用
     */
    @ApiModelProperty(value = "有效天数  仅对短期有效有用")
    private Integer effectDays;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 小程序路径
     */
    @ApiModelProperty(value = "小程序路径")
    private String path;

    /**
     * 非永久的和永久的都是此字段
     */
    @ApiModelProperty(value = " 短链接 路径 非永久的和永久的都是此字段")
    private String shortLink;

    /**
     * 因为永久的需要重新搞一个短链接，每次不是重新生成， 做一个生成时间
     */
    @ApiModelProperty(value = " 因为永久的需要重新搞一个短链接，每次不是重新生成， 做一个生成时间")
    private String permanentShortLink;

    /**
     *  永久的  生成时间  如果小于等于29天  那么 直接使用 permanentShortLink  如果大于29天  重新生成一下
     */
    @TableField(value = "PERMANENT_DATE")
    @ApiModelProperty(value = "永久的  生成时间  如果小于等于29天  那么 直接使用 permanentShortLink  如果大于29天  重新生成一下  不用传递")
    private Date permanentDate;

    @ApiModelProperty(value = "小程序太阳码")
    private String imgUrl;

    @ApiModelProperty(value = "h5地址")
    private String h5UrlLink;



    @ApiModelProperty(value = "页面类型   0 自研页面  1 saas页面")
    private Integer  pageType;


    @ApiModelProperty(value = "系统加参   -1 代表不加参数   数值代表增加的小时数")
    private String systemAddParam;


    @ApiModelProperty(value = "0 小时   1 天")
    private String systemAddHourOrDay;


    @ApiModelProperty(value = "生成短连接的路径和参数拼接")
    private String createShortLinkPathParam;
}
