package com.jnby.dto.oa;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class GetHtListReqDto {
    /**
     * 流程标题
     */
    @ApiModelProperty(value = "流程标题")
    private String title;




    @ApiModelProperty(value = "customerIdList")
    private List<String> customerIdList;


    @ApiModelProperty(value = "品牌架构id")
    private List<String> arcBrandIdList;


    @ApiModelProperty(value = "类型列表")
    private List<String> typeIdList;


    @ApiModelProperty(value = "状态列表")
    private List<String> currentNodeTypeList;


    @ApiModelProperty(value = "合同开始时间")
    private String ktStartTime;


    @ApiModelProperty(value = "合同结束时间")
    private String ktEndTime;

    /**
     * 0合同管理 1财务管理 2销售管理 3店仓管理 4装修管理  5解约管理 6准入管理
     */
    @ApiModelProperty(value = "0合同管理 1财务管理 2销售管理 3店仓管理 4装修管理  5解约管理 6准入管理")
    private Long tabType;
}
