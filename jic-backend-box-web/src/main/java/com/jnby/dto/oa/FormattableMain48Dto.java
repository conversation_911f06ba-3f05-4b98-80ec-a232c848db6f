package com.jnby.dto.oa;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FormattableMain48Dto {

    @ApiModelProperty(value = "解约时间")
    private String jysj2;

    @ApiModelProperty(value = "伯俊经销商id")
    private Long bjjxsmc;

    @ApiModelProperty(value = " 0 是  1 否   是否转直营" )
    private Long sfzzy;

    @ApiModelProperty(value = "解约原因")
    private String jyyy;

}
