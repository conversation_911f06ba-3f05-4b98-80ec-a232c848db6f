package com.jnby.dto.oa;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 合同dto信息
 */
@Data
public class FormattableMain42Dto {

    /**
     * 合同开始时间 htkrrq
     * 进货返利返利率 jhfll
     * 形象返利返利率	xxfll
     * 货品折扣	hpzk
     * 鞋品折扣	xpzk
     * 退货率	thl
     * 合同期进货指标（元）	htqjhzb
     * 系统使用费	xtsyf
     * 履约保证金（元）	lybzj
     */
    @ApiModelProperty(value = "合同开始时间")
    private String htkrrq;

    @ApiModelProperty(value = "进货返利返利率")
    private String  jhfll;

    @ApiModelProperty(value = "形象返利返利率")
    private String  xxfll;

    @ApiModelProperty(value = "货品折扣")
    private String  hpzk;

    @ApiModelProperty(value = "鞋品折扣")
    private String  xpzk;

    @ApiModelProperty(value = "退货率")
    private String  thl;

    @ApiModelProperty(value = "合同期进货指标（元）")
    private String  htqjhzb;

    @ApiModelProperty(value = "系统使用费")
    private String  xtsyf;

    @ApiModelProperty(value = "履约保证金（元）")
    private String  lybzj;

    @ApiModelProperty(value = "税号")
    private String dfsfzhsh;

    private String jsxbjjd;

    private String id;

    @ApiModelProperty(value = "fy年份")
    private String fy;

    @ApiModelProperty(value = "联系人手机号")
    private String dflxrdh;

    @ApiModelProperty(value = "联系人")
    private String dflxr;

    @ApiModelProperty(value = "公司名称")
    private String hzdwmode;

    @ApiModelProperty(value = "公司地址")
    private String dfdz;


}
