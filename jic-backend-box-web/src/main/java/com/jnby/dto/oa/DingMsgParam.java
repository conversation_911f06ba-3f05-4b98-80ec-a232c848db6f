package com.jnby.dto.oa;

import com.jnby.dto.FlowDataDTO;
import com.jnby.infrastructure.bojun.model.CCustomerVo;
import com.jnby.infrastructure.bojun.model.CStoreVo;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class DingMsgParam {
    private FlowDataDTO flowDataDTO;
    private CCustomerVo cCustomerVo;
    private CCustomerVo customerProp;
    private CStoreVo storeDoc;
    private CStoreVo cStoreProp;
    private String str;
    private String type;
    private CStoreVo right;

    public DingMsgParam(FlowDataDTO flowDataDTO, CCustomerVo cCustomerVo, CCustomerVo customerProp, CStoreVo storeDoc, CStoreVo cStoreProp, String str,
                        String type, CStoreVo right) {
        this.flowDataDTO = flowDataDTO;
        this.cCustomerVo = cCustomerVo;
        this.customerProp = customerProp;
        this.storeDoc = storeDoc;
        this.cStoreProp = cStoreProp;
        this.str = str;
        this.type = type;
        this.right = right;
    }
}
