package com.jnby.dto;

import com.google.common.base.Preconditions;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 * @description: 后台服务单列表管理请求参数
 * @date 2021/10/27
 */
@Data
public class BoxStatusUpdateReq implements Serializable {

    @ApiModelProperty(value = "box服务单的主键id", required = true)
    private String boxId;
    @ApiModelProperty(value = "box服务单的订单状态(-1:待提交;0:待发货;1:发货中;2:已签收;3:待入库;4:已评价,5.已完成 6:已取消,7:系统作废,8:被合单后;9:待还货;10:还货中)", required = true)
    private Integer status;

    @ApiModelProperty(value = "子订单集合")
    private List<BoxDetailStatusUpdateReq> subList;

    public void check() {
        // 主子订单参数都不能为空
        Preconditions.checkArgument(StringUtils.isNotBlank(boxId), "boxId不能为空");
        Preconditions.checkArgument(status != null, "status不能为空");
        Preconditions.checkArgument(subList != null && !subList.isEmpty(), "subList不能为空");
    }

    @Data
    public static class BoxDetailStatusUpdateReq {
        @ApiModelProperty(value = "子订单主键ID", required = true)
        private String boxDetailId;
        @ApiModelProperty(value = "box服务单的订单状态商品状态(0:待发货;1:还货中;2:已还货;3:已购买;4:已处理;5:待退款;6:已退款;7:已取消;8:已发货;9:已签收;10:待还货;11:待入库;12:已删款)", required = true)
        private Integer boxDetailStatus;
    }
}
