package com.jnby.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ShortLinkToolsListReq {

    @ApiModelProperty(value = "小程序orh5路径  模糊")
    private String path;

    @ApiModelProperty(value = "类型  0  小程序路径   1 小程序带h5链接  精准")
    private String pathType;

    @ApiModelProperty(value = "页面名称  模糊 ")
    private String pageName;

    @ApiModelProperty(value = "参数设置   0 应用指定渠道并记录落表   1 无需特殊处理  精准")
    private String paramsSettingType;

    @ApiModelProperty(value = "参数设置内容  仅在 应用指定渠道并记录落表  存储值  模糊")
    private String paramsSettingContent;

    @ApiModelProperty(value = "备注 模糊查询")
    private String remark;

    @ApiModelProperty(value = "品牌weid   box定义为  10000  其他则weid  精准")
    private String weid;
}
