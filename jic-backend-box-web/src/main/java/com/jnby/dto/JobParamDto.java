package com.jnby.dto;

import com.alibaba.fastjson.JSON;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

@Data
public class JobParamDto {
    /**
     * 时间-起
     */
    private Date fromDate;
    /**
     * 时间-止
     */
    private Date toDate;
    /**
     * 是否执行这几个表，如果单独指定某个配置，则其他都不执行，如果不指定该参数，则认为都执行
     */
    private Boolean executeCustomerDetails = true;
    private Boolean executeSubscribeInfo = true;
    private Boolean executeSubscribePlan = true;
    private Boolean executeCustomerAskBox = true;
    private Boolean executeBox = true;
    private Boolean executeOrder = true;



    public static JobParamDto build(String jobParam) {
        if (StringUtils.isBlank(jobParam)) {
            new JobParamDto();
        }
        return JSON.parseObject(jobParam, JobParamDto.class);
    }
}
