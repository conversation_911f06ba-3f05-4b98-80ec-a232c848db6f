package com.jnby.listener.impl;

import com.google.common.collect.Lists;
import com.jnby.listener.IMessageListener;
import com.jnby.listener.db.Table;
import com.jnby.module.member.IMemberDataUploadService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

@Component
@Slf4j
@RefreshScope
public class YuGongConsumerListener implements IMessageListener {

    @Resource
    private IMemberDataUploadService memberDataUploadService;

    @Value("${mq.consumer.yugong.topic}")
    private String topic;
    @Value("${mq.consumer.yugong.tags}")
    private String tags;

    private String BOX_TAG = "BOX";

    private String ORDER_TAG = "ORDER_";

    @Override
    public String getTopic() {
        return topic;
    }

    @Override
    public String getTags() {
        return tags;
    }

    @NewSpan
    @Override
    public ConsumeConcurrentlyStatus consume(Message msg) {
        log.info("consume = topic = {}, tags = {}, keys = {}", topic, msg.getTags(), msg.getKeys());

        if (Objects.equals(BOX_TAG, msg.getTags())) {
            return process(msg, BOX_TAG);
        } else if (Objects.equals(ORDER_TAG, msg.getTags())) {
            return process(msg, BOX_TAG);
        }

        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private ConsumeConcurrentlyStatus process(Message msg, String tag) {
        try {
            String obj = new String(msg.getBody(), "UTF-8");
//            log.info("BOX单上报 收到消息 topic = {}, tags = {}, msg = {}", topic, msg.getTags(), msg.getKeys(), obj);
            Table table = Table.fromJson(obj, Table.class);
            String value = table.getColumnByName("ID").getValue().toString();

            log.info("BOX单上报 解析后的ID:[{}]", value);
            if (value != null) {
                if (Objects.equals(BOX_TAG, tag)) {
                    memberDataUploadService.uploadBoxEvent(Lists.newArrayList(value));
                } else if (Objects.equals(ORDER_TAG, tag)) {
                    memberDataUploadService.uploadOrderEvent(Lists.newArrayList(value));
                }
            }
        } catch (Exception e) {
            log.error("BOX单上报 消息处理异常", e);
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }


}
