package com.jnby.api;
import com.jnby.common.exception.BoxException;
import com.jnby.common.ErrorConstants;
import com.jnby.common.ResponseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 全局异常处理类
 * <AUTHOR>
 * @version 1.0
 * @date 3/10/21 5:38 PM
 */
@ControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    /**
     * 处理 Exception 异常
     *
     * @param httpServletRequest httpServletRequest
     * @param e                  异常
     * @return
     */
    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public ResponseResult exceptionHandler(HttpServletRequest httpServletRequest, Exception e) {
        logger.error("拦截器接收到异常请求 path = {}, message = {}",httpServletRequest.getContextPath(), e.getMessage(), e);
        if (e instanceof BoxException){
            return ResponseResult.error(Integer.valueOf(((BoxException) e).getCode()), e.getMessage());
        }
        return ResponseResult.error(Integer.valueOf(ErrorConstants.PARAMS_ERROR.getCode()), e.getMessage());
    }


    /**
     * Bean 校验异常 Validate
     * @param request
     * @param exception
     * @return
     */
    @ExceptionHandler(value = MethodArgumentNotValidException.class) //400
    @ResponseBody
    public ResponseResult methodArgumentValidationHandler(HttpServletRequest request, MethodArgumentNotValidException exception){
        logger.info("Bean校验异常:" + request.getRequestURI(), exception);
        logger.info("请求校验参数错误！{}",getExceptionDetail(exception),"参数数据："+showParams(request));

        int code = Integer.valueOf(ErrorConstants.PARAMS_ERROR.getCode());
        if (exception.getBindingResult() != null && !CollectionUtils.isEmpty(exception.getBindingResult().getAllErrors())) {
            return ResponseResult.error(code,"请求参数校验错误,"+exception.getBindingResult().getAllErrors().get(0).getDefaultMessage());
        }
        return ResponseResult.error(code,"请求参数校验错误,"+exception.getMessage());
    }


    /**
     * 请求参数
     * @param request
     * @return
     */
    public  String showParams(HttpServletRequest request) {
        Map<String,Object> map = new HashMap<String,Object>();
        StringBuilder stringBuilder=new StringBuilder();
        Enumeration paramNames = request.getParameterNames();
        stringBuilder.append("---------------请求参数开始-------------------");
        stringBuilder.append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        if(Objects.nonNull(paramNames)){
            while (paramNames.hasMoreElements()) {
                String paramName = (String) paramNames.nextElement();
                String[] paramValues = request.getParameterValues(paramName);
                if (paramValues.length >0) {
                    String paramValue = paramValues[0];
                    if (paramValue.length() != 0) {
                        stringBuilder.append("参数名:").append(paramName).append("参数值:").append(paramValue);
                    }
                }
            }
        }
        stringBuilder.append("----------------参数结束-------------------");
        return stringBuilder.toString();
    }

    /**
     * 异常详情
     * @param e
     * @return
     */
    private String getExceptionDetail(Exception e) {
        StringBuilder stringBuffer = new StringBuilder(e.toString() + "\n");
        StackTraceElement[] messages = e.getStackTrace();
        Arrays.stream(messages).filter(Objects::nonNull).forEach(stackTraceElement -> {
            stringBuffer.append(stackTraceElement.toString() + "\n");
        });
        return stringBuffer.toString();
    }
}
