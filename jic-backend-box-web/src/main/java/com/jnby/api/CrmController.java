package com.jnby.api;

import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import com.jnby.common.enums.CrmStatus;
import com.jnby.dto.*;
import com.jnby.dto.bojun.CustomerDto;
import com.jnby.dto.oa.FormattableMain42Dto;
import com.jnby.infrastructure.bojun.model.CArcbrand;
import com.jnby.infrastructure.bojun.model.CAreaVo;
import com.jnby.infrastructure.bojun.model.CAreaVo1;
import com.jnby.infrastructure.bojun.model.CRegions;
import com.jnby.module.crm.CrmService;
import com.jnby.module.crm.IRegionsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/admin/crm")
@Api(value = "crm", tags = "经销商crm系统")
public class CrmController {


    @Autowired
    private CrmService crmService;

    @Autowired
    private IRegionsService regionsService;

    @ApiOperation(value = "创建经销客户主表")
    @RequestMapping(value = "/createOrUpdate",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult createOrUpdate(@RequestBody CommonRequest<CrmCreateDto> request){
        crmService.createOrUpdate(request.getRequestData());
        return ResponseResult.success();
    }


    @ApiOperation(value = "编辑经销客户子表信息")
    @RequestMapping(value = "/updateRelation",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult updateRelation(@RequestBody CommonRequest<List<UpdateCrmRelationDto>> request){
        crmService.updateRelation(request.getRequestData());
        return ResponseResult.success();
    }



    @ApiOperation(value = "根据unionid查询一条数据")
    @RequestMapping(value = "/findByUnionid",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<CrmCreateDto> findByUnionid(@RequestBody CommonRequest<String> request){
        CrmCreateDto crmCreateDto = crmService.findByUnionid(request.getRequestData());
        return ResponseResult.success(crmCreateDto);
    }


    @ApiOperation(value = "获取所有省")
    @RequestMapping(value = "/getProvinces",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<CRegions>> getProvinces(){
        List<CRegions> provinces = regionsService.getProvinces();
        return ResponseResult.success(provinces);
    }


    @ApiOperation(value = "根据省获取城市信息")
    @RequestMapping(value = "/getCities",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<CRegions>> getCities(@RequestBody CommonRequest<GetCityOrDistrict> request){
        List<CRegions> citys = regionsService.getCities(request.getRequestData().getProvinceId());
        return ResponseResult.success(citys);
    }


    @ApiOperation(value = "根据城市获取区域信息")
    @RequestMapping(value = "/getDistricts",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<CRegions>> getDistricts(@RequestBody CommonRequest<GetCityOrDistrict> request){
        List<CRegions> districts = regionsService.getDistricts(request.getRequestData().getCityId());
        return ResponseResult.success(districts);
    }


    @ApiOperation(value = "获取经销区域所有")
    @RequestMapping(value = "/getArea",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<CAreaVo>> getArea(){
        List<CAreaVo> area = crmService.getArea();
        return ResponseResult.success(area);
    }

    @ApiOperation(value = "获取经销区域所有")
    @RequestMapping(value = "/getArea1",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<CAreaVo1>> getArea1(){
        List<CAreaVo1> area = crmService.getArea1();
        return ResponseResult.success(area);
    }



    @ApiOperation(value = "进入考核状态  仅需要传递id")
    @RequestMapping(value = "/intoInvestigateStatus",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult intoInvestigateStatus(@RequestBody CommonRequest<CrmCreateDto> request){
        if(StringUtils.isBlank(request.getRequestData().getInvestigatePerson())){
            throw new RuntimeException("进入考察期必须传递考察人!");
        }
        CrmCreateDto crmCreateDto = new CrmCreateDto();
        crmCreateDto.setId(request.getRequestData().getId());
        crmCreateDto.setStatus(CrmStatus.INVESTIGATE_STATUS);
        crmCreateDto.setInvestigatePerson(request.getRequestData().getInvestigatePerson());
        crmCreateDto.setInvestigatePersonId(request.getRequestData().getInvestigatePersonId());
        crmService.createOrUpdate(crmCreateDto);
        return ResponseResult.success();
    }

    @ApiOperation(value = "进入淘汰状态")
    @RequestMapping(value = "/intoOutStatus",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult intoOutStatus(@RequestBody CommonRequest<String> request){
        CrmCreateDto crmCreateDto = new CrmCreateDto();
        crmCreateDto.setId(request.getRequestData());
        crmCreateDto.setStatus(CrmStatus.OUT_STATUS);
        crmService.createOrUpdate(crmCreateDto);
        return ResponseResult.success();
    }


    @ApiOperation(value = "提交准入   仅需要额外传递 准入需要填写的信息")
    @RequestMapping(value = "/submitAccessStatus",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult submitAccessStatus(@RequestBody CommonRequest<CrmCreateDto> request){
        crmService.createOrUpdate(request.getRequestData());
        return ResponseResult.success();
    }


    @ApiOperation(value = "创建OA流程")
    @RequestMapping(value = "/createOaFlow",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult createOaFlow(@RequestBody CommonRequest<CreateOaFlowDto> request){
        try {
            String oaFlow = crmService.createOaFlow(request.getRequestData());
            return ResponseResult.success(oaFlow);
        }catch (Exception e ){
            return ResponseResult.error(-1,e.getMessage());
        }
    }


    @ApiOperation(value = "变更档案")
    @RequestMapping(value = "/changeCrmCustomerMain",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult changeCrmCustomerMain(@RequestBody CommonRequest<ChangeCrmCustomerMainDto> request){
        crmService.changeCrmCustomerMain(request.getRequestData());
        return ResponseResult.success();
    }


    @ApiOperation(value = "变更下级公司")
    @RequestMapping(value = "/changeCrmRelation",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult changeCrmRelation(@RequestBody CommonRequest<ChangeCrmRelationDto> request){
        crmService.changeCrmRelation(request.getRequestData());
        return ResponseResult.success();
    }


    @ApiOperation(value = "获取当前经销商绑定的所有下级公司")
    @RequestMapping(value = "/getCrmRelation",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<CrmRelationDto>> getCrmRelation(@RequestBody CommonRequest<CrmMainIdDto> request){
        List<CrmRelationDto> list = crmService.getCrmRelation(request.getRequestData().getCrmCustomerMainId());
        return ResponseResult.success(list);
    }



    @ApiOperation(value = "获取可绑定的客户id")
    @RequestMapping(value = "/getCanBindingCustomerMain",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<CrmCreateDto>> getCanBindingCustomerMain(){
        List<CrmCreateDto> list = crmService.getCanBindingCustomerMain();
        return ResponseResult.success(list);
    }



    @ApiOperation(value = "客户列表")
    @RequestMapping(value = "/list",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<CrmCreateDto>> list(@RequestBody CommonRequest<CrmCustomerListReq> request){
        Page page = request.getPage();
        List<CrmCreateDto> list = crmService.list(request.getRequestData(),page,request.getComponent(),request.getUser_id());
        return ResponseResult.success(list,page);
    }


    @ApiOperation(value = "查询各个种类的客户总数")
    @RequestMapping(value = "/listCountStatus",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<ListCountStatusDto> listCountStatus(@RequestBody CommonRequest<ListCountReq> request){
        ListCountStatusDto countStatusDto = crmService.listCountStatus(request.getRequestData(),request.getComponent(),request.getUser_id());
        return ResponseResult.success(countStatusDto);
    }




    @ApiOperation(value = "根据经销商编码批量查询可用经销商信息  (requestDataj内直接传递批量经销商code)")
    @RequestMapping(value = "/findCustomerByCodes",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<FindCustomerListDto>> findCustomerByCodes(@RequestBody CommonRequest<List<String>> request){
        List<FindCustomerListDto> map = crmService.findCustomerByCodes(request.getRequestData(),request.getComponent(),request.getUser_id());
        return ResponseResult.success(map);
    }


    // 获取财务信息   获取合同信息  最新的合同信息即可
    @ApiOperation(value = "根据 伯俊经销商id获取合同信息  最新的 展示出来即可")
    @RequestMapping(value = "/findHtByCustomerIds",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<FormattableMain42Dto>> findHtByCustomerIds(@RequestBody CommonRequest<List<String>> request){
        List<FormattableMain42Dto> formattableMain42Dto = crmService.findHtByCustomerId(request.getRequestData());
        return ResponseResult.success(formattableMain42Dto);
    }


    @ApiOperation(value = "查询代销合同信息")
    @RequestMapping(value = "/findConsSalesByCustomerIds",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<FormattableMain42Dto>> findConsSalesByCustomerIds(@RequestBody CommonRequest<List<String>> request){
        List<FormattableMain42Dto> formattableMain42Dto = crmService.findConsSalesByCustomerIds(request.getRequestData());
        return ResponseResult.success(formattableMain42Dto);
    }


    @ApiOperation(value = "获取品牌信息")
    @RequestMapping(value = "/getBrandInfo",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<CArcbrand>> getBrandInfo(){
        List<CArcbrand> cArcbrands = crmService.getBrandInfo();
        return ResponseResult.success(cArcbrands);
    }


    // 获取经销商店铺 c_store

    @ApiOperation(value = "根据查询条件查询店铺信息")
    @RequestMapping(value = "/getStoreList",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<StoreCustomerResp>> getStoreList(@RequestBody CommonRequest<StoreCustomerReq> request){
        Page page = request.getPage();
        List<StoreCustomerResp> storeCustomerResps = crmService.getStoreList(request.getRequestData(),page);
        return ResponseResult.success(storeCustomerResps,page);
    }




    @ApiOperation(value = "同步解约状态  如果已经全部都已经解约了， 那么就变更为解约状态 每天一次即可")
    @RequestMapping(value = "/syncStopStatus",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<String> syncStopStatus(){
        crmService.syncStopStatus();
        return ResponseResult.success("成功");
    }


    @ApiOperation(value = "创建经销商 ")
    @RequestMapping(value = "/createCustomer",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<Long>> testCreateCustomer(@RequestBody  List<CreateCustomerReq> list  ){
        List<Long> ids = new ArrayList<>();
        for (CreateCustomerReq createCustomerReq : list) {
            Long customerBojun = crmService.createCustomerBojun(createCustomerReq.getRequestId());
            ids.add(customerBojun);
        }
        return ResponseResult.success(ids);
    }



    @ApiOperation(value = "解约合同  解约某个 c_csutomer")
    @RequestMapping(value = "/terminateContract",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<String> terminateContract(@RequestBody  List<CreateCustomerReq> list  ){
        for (CreateCustomerReq createCustomerReq : list) {
            crmService.terminateContract(createCustomerReq.getRequestId());
        }
        return ResponseResult.success("成功");
    }


    //
    @ApiOperation(value = "导入旧的数据")
    @RequestMapping(value = "/importOldData",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult importOldData(@RequestBody CommonRequest<String> request){
        crmService.importOldData(request.getRequestData());
        return ResponseResult.success();
    }


    @ApiOperation(value = "每年刷一次 标签  刷标签")
    @RequestMapping(value = "/flushYearsLabel",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult flushYearsLabel(){
        crmService.flushYearsLabel();
        return ResponseResult.success();
    }




    @ApiOperation(value = "导入excel进行更新数据")
    @RequestMapping(value = "/importCrmFileChangeData",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult importCrmFileChangeData(@RequestBody CommonRequest<String> request){
        if(StringUtils.isBlank(request.getRequestData())){
            return ResponseResult.error(-1,"url地址为空 不处理");
        }
        crmService.importCrmFileChangeData(request.getRequestData());
        return ResponseResult.success();
    }




    // 获取oatoken
    @ApiOperation(value = "获取OAtoken")
    @RequestMapping(value = "/getOaToken",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<String> getOaToken(){
        String token = crmService.getOaToken();
        return ResponseResult.success(token);
    }


}
