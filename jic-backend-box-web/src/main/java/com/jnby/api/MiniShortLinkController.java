package com.jnby.api;

import com.jnby.common.ResponseResult;
import com.jnby.module.shortLink.ShortLinkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;

@Controller
@Api(value = "miniShortLinkController",tags = "小程序")
public class MiniShortLinkController {

    @Autowired
    private ShortLinkService shortLinkService;

    @ApiOperation(value = "生成短链接 ， 生成太阳码")
    @RequestMapping(value = "/mini-shortlink/{id}",method = RequestMethod.GET)
    @ResponseBody
    public ResponseResult miniShortLink(@PathVariable String id) throws IOException, WxErrorException {
        String shortLinkUrl = shortLinkService.miniShortLink(id);
        return ResponseResult.success(shortLinkUrl);
    }


}
