package com.jnby.api;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jnby.common.CommonRequest;
import com.jnby.common.Page;
import com.jnby.common.ResponseResult;
import com.jnby.dto.oa.GetHtListReqDto;
import com.jnby.infrastructure.box.mapper.OaUserWorkflowMapper;
import com.jnby.infrastructure.box.mapper.OaWorkflowMapper;
import com.jnby.infrastructure.box.model.MultiWorkFlowEntity;
import com.jnby.infrastructure.box.model.OaUserWorkflow;
import com.jnby.infrastructure.box.model.WorkFlowModel;
import com.jnby.infrastructure.oa.mapper.WorkBaseMapper;
import com.jnby.infrastructure.oa.model.*;
import com.jnby.module.oa.TypeConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Slf4j
@RestController
@RequestMapping("/admin/flow")
@Api(value = "flow", tags = "流程管理")
public class FlowController {

    @Resource
    private WorkBaseMapper workBaseMapper;


    @Resource
    private OaWorkflowMapper oaWorkflowMapper;

    @Resource
    private OaUserWorkflowMapper oaUserWorkflowMapper;



    @ApiOperation(value = "合同管理列表")
    @RequestMapping(value = "/getHtList",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<WorkFlowModel>> getHtList(@RequestBody CommonRequest<GetHtListReqDto>  request){
        log.info("合同管理列表.req:{}", JSONObject.toJSONString(request));
        GetHtListReqDto getHtListReqDto = request.getRequestData();
        getHtListReqDto.setTabType(0L);
        Page page = request.getPage();
        com.github.pagehelper.Page<WorkFlowModel> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        oaWorkflowMapper.selectCommonList(getHtListReqDto);
        PageInfo<WorkFlowModel> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return ResponseResult.success(pageInfo.getList(), page);
    }




    @ApiOperation(value = "财务管理列表")
    @RequestMapping(value = "/getCwList",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<MultiWorkFlowEntity>> getCwList(@RequestBody CommonRequest<GetHtListReqDto>  request){
        log.info("财务管理列表.req:{}",JSONObject.toJSONString(request));
        GetHtListReqDto getHtListReqDto = request.getRequestData();
        getHtListReqDto.setTabType(1L);
        Page page = request.getPage();
        com.github.pagehelper.Page<WorkFlowModel> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        oaWorkflowMapper.selectCwList(getHtListReqDto);
        PageInfo<WorkFlowModel> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());

        List<MultiWorkFlowEntity> multiWorkFlowEntities = new ArrayList<>();
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return ResponseResult.success(multiWorkFlowEntities, page);
        }

        List<String> requestIds = pageInfo.getList().stream().map(WorkFlowModel::getRequestId).collect(Collectors.toList());
        List<OaUserWorkflow> oaUserWorkflows = oaUserWorkflowMapper.selectByRequestIds(requestIds);
        pageInfo.getList().stream().forEach(e -> {
            MultiWorkFlowEntity multiWorkFlowEntityTemp = new MultiWorkFlowEntity();
            multiWorkFlowEntityTemp.setWorkFlowModel(e);
            multiWorkFlowEntityTemp.setOaUserWorkflows(oaUserWorkflows.stream().filter(x -> x.getRequestId().equals(e.getRequestId())).collect(Collectors.toList()));
            multiWorkFlowEntities.add(multiWorkFlowEntityTemp);
        });
        return ResponseResult.success(multiWorkFlowEntities, page);
    }


    @ApiOperation(value = "销售管理列表")
    @RequestMapping(value = "/getXsList", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult< List<MultiWorkFlowEntity>> getXsList(@RequestBody CommonRequest<GetHtListReqDto> request) {
        log.info("销售管理列表.req:{}", JSONObject.toJSONString(request));
        GetHtListReqDto getHtListReqDto = request.getRequestData();
        getHtListReqDto.setTabType(2L);

        Page page = request.getPage();
        com.github.pagehelper.Page<WorkFlowModel> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        oaWorkflowMapper.selectXsList(getHtListReqDto);
        PageInfo<WorkFlowModel> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());


        List<MultiWorkFlowEntity> multiWorkFlowEntities = new ArrayList<>();
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return ResponseResult.success(multiWorkFlowEntities, page);
        }
        List<OaUserWorkflow>  oaUserWorkflows  =  oaUserWorkflowMapper.selectByRequestIds(pageInfo.getList().stream().map(WorkFlowModel::getRequestId).collect(Collectors.toList()));

        pageInfo.getList().forEach(e->{
            MultiWorkFlowEntity multiWorkFlowEntityTemp = new MultiWorkFlowEntity();
            multiWorkFlowEntityTemp.setWorkFlowModel(e);
            multiWorkFlowEntityTemp.setOaUserWorkflows(oaUserWorkflows.stream().filter(x->x.getRequestId().equals(e.getRequestId())).collect(Collectors.toList()));
            multiWorkFlowEntities.add(multiWorkFlowEntityTemp);
        });
        return ResponseResult.success(multiWorkFlowEntities, page);
    }




    @ApiOperation(value = "装修管理列表")
    @RequestMapping(value = "/getZxList",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<WorkFlowModel>> getZxList(@RequestBody CommonRequest<GetHtListReqDto>  request) {
        log.info("装修管理列表.req:{}", JSONObject.toJSONString(request));
        GetHtListReqDto getHtListReqDto = request.getRequestData();
        getHtListReqDto.setTabType(4L);
        Page page = request.getPage();
        com.github.pagehelper.Page<WorkFlowModel> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        oaWorkflowMapper.selectCommonList(getHtListReqDto);
        PageInfo<WorkFlowModel> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return ResponseResult.success(pageInfo.getList(), page);
    }



    @ApiOperation(value = "解约管理列表")
    @RequestMapping(value = "/getJyList",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<WorkFlowModel>> getJyList(@RequestBody CommonRequest<GetHtListReqDto>  request){
        log.info("解约列表.req:{}",JSONObject.toJSONString(request));
        GetHtListReqDto getHtListReqDto = request.getRequestData();
        getHtListReqDto.setTabType(5L);
        Page page = request.getPage();
        com.github.pagehelper.Page<WorkFlowModel> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        oaWorkflowMapper.selectCommonList(getHtListReqDto);
        PageInfo<WorkFlowModel> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return ResponseResult.success(pageInfo.getList(), page);
    }




    @ApiOperation(value = "店仓管理列表")
    @RequestMapping(value = "/getDcList",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<WorkFlowModel>> getDcList(@RequestBody CommonRequest<GetHtListReqDto>  request) {
        log.info("店仓管理列表.req:{}",JSONObject.toJSONString(request));
        GetHtListReqDto getHtListReqDto = request.getRequestData();
        getHtListReqDto.setTabType(3L);
        Page page = request.getPage();
        com.github.pagehelper.Page<WorkFlowModel> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        oaWorkflowMapper.selectCommonList(getHtListReqDto);
        PageInfo<WorkFlowModel> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return ResponseResult.success(pageInfo.getList(), page);
    }



    @ApiOperation(value = "准入管理列表")
    @RequestMapping(value = "/getZrList",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<WorkFlowModel>> getZrList(@RequestBody CommonRequest<GetHtListReqDto>  request) {
        log.info("店仓管理列表.req:{}",JSONObject.toJSONString(request));
        GetHtListReqDto getHtListReqDto = request.getRequestData();
        getHtListReqDto.setTabType(6L);
        Page page = request.getPage();
        com.github.pagehelper.Page<WorkFlowModel> hPage = PageHelper.startPage(page.getPageNo(), page.getPageSize());
        oaWorkflowMapper.selectCommonList(getHtListReqDto);
        PageInfo<WorkFlowModel> pageInfo = new PageInfo<>(hPage);
        page.setCount(hPage.getTotal());
        page.setPages(pageInfo.getPages());
        return ResponseResult.success(pageInfo.getList(), page);
    }



    @ApiOperation(value = "合同管理类型")
    @RequestMapping(value = "/getHtType", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<TypeEntity>> getHtType() {
        return ResponseResult.success(getNowTypeList(TypeConstant.HT.getFromIdList()));
    }


    @ApiOperation(value = "解约管理类型")
    @RequestMapping(value = "/getJyType", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<TypeEntity>> getJyType() {
        return ResponseResult.success(getNowTypeList(TypeConstant.JY.getFromIdList()));
    }

    @ApiOperation(value = "店仓管理类型")
    @RequestMapping(value = "/getDcType", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<TypeEntity>> getDcType() {
        return ResponseResult.success(getNowTypeList(TypeConstant.DC.getFromIdList()));
    }


    @ApiOperation(value = "装修管理类型")
    @RequestMapping(value = "/getZxType", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<TypeEntity>> getZxType() {
        return ResponseResult.success(getNowTypeList(TypeConstant.ZX.getFromIdList()));
    }

    @ApiOperation(value = "财务管理类型")
    @RequestMapping(value = "/getCwType", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<TypeEntity>> getCwType() {
        return ResponseResult.success(getNowTypeList(TypeConstant.CW.getFromIdList()));
    }


    @ApiOperation(value = "销售管理类型")
    @RequestMapping(value = "/getXsType", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<TypeEntity>> getXsType() {
        return ResponseResult.success(getNowTypeList(TypeConstant.XS.getFromIdList()));
    }

    @ApiOperation(value = "准入管理类型")
    @RequestMapping(value = "/getZrType", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<TypeEntity>> getZrType() {
        return ResponseResult.success(getNowTypeList(TypeConstant.ZR.getFromIdList()));
    }

    public List<TypeEntity> getNowTypeList(List<String> formIds){
        List<TypeEntity> typeEntities = new ArrayList<>();
        if(CollectionUtils.isEmpty(formIds)){
            return typeEntities;
        }
        List<WorkflowBase> workflowBases = workBaseMapper.getWorkFlowBaseListByFormIds(formIds);
        if (CollectionUtils.isEmpty(workflowBases)) {
            return typeEntities;
        }
        Map<String, List<WorkflowBase>> map = workflowBases.stream().collect(Collectors.groupingBy(WorkflowBase::getWorkFlowName));

        map.keySet().forEach(e->{
            TypeEntity typeEntityTemp = new TypeEntity();
            typeEntityTemp.setWorkFlowName(e);
            typeEntityTemp.setIdList(map.get(e).stream().map(WorkflowBase::getId).collect(Collectors.toList()));
            typeEntities.add(typeEntityTemp);
        });
        return typeEntities;
    }
}
