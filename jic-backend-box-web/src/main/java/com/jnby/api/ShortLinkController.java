package com.jnby.api;

import com.alibaba.fastjson.JSONObject;
import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import com.jnby.dto.GenShortLinkReq;
import com.jnby.dto.ShortLinkToolsDto;
import com.jnby.dto.ShortLinkToolsListReq;
import com.jnby.infrastructure.box.model.ShortLinkTools;
import com.jnby.module.shortLink.ShortLinkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

@Controller
@RequestMapping("/admin/shortLink")
@Api(value = "shortLinkController",tags = "短链接工具")
@Slf4j
public class ShortLinkController {

    @Autowired
    private ShortLinkService shortLinkService;


    @ApiOperation(value = "创建短链接数据")
    @RequestMapping(value = "/create",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<String> create(@RequestBody CommonRequest<ShortLinkToolsDto> request) throws IOException, WxErrorException {
        log.info("create = {}",JSONObject.toJSONString(request));
        String id = shortLinkService.create(request.getRequestData(),request.getUser_id());
        shortLinkService.genShortLinkById(id);
        return ResponseResult.success(id);
    }


    @ApiOperation(value = "查询列表")
    @RequestMapping(value = "/list",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<List<ShortLinkToolsDto>> list(@RequestBody CommonRequest<ShortLinkToolsListReq> request) throws IOException, WxErrorException {
        List<ShortLinkToolsDto> list = shortLinkService.list(request.getRequestData(),request.getPage());
        return ResponseResult.success(list,request.getPage());
    }


    @ApiOperation(value = "查询详情  直接requestData里面传递id即可")
    @RequestMapping(value = "/details",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<ShortLinkToolsDto> details(@RequestBody CommonRequest<String> request) throws IOException, WxErrorException {
        ShortLinkToolsDto result = shortLinkService.details(request.getRequestData());
        return ResponseResult.success(result);
    }




    @ApiOperation(value = "生成短链接 ， 生成太阳码")
    @RequestMapping(value = "/genShortLinkById",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult genShortLinkById(@RequestBody JSONObject jsonObject) throws IOException, WxErrorException {
        log.info("genShortLinkById = {}",JSONObject.toJSONString(jsonObject));
        shortLinkService.genShortLinkById(jsonObject.getString("id"));
        return ResponseResult.success();
    }


    @ApiOperation(value = "启用 禁用  ")
    @RequestMapping(value = "/changeStatus",method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult changeStatus(@RequestBody CommonRequest<String> request) throws IOException, WxErrorException {
        shortLinkService.changeStatus(request.getRequestData(),1);
        return ResponseResult.success();
    }


}
