package com.jnby.api;

import com.jnby.common.ResponseResult;
import com.jnby.job.member.OaPullFlowJob;
import com.jnby.module.oa.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RequestMapping("/flow/test")
@RestController
@Api(value = "流程测试接口", tags = "流程测试接口")
public class FlowTestController {
    @Resource
    private IPullHtDataService pullHtDataService;

    @Resource
    private PullZrDataServiceImpl pullZrDataService;

    @Resource
    private IPullJyDataService iPullJyDataService;

    @Resource
    private IPullDcDataService iPullDcDataService;

    @Resource
    private IPullZxDataService iPullZxDataService;

    @Resource
    private IPullXsDataService iPullXsDataService;

    @Resource
    private IPullCwDataService iPullCwDataService;


    @Resource
    private ICommonRequestBase iCommonRequestBase;


    @Resource
    private OaPullFlowJob oaPullFlowJob;


    @ResponseBody
    @GetMapping("/pullHtData")
    @ApiOperation(value = "全部合同数据拉取")
    public ResponseResult pullHtData() {
        pullHtDataService.handleHtData();
        return ResponseResult.success();
    }

    @ResponseBody
    @GetMapping("/pullZrData")
    @ApiOperation(value = "全部准入数据拉取")
    public ResponseResult pullZrData() {
        pullZrDataService.handleData();
        return ResponseResult.success();
    }

    @ResponseBody
    @GetMapping("/pullJyData")
    @ApiOperation(value = "全部解约数据拉取")
    public ResponseResult pullJyData() {
        iPullJyDataService.handleData();
        return ResponseResult.success();
    }



    @ResponseBody
    @GetMapping("/pullDcData")
    @ApiOperation(value = "全部店仓数据拉取")
    public ResponseResult pullDcData() {
        iPullDcDataService.handleData();
        return ResponseResult.success();
    }







    @ResponseBody
    @GetMapping("/pullZxData")
    @ApiOperation(value = "全部装修数据拉取")
    public ResponseResult pullZxData() {
        iPullZxDataService.handleData();
        return ResponseResult.success();
    }

    @ResponseBody
    @GetMapping("/pullXsData")
    @ApiOperation(value = "全部销售数据拉取")
    public ResponseResult pullXsData() {
        iPullXsDataService.handleData();
        return ResponseResult.success();
    }

    @ResponseBody
    @GetMapping("/pullCwData")
    @ApiOperation(value = "全部财务数据拉取")
    public ResponseResult pullCwData() {
        iPullCwDataService.handleData();
        return ResponseResult.success();
    }




    @ResponseBody
    @GetMapping("/pullHtDataByRequestId/{requestId}")
    @ApiOperation(value = "合同指定数据拉取")
    public ResponseResult pullHtDataByRequestId(@PathVariable String requestId) {
        pullHtDataService.handleHtData(requestId);
        return ResponseResult.success();
    }

    @ResponseBody
    @GetMapping("/pullZrDataByRequestId/{requestId}")
    @ApiOperation(value = "准入指定数据拉取")
    public ResponseResult pullZrDataByRequestId(@PathVariable String requestId) {
        pullZrDataService.handleData(requestId);
        return ResponseResult.success();
    }





    @ResponseBody
    @GetMapping("/pullJyDataByRequestId/{requestId}")
    @ApiOperation(value = "解约指定数据拉取")
    public ResponseResult pullJyDataByRequestId(@PathVariable String requestId) {
        iPullJyDataService.handleData(requestId);
        return ResponseResult.success();
    }




    @ResponseBody
    @GetMapping("/pullDcDataByRequestId/{requestId}")
    @ApiOperation(value = "店仓指定数据拉取")
    public ResponseResult pullDcDataByRequestId(@PathVariable String requestId) {
        iPullDcDataService.handleData(requestId);
        return ResponseResult.success();
    }




    @ResponseBody
    @GetMapping("/pullZxDataByRequestId/{requestId}")
    @ApiOperation(value = "装修指定数据拉取")
    public ResponseResult pullZxDataByRequestId(@PathVariable String requestId) {
        iPullZxDataService.handleData(requestId);
        return ResponseResult.success();
    }




    @ResponseBody
    @GetMapping("/pullXsDataByRequestId/{requestId}")
    @ApiOperation(value = "销售指定数据拉取")
    public ResponseResult pullXsDataByRequestId(@PathVariable String requestId) {
        iPullXsDataService.handleData(requestId);
        return ResponseResult.success();
    }




    @ResponseBody
    @GetMapping("/pullCwDataByRequestId/{requestId}")
    @ApiOperation(value = "财务指定数据拉取")
    public ResponseResult pullCwDataByRequestId(@PathVariable String requestId) {
        iPullCwDataService.handleData(requestId);
        return ResponseResult.success();
    }




    @ResponseBody
    @GetMapping("/pullDataByRequestId/{requestId}")
    @ApiOperation(value = "特殊-指定数据拉取")
    public ResponseResult pullDataByRequestId(@PathVariable String requestId) {
        iCommonRequestBase.handleRequestBase(requestId);
        return ResponseResult.success();
    }


    @ResponseBody
    @GetMapping("/pullAllData")
    @ApiOperation(value = "特殊-所有数据拉取（有排序 准入-解约-店仓-装修-销售-财务-合同）")
    public ResponseResult pullAllData() {
        // 准入
        pullZrDataService.handleData();
        // 解约
        iPullJyDataService.handleData();
        // 店仓
        iPullDcDataService.handleData();
        // 装修
        iPullZxDataService.handleData();
        // 销售
        iPullXsDataService.handleData();
        // 财务
        iPullCwDataService.handleData();
        // 合同
        pullHtDataService.handleHtData();
        return ResponseResult.success();
    }





    @ResponseBody
    @GetMapping("/testJob")
    @ApiOperation(value = "特殊-测试job 拉取两天以内的数据")
    public ResponseResult handle2Days() throws Exception {
        oaPullFlowJob.execute();
        return ResponseResult.success();
    }

}
