package com.jnby.api;

import cn.hutool.core.lang.Pair;
import com.jnby.common.ResponseResult;
import com.jnby.dto.JobParamDto;
import com.jnby.module.member.IMemberDataUploadService;
import com.xxl.job.core.context.XxlJobHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

@RequestMapping("/test")
@RestController
@Api(value = "测试接口", tags = "测试接口")
public class TestController {

    @Autowired
    private IMemberDataUploadService memberDataUploadService;

    @ResponseBody
    @GetMapping("/incrementalUpload")
    @ApiOperation(value = "增量上传")
    public ResponseResult incrementalUpload(String jobParam) {
        List<Pair<Date, Date>> pointDatePairList = Lists.newArrayList();
        Date toDate = new Date();
        Date fromDate = com.xxl.job.core.util.DateUtil.addDays(toDate, -1);
        Pair<Date, Date> pair = new Pair<>(fromDate, toDate);
        pointDatePairList.add(pair);
        JobParamDto jobParamDto = new JobParamDto();
        if (StringUtils.isNotBlank(jobParam)) {
            jobParamDto = JobParamDto.build(jobParam);

            Date paramFromDate = jobParamDto.getFromDate();
            Date paramToDate = jobParamDto.getToDate();
            if (paramFromDate == null || paramToDate == null) {
                return ResponseResult.error(555, "日期范围指定时 fromDate 和 toDate 都需要有值。");
            }
            // 按照日期切割
            pointDatePairList.clear();
            while (paramFromDate.before(paramToDate) || paramFromDate.equals(paramToDate)) {
                Date oneDayAfter = com.xxl.job.core.util.DateUtil.addDays(paramFromDate, 1);
                Pair<Date, Date> paramPair = new Pair<>(paramFromDate, oneDayAfter);
                pointDatePairList.add(paramPair);
                paramFromDate = oneDayAfter;
            }
        }
        for (Pair<Date, Date> datePair : pointDatePairList) {
            XxlJobHelper.log("执行前的最终入参,fromDate[{}],toDate[{}]", datePair.getKey(), datePair.getValue());
            memberDataUploadService.incrementalUpload(datePair.getKey(), datePair.getValue(), jobParamDto);
        }
        return ResponseResult.success();
    }

    @ResponseBody
    @GetMapping("/uploadBoxEvent")
    @ApiOperation(value = "上报box实时事件")
    public ResponseResult uploadBoxEvent(String id) {
        memberDataUploadService.uploadBoxEvent(id);
        return ResponseResult.success();
    }

    @ResponseBody
    @GetMapping("/uploadOrderEvent")
    @ApiOperation(value = "上报order实时事件")
    public ResponseResult uploadOrderEvent(String id) {
        memberDataUploadService.uploadOrderEvent(id);
        return ResponseResult.success();
    }
}
