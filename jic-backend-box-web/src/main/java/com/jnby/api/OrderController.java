package com.jnby.api;

import com.jnby.common.CommonRequest;
import com.jnby.common.ResponseResult;
import com.jnby.common.enums.BoxDetailsStatusEnum;
import com.jnby.common.enums.BoxStatusEnum;
import com.jnby.dto.BoxDetailReq;
import com.jnby.dto.BoxStatusUpdateReq;
import com.jnby.dto.GetProductInfoByBoxIdResp;
import com.jnby.dto.UpdateBoxStatusReq;
import com.jnby.module.order.IOrderService;
import com.jnbyframework.boot.api.ISysBaseAPI;
import com.jnbyframework.boot.common.system.vo.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/admin/orderForSet")
@Api(value = "order", tags = "后台服务单管理")
public class OrderController {

    @Autowired
    private IOrderService orderService;

    @Resource
    private ISysBaseAPI sysBaseAPI;

    @ApiOperation(value = "box服务单详情查询商品信息")
    @RequestMapping(value = "/getProductInfo", method = RequestMethod.POST)
    @ResponseBody
    public ResponseResult<GetProductInfoByBoxIdResp> getProductInfo(@RequestBody CommonRequest<BoxDetailReq> request) {
        request.getRequestData().check();
        return ResponseResult.success( orderService.getProductInfoByBoxId(request.getRequestData()));
    }

    @ApiOperation(value = "更新box单和子单状态")
    @PostMapping(value = "/updateBoxStatusAndDetailStatus")
    @ResponseBody
    public ResponseResult updateBoxStatusAndDetailStatus(@RequestBody CommonRequest<BoxStatusUpdateReq> request) {
        request.getRequestData().check();
        BoxStatusEnum.getEnumByCode(request.getRequestData().getStatus());
        orderService.updateBoxStatusAndDetailStatus(request.getRequestData());
        return ResponseResult.success();
    }

    @ApiOperation(value = "更新box单状态")
    @PostMapping(value = "/updateBoxStatus")
    @ResponseBody
    public ResponseResult updateBoxStatus(@RequestBody CommonRequest<UpdateBoxStatusReq> request) {
        BoxStatusEnum.getEnumByCode(request.getRequestData().getAfterStatus().intValue());
        BoxStatusEnum.getEnumByCode(request.getRequestData().getBeforeStatus().intValue());
        log.info("updateBoxStatus更新box主单状态, boxSn = {}, beforeStatus = {}, afterStatus = {}, username = {}", request.getRequestData().getBoxSn(), request.getRequestData().getBeforeStatus(), request.getRequestData().getAfterStatus(), getUserInfo());
        orderService.updateBoxStatus(request.getRequestData().getBoxSn(), request.getRequestData().getBeforeStatus(), request.getRequestData().getAfterStatus());
        return ResponseResult.success();
    }

    private String getUserInfo() {
        try {
            LoginUser loginUser = sysBaseAPI.getCurrentLoginUserInfo();
            return loginUser.getRealname();
        }catch (Exception e){
            log.error("获取当前登录用户信息失败 e = {}", e);
        }
        return null;
    }
}
