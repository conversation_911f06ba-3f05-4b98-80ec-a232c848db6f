apiVersion: apps/v1
kind: Deployment
metadata:
  name: jic-backend-box-web
  labels:
    app: jic-backend-box-web
spec:
  replicas: 1
  template:
    metadata:
      name: jic-backend-box-web
      labels:
        app: jic-backend-box-web
    spec:
      containers:
        - name: jic-backend-box-web
          image: "harbor.jnby.com/jic-backend-box/jic-backend-box-web:latest"
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 9101
              name: jic-box-back
              protocol: TCP
          lifecycle:
            preStop:
              exec:
                command:
                  - sh
                  - c
                  - "sleep 5"
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 20
            successThreshold: 1
            tcpSocket:
              port: 9101
            timeoutSeconds: 1
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 60
            periodSeconds: 20
            successThreshold: 1
            tcpSocket:
              port: 9101
            timeoutSeconds: 1
      restartPolicy: Always
      imagePullSecrets:
        - name: 152harbor

  selector:
    matchLabels:
      app: jic-backend-box-web

