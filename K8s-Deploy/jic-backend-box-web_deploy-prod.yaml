apiVersion: apps/v1
kind: Deployment
metadata:
  name: jic-backend-box-web-prod
  namespace: box
  labels:
    web: jic-backend-box-web-prod
spec:
  replicas: {api-replicas}
  selector:
    matchLabels:
      web: jic-backend-box-web-prod
  template:
    metadata:
      labels:
        web: jic-backend-box-web-prod
    spec:
      containers:
      - name: jic-backend-box-web-prod
        image: jnbyharbor.jnby.com/box-group/jic-backend-box/jic-backend-box-web:latest
        imagePullPolicy: "Always"
        ports:
        - containerPort: 9101
        env:
        - name: profiles
          value: prod
        lifecycle:
          preStop:
            exec:
              command:
                - sh
                - c
                - "sleep 5"
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 60
          periodSeconds: 20
          successThreshold: 1
          tcpSocket:
            port: 9101
          timeoutSeconds: 1
        readinessProbe:
          failureThreshold: 3
          initialDelaySeconds: 60
          periodSeconds: 20
          successThreshold: 1
          tcpSocket:
            port: 9101
          timeoutSeconds: 1
      restartPolicy: Always
      imagePullSecrets:
      - name: box-group

---
apiVersion: v1
kind: Service
metadata:
  name: jic-backend-box-web-prod
  namespace: box
  labels:
    web: jic-backend-box-web-prod
spec:
  type: ClusterIP
  ports:
    - port: 9101
  selector:
    web: jic-backend-box-web-prod

